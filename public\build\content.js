var __getOwnPropNames = Object.getOwnPropertyNames;
var __commonJS = (cb, mod) => function __require() {
  return mod || (0, cb[__getOwnPropNames(cb)[0]])((mod = { exports: {} }).exports, mod), mod.exports;
};

// src/ui/listen/audioCore/aec.js
var require_aec = __commonJS({
  "src/ui/listen/audioCore/aec.js"(exports, module) {
    var createAecModule = (() => {
      var _scriptName = typeof document != "undefined" ? document.currentScript?.src : void 0;
      return async function(moduleArg = {}) {
        var moduleRtn;
        var Module = moduleArg;
        var ENVIRONMENT_IS_WEB = typeof window == "object";
        var ENVIRONMENT_IS_WORKER = typeof WorkerGlobalScope != "undefined";
        var ENVIRONMENT_IS_NODE = typeof process == "object" && process.versions?.node && process.type != "renderer";
        var arguments_ = [];
        var thisProgram = "./this.program";
        var quit_ = (status, toThrow) => {
          throw toThrow;
        };
        if (ENVIRONMENT_IS_WORKER) {
          _scriptName = self.location.href;
        }
        var scriptDirectory = "";
        var readAsync, readBinary;
        if (ENVIRONMENT_IS_WEB || ENVIRONMENT_IS_WORKER) {
          try {
            scriptDirectory = new URL(".", _scriptName).href;
          } catch {
          }
          {
            if (ENVIRONMENT_IS_WORKER) {
              readBinary = (url) => {
                var xhr = new XMLHttpRequest();
                xhr.open("GET", url, false);
                xhr.responseType = "arraybuffer";
                xhr.send(null);
                return new Uint8Array(xhr.response);
              };
            }
            readAsync = async (url) => {
              var response = await fetch(url, { credentials: "same-origin" });
              if (response.ok) {
                return response.arrayBuffer();
              }
              throw new Error(response.status + " : " + response.url);
            };
          }
        } else {
        }
        var out = console.log.bind(console);
        var err = console.error.bind(console);
        var wasmBinary;
        var ABORT = false;
        var EXITSTATUS;
        var readyPromiseResolve, readyPromiseReject;
        var wasmMemory;
        var HEAP8, HEAPU8, HEAP16, HEAPU16, HEAP32, HEAPU32, HEAPF32, HEAPF64;
        var HEAP64, HEAPU64;
        var runtimeInitialized = false;
        function updateMemoryViews() {
          var b2 = wasmMemory.buffer;
          HEAP8 = new Int8Array(b2);
          Module["HEAP16"] = HEAP16 = new Int16Array(b2);
          Module["HEAPU8"] = HEAPU8 = new Uint8Array(b2);
          HEAPU16 = new Uint16Array(b2);
          HEAP32 = new Int32Array(b2);
          HEAPU32 = new Uint32Array(b2);
          HEAPF32 = new Float32Array(b2);
          HEAPF64 = new Float64Array(b2);
          HEAP64 = new BigInt64Array(b2);
          HEAPU64 = new BigUint64Array(b2);
        }
        function preRun() {
          if (Module["preRun"]) {
            if (typeof Module["preRun"] == "function") Module["preRun"] = [Module["preRun"]];
            while (Module["preRun"].length) {
              addOnPreRun(Module["preRun"].shift());
            }
          }
          callRuntimeCallbacks(onPreRuns);
        }
        function initRuntime() {
          runtimeInitialized = true;
          if (!Module["noFSInit"] && !FS.initialized) FS.init();
          TTY.init();
          wasmExports["v"]();
          FS.ignorePermissions = false;
        }
        function postRun() {
          if (Module["postRun"]) {
            if (typeof Module["postRun"] == "function") Module["postRun"] = [Module["postRun"]];
            while (Module["postRun"].length) {
              addOnPostRun(Module["postRun"].shift());
            }
          }
          callRuntimeCallbacks(onPostRuns);
        }
        var runDependencies = 0;
        var dependenciesFulfilled = null;
        function addRunDependency(id) {
          runDependencies++;
          Module["monitorRunDependencies"]?.(runDependencies);
        }
        function removeRunDependency(id) {
          runDependencies--;
          Module["monitorRunDependencies"]?.(runDependencies);
          if (runDependencies == 0) {
            if (dependenciesFulfilled) {
              var callback = dependenciesFulfilled;
              dependenciesFulfilled = null;
              callback();
            }
          }
        }
        function abort(what) {
          Module["onAbort"]?.(what);
          what = "Aborted(" + what + ")";
          err(what);
          ABORT = true;
          what += ". Build with -sASSERTIONS for more info.";
          var e2 = new WebAssembly.RuntimeError(what);
          readyPromiseReject?.(e2);
          throw e2;
        }
        var wasmBinaryFile;
        function findWasmBinary() {
          return base64Decode("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");
        }
        function getBinarySync(file) {
          if (ArrayBuffer.isView(file)) {
            return file;
          }
          if (file == wasmBinaryFile && wasmBinary) {
            return new Uint8Array(wasmBinary);
          }
          if (readBinary) {
            return readBinary(file);
          }
          throw "both async and sync fetching of the wasm failed";
        }
        async function getWasmBinary(binaryFile) {
          return getBinarySync(binaryFile);
        }
        async function instantiateArrayBuffer(binaryFile, imports) {
          try {
            var binary = await getWasmBinary(binaryFile);
            var instance = await WebAssembly.instantiate(binary, imports);
            return instance;
          } catch (reason) {
            err(`failed to asynchronously prepare wasm: ${reason}`);
            abort(reason);
          }
        }
        async function instantiateAsync(binary, binaryFile, imports) {
          return instantiateArrayBuffer(binaryFile, imports);
        }
        function getWasmImports() {
          return { a: wasmImports };
        }
        async function createWasm() {
          function receiveInstance(instance, module2) {
            wasmExports = instance.exports;
            wasmMemory = wasmExports["u"];
            updateMemoryViews();
            wasmTable = wasmExports["H"];
            assignWasmExports(wasmExports);
            removeRunDependency("wasm-instantiate");
            return wasmExports;
          }
          addRunDependency("wasm-instantiate");
          function receiveInstantiationResult(result2) {
            return receiveInstance(result2["instance"]);
          }
          var info = getWasmImports();
          if (Module["instantiateWasm"]) {
            return new Promise((resolve, reject) => {
              Module["instantiateWasm"](info, (mod, inst) => {
                resolve(receiveInstance(mod, inst));
              });
            });
          }
          wasmBinaryFile ??= findWasmBinary();
          var result = await instantiateAsync(wasmBinary, wasmBinaryFile, info);
          var exports2 = receiveInstantiationResult(result);
          return exports2;
        }
        class ExitStatus {
          name = "ExitStatus";
          constructor(status) {
            this.message = `Program terminated with exit(${status})`;
            this.status = status;
          }
        }
        var callRuntimeCallbacks = (callbacks) => {
          while (callbacks.length > 0) {
            callbacks.shift()(Module);
          }
        };
        var onPostRuns = [];
        var addOnPostRun = (cb) => onPostRuns.push(cb);
        var onPreRuns = [];
        var addOnPreRun = (cb) => onPreRuns.push(cb);
        var base64Decode = (b64) => {
          var b1, b2, i3 = 0, j2 = 0, bLength = b64.length;
          var output = new Uint8Array((bLength * 3 >> 2) - (b64[bLength - 2] == "=") - (b64[bLength - 1] == "="));
          for (; i3 < bLength; i3 += 4, j2 += 3) {
            b1 = base64ReverseLookup[b64.charCodeAt(i3 + 1)];
            b2 = base64ReverseLookup[b64.charCodeAt(i3 + 2)];
            output[j2] = base64ReverseLookup[b64.charCodeAt(i3)] << 2 | b1 >> 4;
            output[j2 + 1] = b1 << 4 | b2 >> 2;
            output[j2 + 2] = b2 << 6 | base64ReverseLookup[b64.charCodeAt(i3 + 3)];
          }
          return output;
        };
        var noExitRuntime = true;
        var stackRestore = (val) => __emscripten_stack_restore(val);
        var stackSave = () => _emscripten_stack_get_current();
        var exceptionLast = 0;
        class ExceptionInfo {
          constructor(excPtr) {
            this.excPtr = excPtr;
            this.ptr = excPtr - 24;
          }
          set_type(type) {
            HEAPU32[this.ptr + 4 >> 2] = type;
          }
          get_type() {
            return HEAPU32[this.ptr + 4 >> 2];
          }
          set_destructor(destructor) {
            HEAPU32[this.ptr + 8 >> 2] = destructor;
          }
          get_destructor() {
            return HEAPU32[this.ptr + 8 >> 2];
          }
          set_caught(caught) {
            caught = caught ? 1 : 0;
            HEAP8[this.ptr + 12] = caught;
          }
          get_caught() {
            return HEAP8[this.ptr + 12] != 0;
          }
          set_rethrown(rethrown) {
            rethrown = rethrown ? 1 : 0;
            HEAP8[this.ptr + 13] = rethrown;
          }
          get_rethrown() {
            return HEAP8[this.ptr + 13] != 0;
          }
          init(type, destructor) {
            this.set_adjusted_ptr(0);
            this.set_type(type);
            this.set_destructor(destructor);
          }
          set_adjusted_ptr(adjustedPtr) {
            HEAPU32[this.ptr + 16 >> 2] = adjustedPtr;
          }
          get_adjusted_ptr() {
            return HEAPU32[this.ptr + 16 >> 2];
          }
        }
        var setTempRet0 = (val) => __emscripten_tempret_set(val);
        var findMatchingCatch = (args) => {
          var thrown = exceptionLast;
          if (!thrown) {
            setTempRet0(0);
            return 0;
          }
          var info = new ExceptionInfo(thrown);
          info.set_adjusted_ptr(thrown);
          var thrownType = info.get_type();
          if (!thrownType) {
            setTempRet0(0);
            return thrown;
          }
          for (var caughtType of args) {
            if (caughtType === 0 || caughtType === thrownType) {
              break;
            }
            var adjusted_ptr_addr = info.ptr + 16;
            if (___cxa_can_catch(caughtType, thrownType, adjusted_ptr_addr)) {
              setTempRet0(caughtType);
              return thrown;
            }
          }
          setTempRet0(thrownType);
          return thrown;
        };
        var ___cxa_find_matching_catch_2 = () => findMatchingCatch([]);
        var uncaughtExceptionCount = 0;
        var ___cxa_throw = (ptr, type, destructor) => {
          var info = new ExceptionInfo(ptr);
          info.init(type, destructor);
          exceptionLast = ptr;
          uncaughtExceptionCount++;
          throw exceptionLast;
        };
        var ___resumeException = (ptr) => {
          if (!exceptionLast) {
            exceptionLast = ptr;
          }
          throw exceptionLast;
        };
        var lengthBytesUTF8 = (str) => {
          var len = 0;
          for (var i3 = 0; i3 < str.length; ++i3) {
            var c2 = str.charCodeAt(i3);
            if (c2 <= 127) {
              len++;
            } else if (c2 <= 2047) {
              len += 2;
            } else if (c2 >= 55296 && c2 <= 57343) {
              len += 4;
              ++i3;
            } else {
              len += 3;
            }
          }
          return len;
        };
        var stringToUTF8Array = (str, heap, outIdx, maxBytesToWrite) => {
          if (!(maxBytesToWrite > 0)) return 0;
          var startIdx = outIdx;
          var endIdx = outIdx + maxBytesToWrite - 1;
          for (var i3 = 0; i3 < str.length; ++i3) {
            var u2 = str.codePointAt(i3);
            if (u2 <= 127) {
              if (outIdx >= endIdx) break;
              heap[outIdx++] = u2;
            } else if (u2 <= 2047) {
              if (outIdx + 1 >= endIdx) break;
              heap[outIdx++] = 192 | u2 >> 6;
              heap[outIdx++] = 128 | u2 & 63;
            } else if (u2 <= 65535) {
              if (outIdx + 2 >= endIdx) break;
              heap[outIdx++] = 224 | u2 >> 12;
              heap[outIdx++] = 128 | u2 >> 6 & 63;
              heap[outIdx++] = 128 | u2 & 63;
            } else {
              if (outIdx + 3 >= endIdx) break;
              heap[outIdx++] = 240 | u2 >> 18;
              heap[outIdx++] = 128 | u2 >> 12 & 63;
              heap[outIdx++] = 128 | u2 >> 6 & 63;
              heap[outIdx++] = 128 | u2 & 63;
              i3++;
            }
          }
          heap[outIdx] = 0;
          return outIdx - startIdx;
        };
        var stringToUTF8 = (str, outPtr, maxBytesToWrite) => stringToUTF8Array(str, HEAPU8, outPtr, maxBytesToWrite);
        function ___syscall_getcwd(buf, size) {
          try {
            if (size === 0) return -28;
            var cwd = FS.cwd();
            var cwdLengthInBytes = lengthBytesUTF8(cwd) + 1;
            if (size < cwdLengthInBytes) return -68;
            stringToUTF8(cwd, buf, size);
            return cwdLengthInBytes;
          } catch (e2) {
            if (typeof FS == "undefined" || !(e2.name === "ErrnoError")) throw e2;
            return -e2.errno;
          }
        }
        var __abort_js = () => abort("");
        var getHeapMax = () => 2147483648;
        var alignMemory = (size, alignment) => Math.ceil(size / alignment) * alignment;
        var growMemory = (size) => {
          var b2 = wasmMemory.buffer;
          var pages = (size - b2.byteLength + 65535) / 65536 | 0;
          try {
            wasmMemory.grow(pages);
            updateMemoryViews();
            return 1;
          } catch (e2) {
          }
        };
        var _emscripten_resize_heap = (requestedSize) => {
          var oldSize = HEAPU8.length;
          requestedSize >>>= 0;
          var maxHeapSize = getHeapMax();
          if (requestedSize > maxHeapSize) {
            return false;
          }
          for (var cutDown = 1; cutDown <= 4; cutDown *= 2) {
            var overGrownHeapSize = oldSize * (1 + 0.2 / cutDown);
            overGrownHeapSize = Math.min(overGrownHeapSize, requestedSize + 100663296);
            var newSize = Math.min(maxHeapSize, alignMemory(Math.max(requestedSize, overGrownHeapSize), 65536));
            var replacement = growMemory(newSize);
            if (replacement) {
              return true;
            }
          }
          return false;
        };
        var ENV = {};
        var getExecutableName = () => thisProgram || "./this.program";
        var getEnvStrings = () => {
          if (!getEnvStrings.strings) {
            var lang = (typeof navigator == "object" && navigator.language || "C").replace("-", "_") + ".UTF-8";
            var env = { USER: "web_user", LOGNAME: "web_user", PATH: "/", PWD: "/", HOME: "/home/<USER>", LANG: lang, _: getExecutableName() };
            for (var x2 in ENV) {
              if (ENV[x2] === void 0) delete env[x2];
              else env[x2] = ENV[x2];
            }
            var strings = [];
            for (var x2 in env) {
              strings.push(`${x2}=${env[x2]}`);
            }
            getEnvStrings.strings = strings;
          }
          return getEnvStrings.strings;
        };
        var _environ_get = (__environ, environ_buf) => {
          var bufSize = 0;
          var envp = 0;
          for (var string of getEnvStrings()) {
            var ptr = environ_buf + bufSize;
            HEAPU32[__environ + envp >> 2] = ptr;
            bufSize += stringToUTF8(string, ptr, Infinity) + 1;
            envp += 4;
          }
          return 0;
        };
        var _environ_sizes_get = (penviron_count, penviron_buf_size) => {
          var strings = getEnvStrings();
          HEAPU32[penviron_count >> 2] = strings.length;
          var bufSize = 0;
          for (var string of strings) {
            bufSize += lengthBytesUTF8(string) + 1;
          }
          HEAPU32[penviron_buf_size >> 2] = bufSize;
          return 0;
        };
        var runtimeKeepaliveCounter = 0;
        var keepRuntimeAlive = () => noExitRuntime || runtimeKeepaliveCounter > 0;
        var _proc_exit = (code) => {
          EXITSTATUS = code;
          if (!keepRuntimeAlive()) {
            Module["onExit"]?.(code);
            ABORT = true;
          }
          quit_(code, new ExitStatus(code));
        };
        var exitJS = (status, implicit) => {
          EXITSTATUS = status;
          _proc_exit(status);
        };
        var _exit = exitJS;
        var PATH = { isAbs: (path) => path.charAt(0) === "/", splitPath: (filename) => {
          var splitPathRe = /^(\/?|)([\s\S]*?)((?:\.{1,2}|[^\/]+?|)(\.[^.\/]*|))(?:[\/]*)$/;
          return splitPathRe.exec(filename).slice(1);
        }, normalizeArray: (parts, allowAboveRoot) => {
          var up = 0;
          for (var i3 = parts.length - 1; i3 >= 0; i3--) {
            var last = parts[i3];
            if (last === ".") {
              parts.splice(i3, 1);
            } else if (last === "..") {
              parts.splice(i3, 1);
              up++;
            } else if (up) {
              parts.splice(i3, 1);
              up--;
            }
          }
          if (allowAboveRoot) {
            for (; up; up--) {
              parts.unshift("..");
            }
          }
          return parts;
        }, normalize: (path) => {
          var isAbsolute = PATH.isAbs(path), trailingSlash = path.slice(-1) === "/";
          path = PATH.normalizeArray(path.split("/").filter((p2) => !!p2), !isAbsolute).join("/");
          if (!path && !isAbsolute) {
            path = ".";
          }
          if (path && trailingSlash) {
            path += "/";
          }
          return (isAbsolute ? "/" : "") + path;
        }, dirname: (path) => {
          var result = PATH.splitPath(path), root = result[0], dir = result[1];
          if (!root && !dir) {
            return ".";
          }
          if (dir) {
            dir = dir.slice(0, -1);
          }
          return root + dir;
        }, basename: (path) => path && path.match(/([^\/]+|\/)\/*$/)[1], join: (...paths) => PATH.normalize(paths.join("/")), join2: (l2, r2) => PATH.normalize(l2 + "/" + r2) };
        var initRandomFill = () => (view) => crypto.getRandomValues(view);
        var randomFill = (view) => {
          (randomFill = initRandomFill())(view);
        };
        var PATH_FS = { resolve: (...args) => {
          var resolvedPath = "", resolvedAbsolute = false;
          for (var i3 = args.length - 1; i3 >= -1 && !resolvedAbsolute; i3--) {
            var path = i3 >= 0 ? args[i3] : FS.cwd();
            if (typeof path != "string") {
              throw new TypeError("Arguments to path.resolve must be strings");
            } else if (!path) {
              return "";
            }
            resolvedPath = path + "/" + resolvedPath;
            resolvedAbsolute = PATH.isAbs(path);
          }
          resolvedPath = PATH.normalizeArray(resolvedPath.split("/").filter((p2) => !!p2), !resolvedAbsolute).join("/");
          return (resolvedAbsolute ? "/" : "") + resolvedPath || ".";
        }, relative: (from, to) => {
          from = PATH_FS.resolve(from).slice(1);
          to = PATH_FS.resolve(to).slice(1);
          function trim(arr) {
            var start = 0;
            for (; start < arr.length; start++) {
              if (arr[start] !== "") break;
            }
            var end = arr.length - 1;
            for (; end >= 0; end--) {
              if (arr[end] !== "") break;
            }
            if (start > end) return [];
            return arr.slice(start, end - start + 1);
          }
          var fromParts = trim(from.split("/"));
          var toParts = trim(to.split("/"));
          var length = Math.min(fromParts.length, toParts.length);
          var samePartsLength = length;
          for (var i3 = 0; i3 < length; i3++) {
            if (fromParts[i3] !== toParts[i3]) {
              samePartsLength = i3;
              break;
            }
          }
          var outputParts = [];
          for (var i3 = samePartsLength; i3 < fromParts.length; i3++) {
            outputParts.push("..");
          }
          outputParts = outputParts.concat(toParts.slice(samePartsLength));
          return outputParts.join("/");
        } };
        var UTF8Decoder = typeof TextDecoder != "undefined" ? new TextDecoder() : void 0;
        var UTF8ArrayToString = (heapOrArray, idx = 0, maxBytesToRead = NaN) => {
          var endIdx = idx + maxBytesToRead;
          var endPtr = idx;
          while (heapOrArray[endPtr] && !(endPtr >= endIdx)) ++endPtr;
          if (endPtr - idx > 16 && heapOrArray.buffer && UTF8Decoder) {
            return UTF8Decoder.decode(heapOrArray.subarray(idx, endPtr));
          }
          var str = "";
          while (idx < endPtr) {
            var u0 = heapOrArray[idx++];
            if (!(u0 & 128)) {
              str += String.fromCharCode(u0);
              continue;
            }
            var u1 = heapOrArray[idx++] & 63;
            if ((u0 & 224) == 192) {
              str += String.fromCharCode((u0 & 31) << 6 | u1);
              continue;
            }
            var u2 = heapOrArray[idx++] & 63;
            if ((u0 & 240) == 224) {
              u0 = (u0 & 15) << 12 | u1 << 6 | u2;
            } else {
              u0 = (u0 & 7) << 18 | u1 << 12 | u2 << 6 | heapOrArray[idx++] & 63;
            }
            if (u0 < 65536) {
              str += String.fromCharCode(u0);
            } else {
              var ch = u0 - 65536;
              str += String.fromCharCode(55296 | ch >> 10, 56320 | ch & 1023);
            }
          }
          return str;
        };
        var FS_stdin_getChar_buffer = [];
        var intArrayFromString = (stringy, dontAddNull, length) => {
          var len = length > 0 ? length : lengthBytesUTF8(stringy) + 1;
          var u8array = new Array(len);
          var numBytesWritten = stringToUTF8Array(stringy, u8array, 0, u8array.length);
          if (dontAddNull) u8array.length = numBytesWritten;
          return u8array;
        };
        var FS_stdin_getChar = () => {
          if (!FS_stdin_getChar_buffer.length) {
            var result = null;
            if (typeof window != "undefined" && typeof window.prompt == "function") {
              result = window.prompt("Input: ");
              if (result !== null) {
                result += "\n";
              }
            } else {
            }
            if (!result) {
              return null;
            }
            FS_stdin_getChar_buffer = intArrayFromString(result, true);
          }
          return FS_stdin_getChar_buffer.shift();
        };
        var TTY = { ttys: [], init() {
        }, shutdown() {
        }, register(dev, ops) {
          TTY.ttys[dev] = { input: [], output: [], ops };
          FS.registerDevice(dev, TTY.stream_ops);
        }, stream_ops: { open(stream) {
          var tty = TTY.ttys[stream.node.rdev];
          if (!tty) {
            throw new FS.ErrnoError(43);
          }
          stream.tty = tty;
          stream.seekable = false;
        }, close(stream) {
          stream.tty.ops.fsync(stream.tty);
        }, fsync(stream) {
          stream.tty.ops.fsync(stream.tty);
        }, read(stream, buffer, offset, length, pos) {
          if (!stream.tty || !stream.tty.ops.get_char) {
            throw new FS.ErrnoError(60);
          }
          var bytesRead = 0;
          for (var i3 = 0; i3 < length; i3++) {
            var result;
            try {
              result = stream.tty.ops.get_char(stream.tty);
            } catch (e2) {
              throw new FS.ErrnoError(29);
            }
            if (result === void 0 && bytesRead === 0) {
              throw new FS.ErrnoError(6);
            }
            if (result === null || result === void 0) break;
            bytesRead++;
            buffer[offset + i3] = result;
          }
          if (bytesRead) {
            stream.node.atime = Date.now();
          }
          return bytesRead;
        }, write(stream, buffer, offset, length, pos) {
          if (!stream.tty || !stream.tty.ops.put_char) {
            throw new FS.ErrnoError(60);
          }
          try {
            for (var i3 = 0; i3 < length; i3++) {
              stream.tty.ops.put_char(stream.tty, buffer[offset + i3]);
            }
          } catch (e2) {
            throw new FS.ErrnoError(29);
          }
          if (length) {
            stream.node.mtime = stream.node.ctime = Date.now();
          }
          return i3;
        } }, default_tty_ops: { get_char(tty) {
          return FS_stdin_getChar();
        }, put_char(tty, val) {
          if (val === null || val === 10) {
            out(UTF8ArrayToString(tty.output));
            tty.output = [];
          } else {
            if (val != 0) tty.output.push(val);
          }
        }, fsync(tty) {
          if (tty.output?.length > 0) {
            out(UTF8ArrayToString(tty.output));
            tty.output = [];
          }
        }, ioctl_tcgets(tty) {
          return { c_iflag: 25856, c_oflag: 5, c_cflag: 191, c_lflag: 35387, c_cc: [3, 28, 127, 21, 4, 0, 1, 0, 17, 19, 26, 0, 18, 15, 23, 22, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0] };
        }, ioctl_tcsets(tty, optional_actions, data) {
          return 0;
        }, ioctl_tiocgwinsz(tty) {
          return [24, 80];
        } }, default_tty1_ops: { put_char(tty, val) {
          if (val === null || val === 10) {
            err(UTF8ArrayToString(tty.output));
            tty.output = [];
          } else {
            if (val != 0) tty.output.push(val);
          }
        }, fsync(tty) {
          if (tty.output?.length > 0) {
            err(UTF8ArrayToString(tty.output));
            tty.output = [];
          }
        } } };
        var mmapAlloc = (size) => {
          abort();
        };
        var MEMFS = { ops_table: null, mount(mount) {
          return MEMFS.createNode(null, "/", 16895, 0);
        }, createNode(parent, name, mode, dev) {
          if (FS.isBlkdev(mode) || FS.isFIFO(mode)) {
            throw new FS.ErrnoError(63);
          }
          MEMFS.ops_table ||= { dir: { node: { getattr: MEMFS.node_ops.getattr, setattr: MEMFS.node_ops.setattr, lookup: MEMFS.node_ops.lookup, mknod: MEMFS.node_ops.mknod, rename: MEMFS.node_ops.rename, unlink: MEMFS.node_ops.unlink, rmdir: MEMFS.node_ops.rmdir, readdir: MEMFS.node_ops.readdir, symlink: MEMFS.node_ops.symlink }, stream: { llseek: MEMFS.stream_ops.llseek } }, file: { node: { getattr: MEMFS.node_ops.getattr, setattr: MEMFS.node_ops.setattr }, stream: { llseek: MEMFS.stream_ops.llseek, read: MEMFS.stream_ops.read, write: MEMFS.stream_ops.write, mmap: MEMFS.stream_ops.mmap, msync: MEMFS.stream_ops.msync } }, link: { node: { getattr: MEMFS.node_ops.getattr, setattr: MEMFS.node_ops.setattr, readlink: MEMFS.node_ops.readlink }, stream: {} }, chrdev: { node: { getattr: MEMFS.node_ops.getattr, setattr: MEMFS.node_ops.setattr }, stream: FS.chrdev_stream_ops } };
          var node = FS.createNode(parent, name, mode, dev);
          if (FS.isDir(node.mode)) {
            node.node_ops = MEMFS.ops_table.dir.node;
            node.stream_ops = MEMFS.ops_table.dir.stream;
            node.contents = {};
          } else if (FS.isFile(node.mode)) {
            node.node_ops = MEMFS.ops_table.file.node;
            node.stream_ops = MEMFS.ops_table.file.stream;
            node.usedBytes = 0;
            node.contents = null;
          } else if (FS.isLink(node.mode)) {
            node.node_ops = MEMFS.ops_table.link.node;
            node.stream_ops = MEMFS.ops_table.link.stream;
          } else if (FS.isChrdev(node.mode)) {
            node.node_ops = MEMFS.ops_table.chrdev.node;
            node.stream_ops = MEMFS.ops_table.chrdev.stream;
          }
          node.atime = node.mtime = node.ctime = Date.now();
          if (parent) {
            parent.contents[name] = node;
            parent.atime = parent.mtime = parent.ctime = node.atime;
          }
          return node;
        }, getFileDataAsTypedArray(node) {
          if (!node.contents) return new Uint8Array(0);
          if (node.contents.subarray) return node.contents.subarray(0, node.usedBytes);
          return new Uint8Array(node.contents);
        }, expandFileStorage(node, newCapacity) {
          var prevCapacity = node.contents ? node.contents.length : 0;
          if (prevCapacity >= newCapacity) return;
          var CAPACITY_DOUBLING_MAX = 1024 * 1024;
          newCapacity = Math.max(newCapacity, prevCapacity * (prevCapacity < CAPACITY_DOUBLING_MAX ? 2 : 1.125) >>> 0);
          if (prevCapacity != 0) newCapacity = Math.max(newCapacity, 256);
          var oldContents = node.contents;
          node.contents = new Uint8Array(newCapacity);
          if (node.usedBytes > 0) node.contents.set(oldContents.subarray(0, node.usedBytes), 0);
        }, resizeFileStorage(node, newSize) {
          if (node.usedBytes == newSize) return;
          if (newSize == 0) {
            node.contents = null;
            node.usedBytes = 0;
          } else {
            var oldContents = node.contents;
            node.contents = new Uint8Array(newSize);
            if (oldContents) {
              node.contents.set(oldContents.subarray(0, Math.min(newSize, node.usedBytes)));
            }
            node.usedBytes = newSize;
          }
        }, node_ops: { getattr(node) {
          var attr = {};
          attr.dev = FS.isChrdev(node.mode) ? node.id : 1;
          attr.ino = node.id;
          attr.mode = node.mode;
          attr.nlink = 1;
          attr.uid = 0;
          attr.gid = 0;
          attr.rdev = node.rdev;
          if (FS.isDir(node.mode)) {
            attr.size = 4096;
          } else if (FS.isFile(node.mode)) {
            attr.size = node.usedBytes;
          } else if (FS.isLink(node.mode)) {
            attr.size = node.link.length;
          } else {
            attr.size = 0;
          }
          attr.atime = new Date(node.atime);
          attr.mtime = new Date(node.mtime);
          attr.ctime = new Date(node.ctime);
          attr.blksize = 4096;
          attr.blocks = Math.ceil(attr.size / attr.blksize);
          return attr;
        }, setattr(node, attr) {
          for (const key of ["mode", "atime", "mtime", "ctime"]) {
            if (attr[key] != null) {
              node[key] = attr[key];
            }
          }
          if (attr.size !== void 0) {
            MEMFS.resizeFileStorage(node, attr.size);
          }
        }, lookup(parent, name) {
          throw MEMFS.doesNotExistError;
        }, mknod(parent, name, mode, dev) {
          return MEMFS.createNode(parent, name, mode, dev);
        }, rename(old_node, new_dir, new_name) {
          var new_node;
          try {
            new_node = FS.lookupNode(new_dir, new_name);
          } catch (e2) {
          }
          if (new_node) {
            if (FS.isDir(old_node.mode)) {
              for (var i3 in new_node.contents) {
                throw new FS.ErrnoError(55);
              }
            }
            FS.hashRemoveNode(new_node);
          }
          delete old_node.parent.contents[old_node.name];
          new_dir.contents[new_name] = old_node;
          old_node.name = new_name;
          new_dir.ctime = new_dir.mtime = old_node.parent.ctime = old_node.parent.mtime = Date.now();
        }, unlink(parent, name) {
          delete parent.contents[name];
          parent.ctime = parent.mtime = Date.now();
        }, rmdir(parent, name) {
          var node = FS.lookupNode(parent, name);
          for (var i3 in node.contents) {
            throw new FS.ErrnoError(55);
          }
          delete parent.contents[name];
          parent.ctime = parent.mtime = Date.now();
        }, readdir(node) {
          return [".", "..", ...Object.keys(node.contents)];
        }, symlink(parent, newname, oldpath) {
          var node = MEMFS.createNode(parent, newname, 511 | 40960, 0);
          node.link = oldpath;
          return node;
        }, readlink(node) {
          if (!FS.isLink(node.mode)) {
            throw new FS.ErrnoError(28);
          }
          return node.link;
        } }, stream_ops: { read(stream, buffer, offset, length, position) {
          var contents = stream.node.contents;
          if (position >= stream.node.usedBytes) return 0;
          var size = Math.min(stream.node.usedBytes - position, length);
          if (size > 8 && contents.subarray) {
            buffer.set(contents.subarray(position, position + size), offset);
          } else {
            for (var i3 = 0; i3 < size; i3++) buffer[offset + i3] = contents[position + i3];
          }
          return size;
        }, write(stream, buffer, offset, length, position, canOwn) {
          if (buffer.buffer === HEAP8.buffer) {
            canOwn = false;
          }
          if (!length) return 0;
          var node = stream.node;
          node.mtime = node.ctime = Date.now();
          if (buffer.subarray && (!node.contents || node.contents.subarray)) {
            if (canOwn) {
              node.contents = buffer.subarray(offset, offset + length);
              node.usedBytes = length;
              return length;
            } else if (node.usedBytes === 0 && position === 0) {
              node.contents = buffer.slice(offset, offset + length);
              node.usedBytes = length;
              return length;
            } else if (position + length <= node.usedBytes) {
              node.contents.set(buffer.subarray(offset, offset + length), position);
              return length;
            }
          }
          MEMFS.expandFileStorage(node, position + length);
          if (node.contents.subarray && buffer.subarray) {
            node.contents.set(buffer.subarray(offset, offset + length), position);
          } else {
            for (var i3 = 0; i3 < length; i3++) {
              node.contents[position + i3] = buffer[offset + i3];
            }
          }
          node.usedBytes = Math.max(node.usedBytes, position + length);
          return length;
        }, llseek(stream, offset, whence) {
          var position = offset;
          if (whence === 1) {
            position += stream.position;
          } else if (whence === 2) {
            if (FS.isFile(stream.node.mode)) {
              position += stream.node.usedBytes;
            }
          }
          if (position < 0) {
            throw new FS.ErrnoError(28);
          }
          return position;
        }, mmap(stream, length, position, prot, flags) {
          if (!FS.isFile(stream.node.mode)) {
            throw new FS.ErrnoError(43);
          }
          var ptr;
          var allocated;
          var contents = stream.node.contents;
          if (!(flags & 2) && contents && contents.buffer === HEAP8.buffer) {
            allocated = false;
            ptr = contents.byteOffset;
          } else {
            allocated = true;
            ptr = mmapAlloc(length);
            if (!ptr) {
              throw new FS.ErrnoError(48);
            }
            if (contents) {
              if (position > 0 || position + length < contents.length) {
                if (contents.subarray) {
                  contents = contents.subarray(position, position + length);
                } else {
                  contents = Array.prototype.slice.call(contents, position, position + length);
                }
              }
              HEAP8.set(contents, ptr);
            }
          }
          return { ptr, allocated };
        }, msync(stream, buffer, offset, length, mmapFlags) {
          MEMFS.stream_ops.write(stream, buffer, 0, length, offset, false);
          return 0;
        } } };
        var asyncLoad = async (url) => {
          var arrayBuffer = await readAsync(url);
          return new Uint8Array(arrayBuffer);
        };
        var FS_createDataFile = (...args) => FS.createDataFile(...args);
        var getUniqueRunDependency = (id) => id;
        var preloadPlugins = [];
        var FS_handledByPreloadPlugin = (byteArray, fullname, finish, onerror) => {
          if (typeof Browser != "undefined") Browser.init();
          var handled = false;
          preloadPlugins.forEach((plugin) => {
            if (handled) return;
            if (plugin["canHandle"](fullname)) {
              plugin["handle"](byteArray, fullname, finish, onerror);
              handled = true;
            }
          });
          return handled;
        };
        var FS_createPreloadedFile = (parent, name, url, canRead, canWrite, onload, onerror, dontCreateFile, canOwn, preFinish) => {
          var fullname = name ? PATH_FS.resolve(PATH.join2(parent, name)) : parent;
          var dep = getUniqueRunDependency(`cp ${fullname}`);
          function processData(byteArray) {
            function finish(byteArray2) {
              preFinish?.();
              if (!dontCreateFile) {
                FS_createDataFile(parent, name, byteArray2, canRead, canWrite, canOwn);
              }
              onload?.();
              removeRunDependency(dep);
            }
            if (FS_handledByPreloadPlugin(byteArray, fullname, finish, () => {
              onerror?.();
              removeRunDependency(dep);
            })) {
              return;
            }
            finish(byteArray);
          }
          addRunDependency(dep);
          if (typeof url == "string") {
            asyncLoad(url).then(processData, onerror);
          } else {
            processData(url);
          }
        };
        var FS_modeStringToFlags = (str) => {
          var flagModes = { r: 0, "r+": 2, w: 512 | 64 | 1, "w+": 512 | 64 | 2, a: 1024 | 64 | 1, "a+": 1024 | 64 | 2 };
          var flags = flagModes[str];
          if (typeof flags == "undefined") {
            throw new Error(`Unknown file open mode: ${str}`);
          }
          return flags;
        };
        var FS_getMode = (canRead, canWrite) => {
          var mode = 0;
          if (canRead) mode |= 292 | 73;
          if (canWrite) mode |= 146;
          return mode;
        };
        var FS = { root: null, mounts: [], devices: {}, streams: [], nextInode: 1, nameTable: null, currentPath: "/", initialized: false, ignorePermissions: true, filesystems: null, syncFSRequests: 0, readFiles: {}, ErrnoError: class {
          name = "ErrnoError";
          constructor(errno) {
            this.errno = errno;
          }
        }, FSStream: class {
          shared = {};
          get object() {
            return this.node;
          }
          set object(val) {
            this.node = val;
          }
          get isRead() {
            return (this.flags & 2097155) !== 1;
          }
          get isWrite() {
            return (this.flags & 2097155) !== 0;
          }
          get isAppend() {
            return this.flags & 1024;
          }
          get flags() {
            return this.shared.flags;
          }
          set flags(val) {
            this.shared.flags = val;
          }
          get position() {
            return this.shared.position;
          }
          set position(val) {
            this.shared.position = val;
          }
        }, FSNode: class {
          node_ops = {};
          stream_ops = {};
          readMode = 292 | 73;
          writeMode = 146;
          mounted = null;
          constructor(parent, name, mode, rdev) {
            if (!parent) {
              parent = this;
            }
            this.parent = parent;
            this.mount = parent.mount;
            this.id = FS.nextInode++;
            this.name = name;
            this.mode = mode;
            this.rdev = rdev;
            this.atime = this.mtime = this.ctime = Date.now();
          }
          get read() {
            return (this.mode & this.readMode) === this.readMode;
          }
          set read(val) {
            val ? this.mode |= this.readMode : this.mode &= ~this.readMode;
          }
          get write() {
            return (this.mode & this.writeMode) === this.writeMode;
          }
          set write(val) {
            val ? this.mode |= this.writeMode : this.mode &= ~this.writeMode;
          }
          get isFolder() {
            return FS.isDir(this.mode);
          }
          get isDevice() {
            return FS.isChrdev(this.mode);
          }
        }, lookupPath(path, opts = {}) {
          if (!path) {
            throw new FS.ErrnoError(44);
          }
          opts.follow_mount ??= true;
          if (!PATH.isAbs(path)) {
            path = FS.cwd() + "/" + path;
          }
          linkloop: for (var nlinks = 0; nlinks < 40; nlinks++) {
            var parts = path.split("/").filter((p2) => !!p2);
            var current = FS.root;
            var current_path = "/";
            for (var i3 = 0; i3 < parts.length; i3++) {
              var islast = i3 === parts.length - 1;
              if (islast && opts.parent) {
                break;
              }
              if (parts[i3] === ".") {
                continue;
              }
              if (parts[i3] === "..") {
                current_path = PATH.dirname(current_path);
                if (FS.isRoot(current)) {
                  path = current_path + "/" + parts.slice(i3 + 1).join("/");
                  continue linkloop;
                } else {
                  current = current.parent;
                }
                continue;
              }
              current_path = PATH.join2(current_path, parts[i3]);
              try {
                current = FS.lookupNode(current, parts[i3]);
              } catch (e2) {
                if (e2?.errno === 44 && islast && opts.noent_okay) {
                  return { path: current_path };
                }
                throw e2;
              }
              if (FS.isMountpoint(current) && (!islast || opts.follow_mount)) {
                current = current.mounted.root;
              }
              if (FS.isLink(current.mode) && (!islast || opts.follow)) {
                if (!current.node_ops.readlink) {
                  throw new FS.ErrnoError(52);
                }
                var link = current.node_ops.readlink(current);
                if (!PATH.isAbs(link)) {
                  link = PATH.dirname(current_path) + "/" + link;
                }
                path = link + "/" + parts.slice(i3 + 1).join("/");
                continue linkloop;
              }
            }
            return { path: current_path, node: current };
          }
          throw new FS.ErrnoError(32);
        }, getPath(node) {
          var path;
          while (true) {
            if (FS.isRoot(node)) {
              var mount = node.mount.mountpoint;
              if (!path) return mount;
              return mount[mount.length - 1] !== "/" ? `${mount}/${path}` : mount + path;
            }
            path = path ? `${node.name}/${path}` : node.name;
            node = node.parent;
          }
        }, hashName(parentid, name) {
          var hash = 0;
          for (var i3 = 0; i3 < name.length; i3++) {
            hash = (hash << 5) - hash + name.charCodeAt(i3) | 0;
          }
          return (parentid + hash >>> 0) % FS.nameTable.length;
        }, hashAddNode(node) {
          var hash = FS.hashName(node.parent.id, node.name);
          node.name_next = FS.nameTable[hash];
          FS.nameTable[hash] = node;
        }, hashRemoveNode(node) {
          var hash = FS.hashName(node.parent.id, node.name);
          if (FS.nameTable[hash] === node) {
            FS.nameTable[hash] = node.name_next;
          } else {
            var current = FS.nameTable[hash];
            while (current) {
              if (current.name_next === node) {
                current.name_next = node.name_next;
                break;
              }
              current = current.name_next;
            }
          }
        }, lookupNode(parent, name) {
          var errCode = FS.mayLookup(parent);
          if (errCode) {
            throw new FS.ErrnoError(errCode);
          }
          var hash = FS.hashName(parent.id, name);
          for (var node = FS.nameTable[hash]; node; node = node.name_next) {
            var nodeName = node.name;
            if (node.parent.id === parent.id && nodeName === name) {
              return node;
            }
          }
          return FS.lookup(parent, name);
        }, createNode(parent, name, mode, rdev) {
          var node = new FS.FSNode(parent, name, mode, rdev);
          FS.hashAddNode(node);
          return node;
        }, destroyNode(node) {
          FS.hashRemoveNode(node);
        }, isRoot(node) {
          return node === node.parent;
        }, isMountpoint(node) {
          return !!node.mounted;
        }, isFile(mode) {
          return (mode & 61440) === 32768;
        }, isDir(mode) {
          return (mode & 61440) === 16384;
        }, isLink(mode) {
          return (mode & 61440) === 40960;
        }, isChrdev(mode) {
          return (mode & 61440) === 8192;
        }, isBlkdev(mode) {
          return (mode & 61440) === 24576;
        }, isFIFO(mode) {
          return (mode & 61440) === 4096;
        }, isSocket(mode) {
          return (mode & 49152) === 49152;
        }, flagsToPermissionString(flag) {
          var perms = ["r", "w", "rw"][flag & 3];
          if (flag & 512) {
            perms += "w";
          }
          return perms;
        }, nodePermissions(node, perms) {
          if (FS.ignorePermissions) {
            return 0;
          }
          if (perms.includes("r") && !(node.mode & 292)) {
            return 2;
          } else if (perms.includes("w") && !(node.mode & 146)) {
            return 2;
          } else if (perms.includes("x") && !(node.mode & 73)) {
            return 2;
          }
          return 0;
        }, mayLookup(dir) {
          if (!FS.isDir(dir.mode)) return 54;
          var errCode = FS.nodePermissions(dir, "x");
          if (errCode) return errCode;
          if (!dir.node_ops.lookup) return 2;
          return 0;
        }, mayCreate(dir, name) {
          if (!FS.isDir(dir.mode)) {
            return 54;
          }
          try {
            var node = FS.lookupNode(dir, name);
            return 20;
          } catch (e2) {
          }
          return FS.nodePermissions(dir, "wx");
        }, mayDelete(dir, name, isdir) {
          var node;
          try {
            node = FS.lookupNode(dir, name);
          } catch (e2) {
            return e2.errno;
          }
          var errCode = FS.nodePermissions(dir, "wx");
          if (errCode) {
            return errCode;
          }
          if (isdir) {
            if (!FS.isDir(node.mode)) {
              return 54;
            }
            if (FS.isRoot(node) || FS.getPath(node) === FS.cwd()) {
              return 10;
            }
          } else {
            if (FS.isDir(node.mode)) {
              return 31;
            }
          }
          return 0;
        }, mayOpen(node, flags) {
          if (!node) {
            return 44;
          }
          if (FS.isLink(node.mode)) {
            return 32;
          } else if (FS.isDir(node.mode)) {
            if (FS.flagsToPermissionString(flags) !== "r" || flags & (512 | 64)) {
              return 31;
            }
          }
          return FS.nodePermissions(node, FS.flagsToPermissionString(flags));
        }, checkOpExists(op, err2) {
          if (!op) {
            throw new FS.ErrnoError(err2);
          }
          return op;
        }, MAX_OPEN_FDS: 4096, nextfd() {
          for (var fd = 0; fd <= FS.MAX_OPEN_FDS; fd++) {
            if (!FS.streams[fd]) {
              return fd;
            }
          }
          throw new FS.ErrnoError(33);
        }, getStreamChecked(fd) {
          var stream = FS.getStream(fd);
          if (!stream) {
            throw new FS.ErrnoError(8);
          }
          return stream;
        }, getStream: (fd) => FS.streams[fd], createStream(stream, fd = -1) {
          stream = Object.assign(new FS.FSStream(), stream);
          if (fd == -1) {
            fd = FS.nextfd();
          }
          stream.fd = fd;
          FS.streams[fd] = stream;
          return stream;
        }, closeStream(fd) {
          FS.streams[fd] = null;
        }, dupStream(origStream, fd = -1) {
          var stream = FS.createStream(origStream, fd);
          stream.stream_ops?.dup?.(stream);
          return stream;
        }, doSetAttr(stream, node, attr) {
          var setattr = stream?.stream_ops.setattr;
          var arg = setattr ? stream : node;
          setattr ??= node.node_ops.setattr;
          FS.checkOpExists(setattr, 63);
          setattr(arg, attr);
        }, chrdev_stream_ops: { open(stream) {
          var device = FS.getDevice(stream.node.rdev);
          stream.stream_ops = device.stream_ops;
          stream.stream_ops.open?.(stream);
        }, llseek() {
          throw new FS.ErrnoError(70);
        } }, major: (dev) => dev >> 8, minor: (dev) => dev & 255, makedev: (ma, mi) => ma << 8 | mi, registerDevice(dev, ops) {
          FS.devices[dev] = { stream_ops: ops };
        }, getDevice: (dev) => FS.devices[dev], getMounts(mount) {
          var mounts = [];
          var check = [mount];
          while (check.length) {
            var m2 = check.pop();
            mounts.push(m2);
            check.push(...m2.mounts);
          }
          return mounts;
        }, syncfs(populate, callback) {
          if (typeof populate == "function") {
            callback = populate;
            populate = false;
          }
          FS.syncFSRequests++;
          if (FS.syncFSRequests > 1) {
            err(`warning: ${FS.syncFSRequests} FS.syncfs operations in flight at once, probably just doing extra work`);
          }
          var mounts = FS.getMounts(FS.root.mount);
          var completed = 0;
          function doCallback(errCode) {
            FS.syncFSRequests--;
            return callback(errCode);
          }
          function done(errCode) {
            if (errCode) {
              if (!done.errored) {
                done.errored = true;
                return doCallback(errCode);
              }
              return;
            }
            if (++completed >= mounts.length) {
              doCallback(null);
            }
          }
          mounts.forEach((mount) => {
            if (!mount.type.syncfs) {
              return done(null);
            }
            mount.type.syncfs(mount, populate, done);
          });
        }, mount(type, opts, mountpoint) {
          var root = mountpoint === "/";
          var pseudo = !mountpoint;
          var node;
          if (root && FS.root) {
            throw new FS.ErrnoError(10);
          } else if (!root && !pseudo) {
            var lookup = FS.lookupPath(mountpoint, { follow_mount: false });
            mountpoint = lookup.path;
            node = lookup.node;
            if (FS.isMountpoint(node)) {
              throw new FS.ErrnoError(10);
            }
            if (!FS.isDir(node.mode)) {
              throw new FS.ErrnoError(54);
            }
          }
          var mount = { type, opts, mountpoint, mounts: [] };
          var mountRoot = type.mount(mount);
          mountRoot.mount = mount;
          mount.root = mountRoot;
          if (root) {
            FS.root = mountRoot;
          } else if (node) {
            node.mounted = mount;
            if (node.mount) {
              node.mount.mounts.push(mount);
            }
          }
          return mountRoot;
        }, unmount(mountpoint) {
          var lookup = FS.lookupPath(mountpoint, { follow_mount: false });
          if (!FS.isMountpoint(lookup.node)) {
            throw new FS.ErrnoError(28);
          }
          var node = lookup.node;
          var mount = node.mounted;
          var mounts = FS.getMounts(mount);
          Object.keys(FS.nameTable).forEach((hash) => {
            var current = FS.nameTable[hash];
            while (current) {
              var next = current.name_next;
              if (mounts.includes(current.mount)) {
                FS.destroyNode(current);
              }
              current = next;
            }
          });
          node.mounted = null;
          var idx = node.mount.mounts.indexOf(mount);
          node.mount.mounts.splice(idx, 1);
        }, lookup(parent, name) {
          return parent.node_ops.lookup(parent, name);
        }, mknod(path, mode, dev) {
          var lookup = FS.lookupPath(path, { parent: true });
          var parent = lookup.node;
          var name = PATH.basename(path);
          if (!name) {
            throw new FS.ErrnoError(28);
          }
          if (name === "." || name === "..") {
            throw new FS.ErrnoError(20);
          }
          var errCode = FS.mayCreate(parent, name);
          if (errCode) {
            throw new FS.ErrnoError(errCode);
          }
          if (!parent.node_ops.mknod) {
            throw new FS.ErrnoError(63);
          }
          return parent.node_ops.mknod(parent, name, mode, dev);
        }, statfs(path) {
          return FS.statfsNode(FS.lookupPath(path, { follow: true }).node);
        }, statfsStream(stream) {
          return FS.statfsNode(stream.node);
        }, statfsNode(node) {
          var rtn = { bsize: 4096, frsize: 4096, blocks: 1e6, bfree: 5e5, bavail: 5e5, files: FS.nextInode, ffree: FS.nextInode - 1, fsid: 42, flags: 2, namelen: 255 };
          if (node.node_ops.statfs) {
            Object.assign(rtn, node.node_ops.statfs(node.mount.opts.root));
          }
          return rtn;
        }, create(path, mode = 438) {
          mode &= 4095;
          mode |= 32768;
          return FS.mknod(path, mode, 0);
        }, mkdir(path, mode = 511) {
          mode &= 511 | 512;
          mode |= 16384;
          return FS.mknod(path, mode, 0);
        }, mkdirTree(path, mode) {
          var dirs = path.split("/");
          var d2 = "";
          for (var dir of dirs) {
            if (!dir) continue;
            if (d2 || PATH.isAbs(path)) d2 += "/";
            d2 += dir;
            try {
              FS.mkdir(d2, mode);
            } catch (e2) {
              if (e2.errno != 20) throw e2;
            }
          }
        }, mkdev(path, mode, dev) {
          if (typeof dev == "undefined") {
            dev = mode;
            mode = 438;
          }
          mode |= 8192;
          return FS.mknod(path, mode, dev);
        }, symlink(oldpath, newpath) {
          if (!PATH_FS.resolve(oldpath)) {
            throw new FS.ErrnoError(44);
          }
          var lookup = FS.lookupPath(newpath, { parent: true });
          var parent = lookup.node;
          if (!parent) {
            throw new FS.ErrnoError(44);
          }
          var newname = PATH.basename(newpath);
          var errCode = FS.mayCreate(parent, newname);
          if (errCode) {
            throw new FS.ErrnoError(errCode);
          }
          if (!parent.node_ops.symlink) {
            throw new FS.ErrnoError(63);
          }
          return parent.node_ops.symlink(parent, newname, oldpath);
        }, rename(old_path, new_path) {
          var old_dirname = PATH.dirname(old_path);
          var new_dirname = PATH.dirname(new_path);
          var old_name = PATH.basename(old_path);
          var new_name = PATH.basename(new_path);
          var lookup, old_dir, new_dir;
          lookup = FS.lookupPath(old_path, { parent: true });
          old_dir = lookup.node;
          lookup = FS.lookupPath(new_path, { parent: true });
          new_dir = lookup.node;
          if (!old_dir || !new_dir) throw new FS.ErrnoError(44);
          if (old_dir.mount !== new_dir.mount) {
            throw new FS.ErrnoError(75);
          }
          var old_node = FS.lookupNode(old_dir, old_name);
          var relative = PATH_FS.relative(old_path, new_dirname);
          if (relative.charAt(0) !== ".") {
            throw new FS.ErrnoError(28);
          }
          relative = PATH_FS.relative(new_path, old_dirname);
          if (relative.charAt(0) !== ".") {
            throw new FS.ErrnoError(55);
          }
          var new_node;
          try {
            new_node = FS.lookupNode(new_dir, new_name);
          } catch (e2) {
          }
          if (old_node === new_node) {
            return;
          }
          var isdir = FS.isDir(old_node.mode);
          var errCode = FS.mayDelete(old_dir, old_name, isdir);
          if (errCode) {
            throw new FS.ErrnoError(errCode);
          }
          errCode = new_node ? FS.mayDelete(new_dir, new_name, isdir) : FS.mayCreate(new_dir, new_name);
          if (errCode) {
            throw new FS.ErrnoError(errCode);
          }
          if (!old_dir.node_ops.rename) {
            throw new FS.ErrnoError(63);
          }
          if (FS.isMountpoint(old_node) || new_node && FS.isMountpoint(new_node)) {
            throw new FS.ErrnoError(10);
          }
          if (new_dir !== old_dir) {
            errCode = FS.nodePermissions(old_dir, "w");
            if (errCode) {
              throw new FS.ErrnoError(errCode);
            }
          }
          FS.hashRemoveNode(old_node);
          try {
            old_dir.node_ops.rename(old_node, new_dir, new_name);
            old_node.parent = new_dir;
          } catch (e2) {
            throw e2;
          } finally {
            FS.hashAddNode(old_node);
          }
        }, rmdir(path) {
          var lookup = FS.lookupPath(path, { parent: true });
          var parent = lookup.node;
          var name = PATH.basename(path);
          var node = FS.lookupNode(parent, name);
          var errCode = FS.mayDelete(parent, name, true);
          if (errCode) {
            throw new FS.ErrnoError(errCode);
          }
          if (!parent.node_ops.rmdir) {
            throw new FS.ErrnoError(63);
          }
          if (FS.isMountpoint(node)) {
            throw new FS.ErrnoError(10);
          }
          parent.node_ops.rmdir(parent, name);
          FS.destroyNode(node);
        }, readdir(path) {
          var lookup = FS.lookupPath(path, { follow: true });
          var node = lookup.node;
          var readdir = FS.checkOpExists(node.node_ops.readdir, 54);
          return readdir(node);
        }, unlink(path) {
          var lookup = FS.lookupPath(path, { parent: true });
          var parent = lookup.node;
          if (!parent) {
            throw new FS.ErrnoError(44);
          }
          var name = PATH.basename(path);
          var node = FS.lookupNode(parent, name);
          var errCode = FS.mayDelete(parent, name, false);
          if (errCode) {
            throw new FS.ErrnoError(errCode);
          }
          if (!parent.node_ops.unlink) {
            throw new FS.ErrnoError(63);
          }
          if (FS.isMountpoint(node)) {
            throw new FS.ErrnoError(10);
          }
          parent.node_ops.unlink(parent, name);
          FS.destroyNode(node);
        }, readlink(path) {
          var lookup = FS.lookupPath(path);
          var link = lookup.node;
          if (!link) {
            throw new FS.ErrnoError(44);
          }
          if (!link.node_ops.readlink) {
            throw new FS.ErrnoError(28);
          }
          return link.node_ops.readlink(link);
        }, stat(path, dontFollow) {
          var lookup = FS.lookupPath(path, { follow: !dontFollow });
          var node = lookup.node;
          var getattr = FS.checkOpExists(node.node_ops.getattr, 63);
          return getattr(node);
        }, fstat(fd) {
          var stream = FS.getStreamChecked(fd);
          var node = stream.node;
          var getattr = stream.stream_ops.getattr;
          var arg = getattr ? stream : node;
          getattr ??= node.node_ops.getattr;
          FS.checkOpExists(getattr, 63);
          return getattr(arg);
        }, lstat(path) {
          return FS.stat(path, true);
        }, doChmod(stream, node, mode, dontFollow) {
          FS.doSetAttr(stream, node, { mode: mode & 4095 | node.mode & ~4095, ctime: Date.now(), dontFollow });
        }, chmod(path, mode, dontFollow) {
          var node;
          if (typeof path == "string") {
            var lookup = FS.lookupPath(path, { follow: !dontFollow });
            node = lookup.node;
          } else {
            node = path;
          }
          FS.doChmod(null, node, mode, dontFollow);
        }, lchmod(path, mode) {
          FS.chmod(path, mode, true);
        }, fchmod(fd, mode) {
          var stream = FS.getStreamChecked(fd);
          FS.doChmod(stream, stream.node, mode, false);
        }, doChown(stream, node, dontFollow) {
          FS.doSetAttr(stream, node, { timestamp: Date.now(), dontFollow });
        }, chown(path, uid, gid, dontFollow) {
          var node;
          if (typeof path == "string") {
            var lookup = FS.lookupPath(path, { follow: !dontFollow });
            node = lookup.node;
          } else {
            node = path;
          }
          FS.doChown(null, node, dontFollow);
        }, lchown(path, uid, gid) {
          FS.chown(path, uid, gid, true);
        }, fchown(fd, uid, gid) {
          var stream = FS.getStreamChecked(fd);
          FS.doChown(stream, stream.node, false);
        }, doTruncate(stream, node, len) {
          if (FS.isDir(node.mode)) {
            throw new FS.ErrnoError(31);
          }
          if (!FS.isFile(node.mode)) {
            throw new FS.ErrnoError(28);
          }
          var errCode = FS.nodePermissions(node, "w");
          if (errCode) {
            throw new FS.ErrnoError(errCode);
          }
          FS.doSetAttr(stream, node, { size: len, timestamp: Date.now() });
        }, truncate(path, len) {
          if (len < 0) {
            throw new FS.ErrnoError(28);
          }
          var node;
          if (typeof path == "string") {
            var lookup = FS.lookupPath(path, { follow: true });
            node = lookup.node;
          } else {
            node = path;
          }
          FS.doTruncate(null, node, len);
        }, ftruncate(fd, len) {
          var stream = FS.getStreamChecked(fd);
          if (len < 0 || (stream.flags & 2097155) === 0) {
            throw new FS.ErrnoError(28);
          }
          FS.doTruncate(stream, stream.node, len);
        }, utime(path, atime, mtime) {
          var lookup = FS.lookupPath(path, { follow: true });
          var node = lookup.node;
          var setattr = FS.checkOpExists(node.node_ops.setattr, 63);
          setattr(node, { atime, mtime });
        }, open(path, flags, mode = 438) {
          if (path === "") {
            throw new FS.ErrnoError(44);
          }
          flags = typeof flags == "string" ? FS_modeStringToFlags(flags) : flags;
          if (flags & 64) {
            mode = mode & 4095 | 32768;
          } else {
            mode = 0;
          }
          var node;
          var isDirPath;
          if (typeof path == "object") {
            node = path;
          } else {
            isDirPath = path.endsWith("/");
            var lookup = FS.lookupPath(path, { follow: !(flags & 131072), noent_okay: true });
            node = lookup.node;
            path = lookup.path;
          }
          var created = false;
          if (flags & 64) {
            if (node) {
              if (flags & 128) {
                throw new FS.ErrnoError(20);
              }
            } else if (isDirPath) {
              throw new FS.ErrnoError(31);
            } else {
              node = FS.mknod(path, mode | 511, 0);
              created = true;
            }
          }
          if (!node) {
            throw new FS.ErrnoError(44);
          }
          if (FS.isChrdev(node.mode)) {
            flags &= ~512;
          }
          if (flags & 65536 && !FS.isDir(node.mode)) {
            throw new FS.ErrnoError(54);
          }
          if (!created) {
            var errCode = FS.mayOpen(node, flags);
            if (errCode) {
              throw new FS.ErrnoError(errCode);
            }
          }
          if (flags & 512 && !created) {
            FS.truncate(node, 0);
          }
          flags &= ~(128 | 512 | 131072);
          var stream = FS.createStream({ node, path: FS.getPath(node), flags, seekable: true, position: 0, stream_ops: node.stream_ops, ungotten: [], error: false });
          if (stream.stream_ops.open) {
            stream.stream_ops.open(stream);
          }
          if (created) {
            FS.chmod(node, mode & 511);
          }
          if (Module["logReadFiles"] && !(flags & 1)) {
            if (!(path in FS.readFiles)) {
              FS.readFiles[path] = 1;
            }
          }
          return stream;
        }, close(stream) {
          if (FS.isClosed(stream)) {
            throw new FS.ErrnoError(8);
          }
          if (stream.getdents) stream.getdents = null;
          try {
            if (stream.stream_ops.close) {
              stream.stream_ops.close(stream);
            }
          } catch (e2) {
            throw e2;
          } finally {
            FS.closeStream(stream.fd);
          }
          stream.fd = null;
        }, isClosed(stream) {
          return stream.fd === null;
        }, llseek(stream, offset, whence) {
          if (FS.isClosed(stream)) {
            throw new FS.ErrnoError(8);
          }
          if (!stream.seekable || !stream.stream_ops.llseek) {
            throw new FS.ErrnoError(70);
          }
          if (whence != 0 && whence != 1 && whence != 2) {
            throw new FS.ErrnoError(28);
          }
          stream.position = stream.stream_ops.llseek(stream, offset, whence);
          stream.ungotten = [];
          return stream.position;
        }, read(stream, buffer, offset, length, position) {
          if (length < 0 || position < 0) {
            throw new FS.ErrnoError(28);
          }
          if (FS.isClosed(stream)) {
            throw new FS.ErrnoError(8);
          }
          if ((stream.flags & 2097155) === 1) {
            throw new FS.ErrnoError(8);
          }
          if (FS.isDir(stream.node.mode)) {
            throw new FS.ErrnoError(31);
          }
          if (!stream.stream_ops.read) {
            throw new FS.ErrnoError(28);
          }
          var seeking = typeof position != "undefined";
          if (!seeking) {
            position = stream.position;
          } else if (!stream.seekable) {
            throw new FS.ErrnoError(70);
          }
          var bytesRead = stream.stream_ops.read(stream, buffer, offset, length, position);
          if (!seeking) stream.position += bytesRead;
          return bytesRead;
        }, write(stream, buffer, offset, length, position, canOwn) {
          if (length < 0 || position < 0) {
            throw new FS.ErrnoError(28);
          }
          if (FS.isClosed(stream)) {
            throw new FS.ErrnoError(8);
          }
          if ((stream.flags & 2097155) === 0) {
            throw new FS.ErrnoError(8);
          }
          if (FS.isDir(stream.node.mode)) {
            throw new FS.ErrnoError(31);
          }
          if (!stream.stream_ops.write) {
            throw new FS.ErrnoError(28);
          }
          if (stream.seekable && stream.flags & 1024) {
            FS.llseek(stream, 0, 2);
          }
          var seeking = typeof position != "undefined";
          if (!seeking) {
            position = stream.position;
          } else if (!stream.seekable) {
            throw new FS.ErrnoError(70);
          }
          var bytesWritten = stream.stream_ops.write(stream, buffer, offset, length, position, canOwn);
          if (!seeking) stream.position += bytesWritten;
          return bytesWritten;
        }, mmap(stream, length, position, prot, flags) {
          if ((prot & 2) !== 0 && (flags & 2) === 0 && (stream.flags & 2097155) !== 2) {
            throw new FS.ErrnoError(2);
          }
          if ((stream.flags & 2097155) === 1) {
            throw new FS.ErrnoError(2);
          }
          if (!stream.stream_ops.mmap) {
            throw new FS.ErrnoError(43);
          }
          if (!length) {
            throw new FS.ErrnoError(28);
          }
          return stream.stream_ops.mmap(stream, length, position, prot, flags);
        }, msync(stream, buffer, offset, length, mmapFlags) {
          if (!stream.stream_ops.msync) {
            return 0;
          }
          return stream.stream_ops.msync(stream, buffer, offset, length, mmapFlags);
        }, ioctl(stream, cmd, arg) {
          if (!stream.stream_ops.ioctl) {
            throw new FS.ErrnoError(59);
          }
          return stream.stream_ops.ioctl(stream, cmd, arg);
        }, readFile(path, opts = {}) {
          opts.flags = opts.flags || 0;
          opts.encoding = opts.encoding || "binary";
          if (opts.encoding !== "utf8" && opts.encoding !== "binary") {
            throw new Error(`Invalid encoding type "${opts.encoding}"`);
          }
          var stream = FS.open(path, opts.flags);
          var stat = FS.stat(path);
          var length = stat.size;
          var buf = new Uint8Array(length);
          FS.read(stream, buf, 0, length, 0);
          if (opts.encoding === "utf8") {
            buf = UTF8ArrayToString(buf);
          }
          FS.close(stream);
          return buf;
        }, writeFile(path, data, opts = {}) {
          opts.flags = opts.flags || 577;
          var stream = FS.open(path, opts.flags, opts.mode);
          if (typeof data == "string") {
            data = new Uint8Array(intArrayFromString(data, true));
          }
          if (ArrayBuffer.isView(data)) {
            FS.write(stream, data, 0, data.byteLength, void 0, opts.canOwn);
          } else {
            throw new Error("Unsupported data type");
          }
          FS.close(stream);
        }, cwd: () => FS.currentPath, chdir(path) {
          var lookup = FS.lookupPath(path, { follow: true });
          if (lookup.node === null) {
            throw new FS.ErrnoError(44);
          }
          if (!FS.isDir(lookup.node.mode)) {
            throw new FS.ErrnoError(54);
          }
          var errCode = FS.nodePermissions(lookup.node, "x");
          if (errCode) {
            throw new FS.ErrnoError(errCode);
          }
          FS.currentPath = lookup.path;
        }, createDefaultDirectories() {
          FS.mkdir("/tmp");
          FS.mkdir("/home");
          FS.mkdir("/home/<USER>");
        }, createDefaultDevices() {
          FS.mkdir("/dev");
          FS.registerDevice(FS.makedev(1, 3), { read: () => 0, write: (stream, buffer, offset, length, pos) => length, llseek: () => 0 });
          FS.mkdev("/dev/null", FS.makedev(1, 3));
          TTY.register(FS.makedev(5, 0), TTY.default_tty_ops);
          TTY.register(FS.makedev(6, 0), TTY.default_tty1_ops);
          FS.mkdev("/dev/tty", FS.makedev(5, 0));
          FS.mkdev("/dev/tty1", FS.makedev(6, 0));
          var randomBuffer = new Uint8Array(1024), randomLeft = 0;
          var randomByte = () => {
            if (randomLeft === 0) {
              randomFill(randomBuffer);
              randomLeft = randomBuffer.byteLength;
            }
            return randomBuffer[--randomLeft];
          };
          FS.createDevice("/dev", "random", randomByte);
          FS.createDevice("/dev", "urandom", randomByte);
          FS.mkdir("/dev/shm");
          FS.mkdir("/dev/shm/tmp");
        }, createSpecialDirectories() {
          FS.mkdir("/proc");
          var proc_self = FS.mkdir("/proc/self");
          FS.mkdir("/proc/self/fd");
          FS.mount({ mount() {
            var node = FS.createNode(proc_self, "fd", 16895, 73);
            node.stream_ops = { llseek: MEMFS.stream_ops.llseek };
            node.node_ops = { lookup(parent, name) {
              var fd = +name;
              var stream = FS.getStreamChecked(fd);
              var ret = { parent: null, mount: { mountpoint: "fake" }, node_ops: { readlink: () => stream.path }, id: fd + 1 };
              ret.parent = ret;
              return ret;
            }, readdir() {
              return Array.from(FS.streams.entries()).filter(([k2, v2]) => v2).map(([k2, v2]) => k2.toString());
            } };
            return node;
          } }, {}, "/proc/self/fd");
        }, createStandardStreams(input, output, error) {
          if (input) {
            FS.createDevice("/dev", "stdin", input);
          } else {
            FS.symlink("/dev/tty", "/dev/stdin");
          }
          if (output) {
            FS.createDevice("/dev", "stdout", null, output);
          } else {
            FS.symlink("/dev/tty", "/dev/stdout");
          }
          if (error) {
            FS.createDevice("/dev", "stderr", null, error);
          } else {
            FS.symlink("/dev/tty1", "/dev/stderr");
          }
          var stdin = FS.open("/dev/stdin", 0);
          var stdout = FS.open("/dev/stdout", 1);
          var stderr = FS.open("/dev/stderr", 1);
        }, staticInit() {
          FS.nameTable = new Array(4096);
          FS.mount(MEMFS, {}, "/");
          FS.createDefaultDirectories();
          FS.createDefaultDevices();
          FS.createSpecialDirectories();
          FS.filesystems = { MEMFS };
        }, init(input, output, error) {
          FS.initialized = true;
          input ??= Module["stdin"];
          output ??= Module["stdout"];
          error ??= Module["stderr"];
          FS.createStandardStreams(input, output, error);
        }, quit() {
          FS.initialized = false;
          for (var stream of FS.streams) {
            if (stream) {
              FS.close(stream);
            }
          }
        }, findObject(path, dontResolveLastLink) {
          var ret = FS.analyzePath(path, dontResolveLastLink);
          if (!ret.exists) {
            return null;
          }
          return ret.object;
        }, analyzePath(path, dontResolveLastLink) {
          try {
            var lookup = FS.lookupPath(path, { follow: !dontResolveLastLink });
            path = lookup.path;
          } catch (e2) {
          }
          var ret = { isRoot: false, exists: false, error: 0, name: null, path: null, object: null, parentExists: false, parentPath: null, parentObject: null };
          try {
            var lookup = FS.lookupPath(path, { parent: true });
            ret.parentExists = true;
            ret.parentPath = lookup.path;
            ret.parentObject = lookup.node;
            ret.name = PATH.basename(path);
            lookup = FS.lookupPath(path, { follow: !dontResolveLastLink });
            ret.exists = true;
            ret.path = lookup.path;
            ret.object = lookup.node;
            ret.name = lookup.node.name;
            ret.isRoot = lookup.path === "/";
          } catch (e2) {
            ret.error = e2.errno;
          }
          return ret;
        }, createPath(parent, path, canRead, canWrite) {
          parent = typeof parent == "string" ? parent : FS.getPath(parent);
          var parts = path.split("/").reverse();
          while (parts.length) {
            var part = parts.pop();
            if (!part) continue;
            var current = PATH.join2(parent, part);
            try {
              FS.mkdir(current);
            } catch (e2) {
              if (e2.errno != 20) throw e2;
            }
            parent = current;
          }
          return current;
        }, createFile(parent, name, properties, canRead, canWrite) {
          var path = PATH.join2(typeof parent == "string" ? parent : FS.getPath(parent), name);
          var mode = FS_getMode(canRead, canWrite);
          return FS.create(path, mode);
        }, createDataFile(parent, name, data, canRead, canWrite, canOwn) {
          var path = name;
          if (parent) {
            parent = typeof parent == "string" ? parent : FS.getPath(parent);
            path = name ? PATH.join2(parent, name) : parent;
          }
          var mode = FS_getMode(canRead, canWrite);
          var node = FS.create(path, mode);
          if (data) {
            if (typeof data == "string") {
              var arr = new Array(data.length);
              for (var i3 = 0, len = data.length; i3 < len; ++i3) arr[i3] = data.charCodeAt(i3);
              data = arr;
            }
            FS.chmod(node, mode | 146);
            var stream = FS.open(node, 577);
            FS.write(stream, data, 0, data.length, 0, canOwn);
            FS.close(stream);
            FS.chmod(node, mode);
          }
        }, createDevice(parent, name, input, output) {
          var path = PATH.join2(typeof parent == "string" ? parent : FS.getPath(parent), name);
          var mode = FS_getMode(!!input, !!output);
          FS.createDevice.major ??= 64;
          var dev = FS.makedev(FS.createDevice.major++, 0);
          FS.registerDevice(dev, { open(stream) {
            stream.seekable = false;
          }, close(stream) {
            if (output?.buffer?.length) {
              output(10);
            }
          }, read(stream, buffer, offset, length, pos) {
            var bytesRead = 0;
            for (var i3 = 0; i3 < length; i3++) {
              var result;
              try {
                result = input();
              } catch (e2) {
                throw new FS.ErrnoError(29);
              }
              if (result === void 0 && bytesRead === 0) {
                throw new FS.ErrnoError(6);
              }
              if (result === null || result === void 0) break;
              bytesRead++;
              buffer[offset + i3] = result;
            }
            if (bytesRead) {
              stream.node.atime = Date.now();
            }
            return bytesRead;
          }, write(stream, buffer, offset, length, pos) {
            for (var i3 = 0; i3 < length; i3++) {
              try {
                output(buffer[offset + i3]);
              } catch (e2) {
                throw new FS.ErrnoError(29);
              }
            }
            if (length) {
              stream.node.mtime = stream.node.ctime = Date.now();
            }
            return i3;
          } });
          return FS.mkdev(path, mode, dev);
        }, forceLoadFile(obj) {
          if (obj.isDevice || obj.isFolder || obj.link || obj.contents) return true;
          if (typeof XMLHttpRequest != "undefined") {
            throw new Error("Lazy loading should have been performed (contents set) in createLazyFile, but it was not. Lazy loading only works in web workers. Use --embed-file or --preload-file in emcc on the main thread.");
          } else {
            try {
              obj.contents = readBinary(obj.url);
              obj.usedBytes = obj.contents.length;
            } catch (e2) {
              throw new FS.ErrnoError(29);
            }
          }
        }, createLazyFile(parent, name, url, canRead, canWrite) {
          class LazyUint8Array {
            lengthKnown = false;
            chunks = [];
            get(idx) {
              if (idx > this.length - 1 || idx < 0) {
                return void 0;
              }
              var chunkOffset = idx % this.chunkSize;
              var chunkNum = idx / this.chunkSize | 0;
              return this.getter(chunkNum)[chunkOffset];
            }
            setDataGetter(getter) {
              this.getter = getter;
            }
            cacheLength() {
              var xhr = new XMLHttpRequest();
              xhr.open("HEAD", url, false);
              xhr.send(null);
              if (!(xhr.status >= 200 && xhr.status < 300 || xhr.status === 304)) throw new Error("Couldn't load " + url + ". Status: " + xhr.status);
              var datalength = Number(xhr.getResponseHeader("Content-length"));
              var header;
              var hasByteServing = (header = xhr.getResponseHeader("Accept-Ranges")) && header === "bytes";
              var usesGzip = (header = xhr.getResponseHeader("Content-Encoding")) && header === "gzip";
              var chunkSize = 1024 * 1024;
              if (!hasByteServing) chunkSize = datalength;
              var doXHR = (from, to) => {
                if (from > to) throw new Error("invalid range (" + from + ", " + to + ") or no bytes requested!");
                if (to > datalength - 1) throw new Error("only " + datalength + " bytes available! programmer error!");
                var xhr2 = new XMLHttpRequest();
                xhr2.open("GET", url, false);
                if (datalength !== chunkSize) xhr2.setRequestHeader("Range", "bytes=" + from + "-" + to);
                xhr2.responseType = "arraybuffer";
                if (xhr2.overrideMimeType) {
                  xhr2.overrideMimeType("text/plain; charset=x-user-defined");
                }
                xhr2.send(null);
                if (!(xhr2.status >= 200 && xhr2.status < 300 || xhr2.status === 304)) throw new Error("Couldn't load " + url + ". Status: " + xhr2.status);
                if (xhr2.response !== void 0) {
                  return new Uint8Array(xhr2.response || []);
                }
                return intArrayFromString(xhr2.responseText || "", true);
              };
              var lazyArray2 = this;
              lazyArray2.setDataGetter((chunkNum) => {
                var start = chunkNum * chunkSize;
                var end = (chunkNum + 1) * chunkSize - 1;
                end = Math.min(end, datalength - 1);
                if (typeof lazyArray2.chunks[chunkNum] == "undefined") {
                  lazyArray2.chunks[chunkNum] = doXHR(start, end);
                }
                if (typeof lazyArray2.chunks[chunkNum] == "undefined") throw new Error("doXHR failed!");
                return lazyArray2.chunks[chunkNum];
              });
              if (usesGzip || !datalength) {
                chunkSize = datalength = 1;
                datalength = this.getter(0).length;
                chunkSize = datalength;
                out("LazyFiles on gzip forces download of the whole file when length is accessed");
              }
              this._length = datalength;
              this._chunkSize = chunkSize;
              this.lengthKnown = true;
            }
            get length() {
              if (!this.lengthKnown) {
                this.cacheLength();
              }
              return this._length;
            }
            get chunkSize() {
              if (!this.lengthKnown) {
                this.cacheLength();
              }
              return this._chunkSize;
            }
          }
          if (typeof XMLHttpRequest != "undefined") {
            if (!ENVIRONMENT_IS_WORKER) throw "Cannot do synchronous binary XHRs outside webworkers in modern browsers. Use --embed-file or --preload-file in emcc";
            var lazyArray = new LazyUint8Array();
            var properties = { isDevice: false, contents: lazyArray };
          } else {
            var properties = { isDevice: false, url };
          }
          var node = FS.createFile(parent, name, properties, canRead, canWrite);
          if (properties.contents) {
            node.contents = properties.contents;
          } else if (properties.url) {
            node.contents = null;
            node.url = properties.url;
          }
          Object.defineProperties(node, { usedBytes: { get: function() {
            return this.contents.length;
          } } });
          var stream_ops = {};
          var keys = Object.keys(node.stream_ops);
          keys.forEach((key) => {
            var fn = node.stream_ops[key];
            stream_ops[key] = (...args) => {
              FS.forceLoadFile(node);
              return fn(...args);
            };
          });
          function writeChunks(stream, buffer, offset, length, position) {
            var contents = stream.node.contents;
            if (position >= contents.length) return 0;
            var size = Math.min(contents.length - position, length);
            if (contents.slice) {
              for (var i3 = 0; i3 < size; i3++) {
                buffer[offset + i3] = contents[position + i3];
              }
            } else {
              for (var i3 = 0; i3 < size; i3++) {
                buffer[offset + i3] = contents.get(position + i3);
              }
            }
            return size;
          }
          stream_ops.read = (stream, buffer, offset, length, position) => {
            FS.forceLoadFile(node);
            return writeChunks(stream, buffer, offset, length, position);
          };
          stream_ops.mmap = (stream, length, position, prot, flags) => {
            FS.forceLoadFile(node);
            var ptr = mmapAlloc(length);
            if (!ptr) {
              throw new FS.ErrnoError(48);
            }
            writeChunks(stream, HEAP8, ptr, length, position);
            return { ptr, allocated: true };
          };
          node.stream_ops = stream_ops;
          return node;
        } };
        var UTF8ToString = (ptr, maxBytesToRead) => ptr ? UTF8ArrayToString(HEAPU8, ptr, maxBytesToRead) : "";
        var SYSCALLS = { DEFAULT_POLLMASK: 5, calculateAt(dirfd, path, allowEmpty) {
          if (PATH.isAbs(path)) {
            return path;
          }
          var dir;
          if (dirfd === -100) {
            dir = FS.cwd();
          } else {
            var dirstream = SYSCALLS.getStreamFromFD(dirfd);
            dir = dirstream.path;
          }
          if (path.length == 0) {
            if (!allowEmpty) {
              throw new FS.ErrnoError(44);
            }
            return dir;
          }
          return dir + "/" + path;
        }, writeStat(buf, stat) {
          HEAP32[buf >> 2] = stat.dev;
          HEAP32[buf + 4 >> 2] = stat.mode;
          HEAPU32[buf + 8 >> 2] = stat.nlink;
          HEAP32[buf + 12 >> 2] = stat.uid;
          HEAP32[buf + 16 >> 2] = stat.gid;
          HEAP32[buf + 20 >> 2] = stat.rdev;
          HEAP64[buf + 24 >> 3] = BigInt(stat.size);
          HEAP32[buf + 32 >> 2] = 4096;
          HEAP32[buf + 36 >> 2] = stat.blocks;
          var atime = stat.atime.getTime();
          var mtime = stat.mtime.getTime();
          var ctime = stat.ctime.getTime();
          HEAP64[buf + 40 >> 3] = BigInt(Math.floor(atime / 1e3));
          HEAPU32[buf + 48 >> 2] = atime % 1e3 * 1e3 * 1e3;
          HEAP64[buf + 56 >> 3] = BigInt(Math.floor(mtime / 1e3));
          HEAPU32[buf + 64 >> 2] = mtime % 1e3 * 1e3 * 1e3;
          HEAP64[buf + 72 >> 3] = BigInt(Math.floor(ctime / 1e3));
          HEAPU32[buf + 80 >> 2] = ctime % 1e3 * 1e3 * 1e3;
          HEAP64[buf + 88 >> 3] = BigInt(stat.ino);
          return 0;
        }, writeStatFs(buf, stats) {
          HEAP32[buf + 4 >> 2] = stats.bsize;
          HEAP32[buf + 40 >> 2] = stats.bsize;
          HEAP32[buf + 8 >> 2] = stats.blocks;
          HEAP32[buf + 12 >> 2] = stats.bfree;
          HEAP32[buf + 16 >> 2] = stats.bavail;
          HEAP32[buf + 20 >> 2] = stats.files;
          HEAP32[buf + 24 >> 2] = stats.ffree;
          HEAP32[buf + 28 >> 2] = stats.fsid;
          HEAP32[buf + 44 >> 2] = stats.flags;
          HEAP32[buf + 36 >> 2] = stats.namelen;
        }, doMsync(addr, stream, len, flags, offset) {
          if (!FS.isFile(stream.node.mode)) {
            throw new FS.ErrnoError(43);
          }
          if (flags & 2) {
            return 0;
          }
          var buffer = HEAPU8.slice(addr, addr + len);
          FS.msync(stream, buffer, offset, len, flags);
        }, getStreamFromFD(fd) {
          var stream = FS.getStreamChecked(fd);
          return stream;
        }, varargs: void 0, getStr(ptr) {
          var ret = UTF8ToString(ptr);
          return ret;
        } };
        function _fd_close(fd) {
          try {
            var stream = SYSCALLS.getStreamFromFD(fd);
            FS.close(stream);
            return 0;
          } catch (e2) {
            if (typeof FS == "undefined" || !(e2.name === "ErrnoError")) throw e2;
            return e2.errno;
          }
        }
        var INT53_MAX = 9007199254740992;
        var INT53_MIN = -9007199254740992;
        var bigintToI53Checked = (num) => num < INT53_MIN || num > INT53_MAX ? NaN : Number(num);
        function _fd_seek(fd, offset, whence, newOffset) {
          offset = bigintToI53Checked(offset);
          try {
            if (isNaN(offset)) return 61;
            var stream = SYSCALLS.getStreamFromFD(fd);
            FS.llseek(stream, offset, whence);
            HEAP64[newOffset >> 3] = BigInt(stream.position);
            if (stream.getdents && offset === 0 && whence === 0) stream.getdents = null;
            return 0;
          } catch (e2) {
            if (typeof FS == "undefined" || !(e2.name === "ErrnoError")) throw e2;
            return e2.errno;
          }
        }
        var doWritev = (stream, iov, iovcnt, offset) => {
          var ret = 0;
          for (var i3 = 0; i3 < iovcnt; i3++) {
            var ptr = HEAPU32[iov >> 2];
            var len = HEAPU32[iov + 4 >> 2];
            iov += 8;
            var curr = FS.write(stream, HEAP8, ptr, len, offset);
            if (curr < 0) return -1;
            ret += curr;
            if (curr < len) {
              break;
            }
            if (typeof offset != "undefined") {
              offset += curr;
            }
          }
          return ret;
        };
        function _fd_write(fd, iov, iovcnt, pnum) {
          try {
            var stream = SYSCALLS.getStreamFromFD(fd);
            var num = doWritev(stream, iov, iovcnt);
            HEAPU32[pnum >> 2] = num;
            return 0;
          } catch (e2) {
            if (typeof FS == "undefined" || !(e2.name === "ErrnoError")) throw e2;
            return e2.errno;
          }
        }
        var wasmTableMirror = [];
        var wasmTable;
        var getWasmTableEntry = (funcPtr) => {
          var func = wasmTableMirror[funcPtr];
          if (!func) {
            wasmTableMirror[funcPtr] = func = wasmTable.get(funcPtr);
          }
          return func;
        };
        var getCFunc = (ident) => {
          var func = Module["_" + ident];
          return func;
        };
        var writeArrayToMemory = (array, buffer) => {
          HEAP8.set(array, buffer);
        };
        var stackAlloc = (sz) => __emscripten_stack_alloc(sz);
        var stringToUTF8OnStack = (str) => {
          var size = lengthBytesUTF8(str) + 1;
          var ret = stackAlloc(size);
          stringToUTF8(str, ret, size);
          return ret;
        };
        var ccall = (ident, returnType, argTypes, args, opts) => {
          var toC = { string: (str) => {
            var ret2 = 0;
            if (str !== null && str !== void 0 && str !== 0) {
              ret2 = stringToUTF8OnStack(str);
            }
            return ret2;
          }, array: (arr) => {
            var ret2 = stackAlloc(arr.length);
            writeArrayToMemory(arr, ret2);
            return ret2;
          } };
          function convertReturnValue(ret2) {
            if (returnType === "string") {
              return UTF8ToString(ret2);
            }
            if (returnType === "boolean") return Boolean(ret2);
            return ret2;
          }
          var func = getCFunc(ident);
          var cArgs = [];
          var stack = 0;
          if (args) {
            for (var i3 = 0; i3 < args.length; i3++) {
              var converter = toC[argTypes[i3]];
              if (converter) {
                if (stack === 0) stack = stackSave();
                cArgs[i3] = converter(args[i3]);
              } else {
                cArgs[i3] = args[i3];
              }
            }
          }
          var ret = func(...cArgs);
          function onDone(ret2) {
            if (stack !== 0) stackRestore(stack);
            return convertReturnValue(ret2);
          }
          ret = onDone(ret);
          return ret;
        };
        var cwrap = (ident, returnType, argTypes, opts) => {
          var numericArgs = !argTypes || argTypes.every((type) => type === "number" || type === "boolean");
          var numericRet = returnType !== "string";
          if (numericRet && numericArgs && !opts) {
            return getCFunc(ident);
          }
          return (...args) => ccall(ident, returnType, argTypes, args, opts);
        };
        for (var base64ReverseLookup = new Uint8Array(123), i2 = 25; i2 >= 0; --i2) {
          base64ReverseLookup[48 + i2] = 52 + i2;
          base64ReverseLookup[65 + i2] = i2;
          base64ReverseLookup[97 + i2] = 26 + i2;
        }
        base64ReverseLookup[43] = 62;
        base64ReverseLookup[47] = 63;
        FS.createPreloadedFile = FS_createPreloadedFile;
        FS.staticInit();
        MEMFS.doesNotExistError = new FS.ErrnoError(44);
        MEMFS.doesNotExistError.stack = "<generic error, no stack>";
        {
          if (Module["noExitRuntime"]) noExitRuntime = Module["noExitRuntime"];
          if (Module["preloadPlugins"]) preloadPlugins = Module["preloadPlugins"];
          if (Module["print"]) out = Module["print"];
          if (Module["printErr"]) err = Module["printErr"];
          if (Module["wasmBinary"]) wasmBinary = Module["wasmBinary"];
          if (Module["arguments"]) arguments_ = Module["arguments"];
          if (Module["thisProgram"]) thisProgram = Module["thisProgram"];
        }
        Module["ccall"] = ccall;
        Module["cwrap"] = cwrap;
        var _AecNew, _AecCancelEcho, _AecDestroy, _malloc, _free, _setThrew, __emscripten_tempret_set, __emscripten_stack_restore, __emscripten_stack_alloc, _emscripten_stack_get_current, ___cxa_can_catch;
        function assignWasmExports(wasmExports2) {
          Module["_AecNew"] = _AecNew = wasmExports2["w"];
          Module["_AecCancelEcho"] = _AecCancelEcho = wasmExports2["x"];
          Module["_AecDestroy"] = _AecDestroy = wasmExports2["y"];
          Module["_malloc"] = _malloc = wasmExports2["z"];
          Module["_free"] = _free = wasmExports2["A"];
          _setThrew = wasmExports2["B"];
          __emscripten_tempret_set = wasmExports2["C"];
          __emscripten_stack_restore = wasmExports2["D"];
          __emscripten_stack_alloc = wasmExports2["E"];
          _emscripten_stack_get_current = wasmExports2["F"];
          ___cxa_can_catch = wasmExports2["G"];
        }
        var wasmImports = { a: ___cxa_find_matching_catch_2, q: ___cxa_throw, b: ___resumeException, p: ___syscall_getcwd, r: __abort_js, t: _emscripten_resize_heap, n: _environ_get, o: _environ_sizes_get, k: _exit, s: _fd_close, l: _fd_seek, g: _fd_write, i: invoke_ii, f: invoke_iiii, m: invoke_iiiiii, c: invoke_vi, d: invoke_vii, e: invoke_viii, j: invoke_viiii, h: invoke_viiiii };
        var wasmExports = await createWasm();
        function invoke_vi(index, a1) {
          var sp = stackSave();
          try {
            getWasmTableEntry(index)(a1);
          } catch (e2) {
            stackRestore(sp);
            if (e2 !== e2 + 0) throw e2;
            _setThrew(1, 0);
          }
        }
        function invoke_viii(index, a1, a2, a3) {
          var sp = stackSave();
          try {
            getWasmTableEntry(index)(a1, a2, a3);
          } catch (e2) {
            stackRestore(sp);
            if (e2 !== e2 + 0) throw e2;
            _setThrew(1, 0);
          }
        }
        function invoke_vii(index, a1, a2) {
          var sp = stackSave();
          try {
            getWasmTableEntry(index)(a1, a2);
          } catch (e2) {
            stackRestore(sp);
            if (e2 !== e2 + 0) throw e2;
            _setThrew(1, 0);
          }
        }
        function invoke_viiii(index, a1, a2, a3, a4) {
          var sp = stackSave();
          try {
            getWasmTableEntry(index)(a1, a2, a3, a4);
          } catch (e2) {
            stackRestore(sp);
            if (e2 !== e2 + 0) throw e2;
            _setThrew(1, 0);
          }
        }
        function invoke_iiii(index, a1, a2, a3) {
          var sp = stackSave();
          try {
            return getWasmTableEntry(index)(a1, a2, a3);
          } catch (e2) {
            stackRestore(sp);
            if (e2 !== e2 + 0) throw e2;
            _setThrew(1, 0);
          }
        }
        function invoke_viiiii(index, a1, a2, a3, a4, a5) {
          var sp = stackSave();
          try {
            getWasmTableEntry(index)(a1, a2, a3, a4, a5);
          } catch (e2) {
            stackRestore(sp);
            if (e2 !== e2 + 0) throw e2;
            _setThrew(1, 0);
          }
        }
        function invoke_ii(index, a1) {
          var sp = stackSave();
          try {
            return getWasmTableEntry(index)(a1);
          } catch (e2) {
            stackRestore(sp);
            if (e2 !== e2 + 0) throw e2;
            _setThrew(1, 0);
          }
        }
        function invoke_iiiiii(index, a1, a2, a3, a4, a5) {
          var sp = stackSave();
          try {
            return getWasmTableEntry(index)(a1, a2, a3, a4, a5);
          } catch (e2) {
            stackRestore(sp);
            if (e2 !== e2 + 0) throw e2;
            _setThrew(1, 0);
          }
        }
        function run() {
          if (runDependencies > 0) {
            dependenciesFulfilled = run;
            return;
          }
          preRun();
          if (runDependencies > 0) {
            dependenciesFulfilled = run;
            return;
          }
          function doRun() {
            Module["calledRun"] = true;
            if (ABORT) return;
            initRuntime();
            readyPromiseResolve?.(Module);
            Module["onRuntimeInitialized"]?.();
            postRun();
          }
          if (Module["setStatus"]) {
            Module["setStatus"]("Running...");
            setTimeout(() => {
              setTimeout(() => Module["setStatus"](""), 1);
              doRun();
            }, 1);
          } else {
            doRun();
          }
        }
        function preInit() {
          if (Module["preInit"]) {
            if (typeof Module["preInit"] == "function") Module["preInit"] = [Module["preInit"]];
            while (Module["preInit"].length > 0) {
              Module["preInit"].shift()();
            }
          }
        }
        preInit();
        run();
        if (runtimeInitialized) {
          moduleRtn = Module;
        } else {
          moduleRtn = new Promise((resolve, reject) => {
            readyPromiseResolve = resolve;
            readyPromiseReject = reject;
          });
        }
        return moduleRtn;
      };
    })();
    if (typeof exports === "object" && typeof module === "object") {
      module.exports = createAecModule;
      module.exports.default = createAecModule;
    } else if (typeof define === "function" && define["amd"])
      define([], () => createAecModule);
  }
});

// src/ui/listen/audioCore/listenCapture.js
var require_listenCapture = __commonJS({
  "src/ui/listen/audioCore/listenCapture.js"(exports, module) {
    var createAecModule = require_aec();
    var aecModPromise = null;
    var aecMod = null;
    var aecPtr = 0;
    async function getAec() {
      if (aecModPromise) return aecModPromise;
      aecModPromise = createAecModule().then((M2) => {
        aecMod = M2;
        console.log("WASM Module Loaded:", M2);
        M2.newPtr = M2.cwrap(
          "AecNew",
          "number",
          ["number", "number", "number", "number"]
        );
        M2.cancel = M2.cwrap(
          "AecCancelEcho",
          null,
          ["number", "number", "number", "number", "number"]
        );
        M2.destroy = M2.cwrap("AecDestroy", null, ["number"]);
        return M2;
      });
      return aecModPromise;
    }
    var SAMPLE_RATE = 24e3;
    var AUDIO_CHUNK_DURATION = 0.1;
    var BUFFER_SIZE = 4096;
    var isLinux = window.api.platform.isLinux;
    var isMacOS = window.api.platform.isMacOS;
    var mediaStream = null;
    var micMediaStream = null;
    var audioContext = null;
    var audioProcessor = null;
    var systemAudioContext = null;
    var systemAudioProcessor = null;
    var systemAudioBuffer = [];
    var MAX_SYSTEM_BUFFER_SIZE = 10;
    function base64ToFloat32Array(base64) {
      const binaryString = atob(base64);
      const bytes = new Uint8Array(binaryString.length);
      for (let i2 = 0; i2 < binaryString.length; i2++) {
        bytes[i2] = binaryString.charCodeAt(i2);
      }
      const int16Array = new Int16Array(bytes.buffer);
      const float32Array = new Float32Array(int16Array.length);
      for (let i2 = 0; i2 < int16Array.length; i2++) {
        float32Array[i2] = int16Array[i2] / 32768;
      }
      return float32Array;
    }
    function convertFloat32ToInt16(float32Array) {
      const int16Array = new Int16Array(float32Array.length);
      for (let i2 = 0; i2 < float32Array.length; i2++) {
        const s2 = Math.max(-1, Math.min(1, float32Array[i2]));
        int16Array[i2] = s2 < 0 ? s2 * 32768 : s2 * 32767;
      }
      return int16Array;
    }
    function arrayBufferToBase64(buffer) {
      let binary = "";
      const bytes = new Uint8Array(buffer);
      const len = bytes.byteLength;
      for (let i2 = 0; i2 < len; i2++) {
        binary += String.fromCharCode(bytes[i2]);
      }
      return btoa(binary);
    }
    function int16PtrFromFloat32(mod, f32) {
      const len = f32.length;
      const bytes = len * 2;
      const ptr = mod._malloc(bytes);
      const heapBuf = mod.HEAP16 ? mod.HEAP16.buffer : mod.HEAPU8.buffer;
      const i16 = new Int16Array(heapBuf, ptr, len);
      for (let i2 = 0; i2 < len; ++i2) {
        const s2 = Math.max(-1, Math.min(1, f32[i2]));
        i16[i2] = s2 < 0 ? s2 * 32768 : s2 * 32767;
      }
      return { ptr, view: i16 };
    }
    function float32FromInt16View(i16) {
      const out = new Float32Array(i16.length);
      for (let i2 = 0; i2 < i16.length; ++i2) out[i2] = i16[i2] / 32768;
      return out;
    }
    function disposeAec() {
      getAec().then((mod) => {
        if (aecPtr) mod.destroy(aecPtr);
      });
    }
    function runAecSync(micF32, sysF32) {
      if (!aecMod || !aecPtr || !aecMod.HEAPU8) {
        return micF32;
      }
      const frameSize = 160;
      const numFrames = Math.floor(micF32.length / frameSize);
      const processedF32 = new Float32Array(micF32.length);
      let alignedSysF32 = new Float32Array(micF32.length);
      if (sysF32.length > 0) {
        const lengthToCopy = Math.min(micF32.length, sysF32.length);
        alignedSysF32.set(sysF32.slice(0, lengthToCopy));
      }
      for (let i2 = 0; i2 < numFrames; i2++) {
        const offset = i2 * frameSize;
        const micFrame = micF32.subarray(offset, offset + frameSize);
        const echoFrame = alignedSysF32.subarray(offset, offset + frameSize);
        const micPtr = int16PtrFromFloat32(aecMod, micFrame);
        const echoPtr = int16PtrFromFloat32(aecMod, echoFrame);
        const outPtr = aecMod._malloc(frameSize * 2);
        aecMod.cancel(aecPtr, micPtr.ptr, echoPtr.ptr, outPtr, frameSize);
        const heapBuf = aecMod.HEAP16 ? aecMod.HEAP16.buffer : aecMod.HEAPU8.buffer;
        const outFrameI16 = new Int16Array(heapBuf, outPtr, frameSize);
        const outFrameF32 = float32FromInt16View(outFrameI16);
        processedF32.set(outFrameF32, offset);
        aecMod._free(micPtr.ptr);
        aecMod._free(echoPtr.ptr);
        aecMod._free(outPtr);
      }
      return processedF32;
    }
    window.api.listenCapture.onSystemAudioData((event, { data }) => {
      systemAudioBuffer.push({
        data,
        timestamp: Date.now()
      });
      if (systemAudioBuffer.length > MAX_SYSTEM_BUFFER_SIZE) {
        systemAudioBuffer = systemAudioBuffer.slice(-MAX_SYSTEM_BUFFER_SIZE);
      }
    });
    var tokenTracker = {
      tokens: [],
      audioStartTime: null,
      addTokens(count, type = "image") {
        const now = Date.now();
        this.tokens.push({
          timestamp: now,
          count,
          type
        });
        this.cleanOldTokens();
      },
      calculateImageTokens(width, height) {
        const pixels = width * height;
        if (pixels <= 384 * 384) {
          return 85;
        }
        const tiles = Math.ceil(pixels / (768 * 768));
        return tiles * 85;
      },
      trackAudioTokens() {
        if (!this.audioStartTime) {
          this.audioStartTime = Date.now();
          return;
        }
        const now = Date.now();
        const elapsedSeconds = (now - this.audioStartTime) / 1e3;
        const audioTokens = Math.floor(elapsedSeconds * 16);
        if (audioTokens > 0) {
          this.addTokens(audioTokens, "audio");
          this.audioStartTime = now;
        }
      },
      cleanOldTokens() {
        const oneMinuteAgo = Date.now() - 60 * 1e3;
        this.tokens = this.tokens.filter((token) => token.timestamp > oneMinuteAgo);
      },
      getTokensInLastMinute() {
        this.cleanOldTokens();
        return this.tokens.reduce((total, token) => total + token.count, 0);
      },
      shouldThrottle() {
        const throttleEnabled = localStorage.getItem("throttleTokens") === "true";
        if (!throttleEnabled) {
          return false;
        }
        const maxTokensPerMin = parseInt(localStorage.getItem("maxTokensPerMin") || "500000", 10);
        const throttleAtPercent = parseInt(localStorage.getItem("throttleAtPercent") || "75", 10);
        const currentTokens = this.getTokensInLastMinute();
        const throttleThreshold = Math.floor(maxTokensPerMin * throttleAtPercent / 100);
        console.log(`Token check: ${currentTokens}/${maxTokensPerMin} (throttle at ${throttleThreshold})`);
        return currentTokens >= throttleThreshold;
      },
      // Reset the tracker
      reset() {
        this.tokens = [];
        this.audioStartTime = null;
      }
    };
    setInterval(() => {
      tokenTracker.trackAudioTokens();
    }, 2e3);
    async function setupMicProcessing(micStream) {
      const mod = await getAec();
      if (!aecPtr) aecPtr = mod.newPtr(160, 1600, 24e3, 1);
      const micAudioContext = new AudioContext({ sampleRate: SAMPLE_RATE });
      await micAudioContext.resume();
      const micSource = micAudioContext.createMediaStreamSource(micStream);
      const micProcessor = micAudioContext.createScriptProcessor(BUFFER_SIZE, 1, 1);
      let audioBuffer = [];
      const samplesPerChunk = SAMPLE_RATE * AUDIO_CHUNK_DURATION;
      micProcessor.onaudioprocess = (e2) => {
        const inputData = e2.inputBuffer.getChannelData(0);
        audioBuffer.push(...inputData);
        while (audioBuffer.length >= samplesPerChunk) {
          let chunk = audioBuffer.splice(0, samplesPerChunk);
          let processedChunk = new Float32Array(chunk);
          if (systemAudioBuffer.length > 0) {
            const latest = systemAudioBuffer[systemAudioBuffer.length - 1];
            const sysF32 = base64ToFloat32Array(latest.data);
            processedChunk = runAecSync(new Float32Array(chunk), sysF32);
          } else {
            console.log("\u{1F50A} No system audio for AEC reference");
          }
          const pcm16 = convertFloat32ToInt16(processedChunk);
          const b64 = arrayBufferToBase64(pcm16.buffer);
          window.api.listenCapture.sendMicAudioContent({
            data: b64,
            mimeType: "audio/pcm;rate=24000"
          });
        }
      };
      micSource.connect(micProcessor);
      micProcessor.connect(micAudioContext.destination);
      audioProcessor = micProcessor;
      return { context: micAudioContext, processor: micProcessor };
    }
    function setupLinuxMicProcessing(micStream) {
      const micAudioContext = new AudioContext({ sampleRate: SAMPLE_RATE });
      const micSource = micAudioContext.createMediaStreamSource(micStream);
      const micProcessor = micAudioContext.createScriptProcessor(BUFFER_SIZE, 1, 1);
      let audioBuffer = [];
      const samplesPerChunk = SAMPLE_RATE * AUDIO_CHUNK_DURATION;
      micProcessor.onaudioprocess = async (e2) => {
        const inputData = e2.inputBuffer.getChannelData(0);
        audioBuffer.push(...inputData);
        while (audioBuffer.length >= samplesPerChunk) {
          const chunk = audioBuffer.splice(0, samplesPerChunk);
          const pcmData16 = convertFloat32ToInt16(chunk);
          const base64Data = arrayBufferToBase64(pcmData16.buffer);
          await window.api.listenCapture.sendMicAudioContent({
            data: base64Data,
            mimeType: "audio/pcm;rate=24000"
          });
        }
      };
      micSource.connect(micProcessor);
      micProcessor.connect(micAudioContext.destination);
      audioProcessor = micProcessor;
    }
    function setupSystemAudioProcessing(systemStream) {
      const systemAudioContext2 = new AudioContext({ sampleRate: SAMPLE_RATE });
      const systemSource = systemAudioContext2.createMediaStreamSource(systemStream);
      const systemProcessor = systemAudioContext2.createScriptProcessor(BUFFER_SIZE, 1, 1);
      let audioBuffer = [];
      const samplesPerChunk = SAMPLE_RATE * AUDIO_CHUNK_DURATION;
      systemProcessor.onaudioprocess = async (e2) => {
        const inputData = e2.inputBuffer.getChannelData(0);
        if (!inputData || inputData.length === 0) return;
        audioBuffer.push(...inputData);
        while (audioBuffer.length >= samplesPerChunk) {
          const chunk = audioBuffer.splice(0, samplesPerChunk);
          const pcmData16 = convertFloat32ToInt16(chunk);
          const base64Data = arrayBufferToBase64(pcmData16.buffer);
          try {
            await window.api.listenCapture.sendSystemAudioContent({
              data: base64Data,
              mimeType: "audio/pcm;rate=24000"
            });
          } catch (error) {
            console.error("Failed to send system audio:", error);
          }
        }
      };
      systemSource.connect(systemProcessor);
      systemProcessor.connect(systemAudioContext2.destination);
      return { context: systemAudioContext2, processor: systemProcessor };
    }
    async function startCapture(screenshotIntervalSeconds = 5, imageQuality = "medium") {
      tokenTracker.reset();
      console.log("\u{1F3AF} Token tracker reset for new capture session");
      try {
        if (isMacOS) {
          const sessionActive = await window.api.listenCapture.isSessionActive();
          if (!sessionActive) {
            throw new Error("STT sessions not initialized - please wait for initialization to complete");
          }
          console.log("Starting macOS capture with SystemAudioDump...");
          const audioResult = await window.api.listenCapture.startMacosSystemAudio();
          if (!audioResult.success) {
            console.warn("[listenCapture] macOS audio start failed:", audioResult.error);
            if (audioResult.error === "already_running") {
              await window.api.listenCapture.stopMacosSystemAudio();
              await new Promise((r2) => setTimeout(r2, 500));
              const retry = await window.api.listenCapture.startMacosSystemAudio();
              if (!retry.success) {
                throw new Error("Retry failed: " + retry.error);
              }
            } else {
              throw new Error("Failed to start macOS audio capture: " + audioResult.error);
            }
          }
          try {
            micMediaStream = await navigator.mediaDevices.getUserMedia({
              audio: {
                sampleRate: SAMPLE_RATE,
                channelCount: 1,
                echoCancellation: true,
                noiseSuppression: true,
                autoGainControl: true
              },
              video: false
            });
            console.log("macOS microphone capture started");
            const { context, processor } = await setupMicProcessing(micMediaStream);
            audioContext = context;
            audioProcessor = processor;
          } catch (micErr) {
            console.warn("Failed to get microphone on macOS:", micErr);
          }
          console.log("macOS screen capture started - audio handled by SystemAudioDump");
        } else if (isLinux) {
          const sessionActive = await window.api.listenCapture.isSessionActive();
          if (!sessionActive) {
            throw new Error("STT sessions not initialized - please wait for initialization to complete");
          }
          mediaStream = await navigator.mediaDevices.getDisplayMedia({
            video: {
              frameRate: 1,
              width: { ideal: 1920 },
              height: { ideal: 1080 }
            },
            audio: false
            // Don't use system audio loopback on Linux
          });
          let micMediaStream2 = null;
          try {
            micMediaStream2 = await navigator.mediaDevices.getUserMedia({
              audio: {
                sampleRate: SAMPLE_RATE,
                channelCount: 1,
                echoCancellation: true,
                noiseSuppression: true,
                autoGainControl: true
              },
              video: false
            });
            console.log("Linux microphone capture started");
            setupLinuxMicProcessing(micMediaStream2);
          } catch (micError) {
            console.warn("Failed to get microphone access on Linux:", micError);
          }
          console.log("Linux screen capture started");
        } else {
          console.log("Starting Windows capture with native loopback audio...");
          const sessionActive = await window.api.listenCapture.isSessionActive();
          if (!sessionActive) {
            throw new Error("STT sessions not initialized - please wait for initialization to complete");
          }
          try {
            micMediaStream = await navigator.mediaDevices.getUserMedia({
              audio: {
                sampleRate: SAMPLE_RATE,
                channelCount: 1,
                echoCancellation: true,
                noiseSuppression: true,
                autoGainControl: true
              },
              video: false
            });
            console.log("Windows microphone capture started");
            const { context, processor } = await setupMicProcessing(micMediaStream);
            audioContext = context;
            audioProcessor = processor;
          } catch (micErr) {
            console.warn("Could not get microphone access on Windows:", micErr);
          }
          try {
            mediaStream = await navigator.mediaDevices.getDisplayMedia({
              video: true,
              audio: true
              // This will now use native loopback from our handler
            });
            const audioTracks = mediaStream.getAudioTracks();
            if (audioTracks.length === 0) {
              throw new Error("No audio track in native loopback stream");
            }
            console.log("Windows native loopback audio capture started");
            const { context, processor } = setupSystemAudioProcessing(mediaStream);
            systemAudioContext = context;
            systemAudioProcessor = processor;
          } catch (sysAudioErr) {
            console.error("Failed to start Windows native loopback audio:", sysAudioErr);
          }
        }
      } catch (err) {
        console.error("Error starting capture:", err);
      }
    }
    function stopCapture() {
      if (audioProcessor) {
        audioProcessor.disconnect();
        audioProcessor = null;
      }
      if (audioContext) {
        audioContext.close();
        audioContext = null;
      }
      if (systemAudioProcessor) {
        systemAudioProcessor.disconnect();
        systemAudioProcessor = null;
      }
      if (systemAudioContext) {
        systemAudioContext.close();
        systemAudioContext = null;
      }
      if (mediaStream) {
        mediaStream.getTracks().forEach((track) => track.stop());
        mediaStream = null;
      }
      if (micMediaStream) {
        micMediaStream.getTracks().forEach((t2) => t2.stop());
        micMediaStream = null;
      }
      if (isMacOS) {
        window.api.listenCapture.stopMacosSystemAudio().catch((err) => {
          console.error("Error stopping macOS audio:", err);
        });
      }
    }
    module.exports = {
      getAec,
      // 새로 만든 초기화 함수
      runAecSync,
      // sync 버전
      disposeAec,
      // 필요시 Rust 객체 파괴
      startCapture,
      stopCapture,
      isLinux,
      isMacOS
    };
    if (typeof window !== "undefined") {
      window.listenCapture = module.exports;
      window.pickleGlass = window.pickleGlass || {};
      window.pickleGlass.startCapture = startCapture;
      window.pickleGlass.stopCapture = stopCapture;
    }
  }
});

// src/ui/assets/lit-core-2.7.4.min.js
var t = window;
var i = t.ShadowRoot && (void 0 === t.ShadyCSS || t.ShadyCSS.nativeShadow) && "adoptedStyleSheets" in Document.prototype && "replace" in CSSStyleSheet.prototype;
var s = Symbol();
var e = /* @__PURE__ */ new WeakMap();
var o = class {
  constructor(t2, e2, i2) {
    if (this._$cssResult$ = true, i2 !== s) throw Error("CSSResult is not constructable. Use `unsafeCSS` or `css` instead.");
    this.cssText = t2, this.t = e2;
  }
  get styleSheet() {
    let t2 = this.i;
    const s2 = this.t;
    if (i && void 0 === t2) {
      const i2 = void 0 !== s2 && 1 === s2.length;
      i2 && (t2 = e.get(s2)), void 0 === t2 && ((this.i = t2 = new CSSStyleSheet()).replaceSync(this.cssText), i2 && e.set(s2, t2));
    }
    return t2;
  }
  toString() {
    return this.cssText;
  }
};
var n = (t2) => new o("string" == typeof t2 ? t2 : t2 + "", void 0, s);
var r = (t2, ...e2) => {
  const i2 = 1 === t2.length ? t2[0] : e2.reduce((e3, s2, i3) => e3 + ((t3) => {
    if (true === t3._$cssResult$) return t3.cssText;
    if ("number" == typeof t3) return t3;
    throw Error("Value passed to 'css' function must be a 'css' function result: " + t3 + ". Use 'unsafeCSS' to pass non-literal values, but take care to ensure page security.");
  })(s2) + t2[i3 + 1], t2[0]);
  return new o(i2, t2, s);
};
var h = (e2, s2) => {
  i ? e2.adoptedStyleSheets = s2.map((t2) => t2 instanceof CSSStyleSheet ? t2 : t2.styleSheet) : s2.forEach((s3) => {
    const i2 = document.createElement("style"), n2 = t.litNonce;
    void 0 !== n2 && i2.setAttribute("nonce", n2), i2.textContent = s3.cssText, e2.appendChild(i2);
  });
};
var l = i ? (t2) => t2 : (t2) => t2 instanceof CSSStyleSheet ? ((t3) => {
  let e2 = "";
  for (const s2 of t3.cssRules) e2 += s2.cssText;
  return n(e2);
})(t2) : t2;
var a;
var u = window;
var c = u.trustedTypes;
var d = c ? c.emptyScript : "";
var v = u.reactiveElementPolyfillSupport;
var p = { toAttribute(t2, e2) {
  switch (e2) {
    case Boolean:
      t2 = t2 ? d : null;
      break;
    case Object:
    case Array:
      t2 = null == t2 ? t2 : JSON.stringify(t2);
  }
  return t2;
}, fromAttribute(t2, e2) {
  let s2 = t2;
  switch (e2) {
    case Boolean:
      s2 = null !== t2;
      break;
    case Number:
      s2 = null === t2 ? null : Number(t2);
      break;
    case Object:
    case Array:
      try {
        s2 = JSON.parse(t2);
      } catch (t3) {
        s2 = null;
      }
  }
  return s2;
} };
var f = (t2, e2) => e2 !== t2 && (e2 == e2 || t2 == t2);
var m = { attribute: true, type: String, converter: p, reflect: false, hasChanged: f };
var y = "finalized";
var _ = class extends HTMLElement {
  constructor() {
    super(), this.o = /* @__PURE__ */ new Map(), this.isUpdatePending = false, this.hasUpdated = false, this.l = null, this.u();
  }
  static addInitializer(t2) {
    var e2;
    this.finalize(), (null !== (e2 = this.v) && void 0 !== e2 ? e2 : this.v = []).push(t2);
  }
  static get observedAttributes() {
    this.finalize();
    const t2 = [];
    return this.elementProperties.forEach((e2, s2) => {
      const i2 = this.p(s2, e2);
      void 0 !== i2 && (this.m.set(i2, s2), t2.push(i2));
    }), t2;
  }
  static createProperty(t2, e2 = m) {
    if (e2.state && (e2.attribute = false), this.finalize(), this.elementProperties.set(t2, e2), !e2.noAccessor && !this.prototype.hasOwnProperty(t2)) {
      const s2 = "symbol" == typeof t2 ? Symbol() : "__" + t2, i2 = this.getPropertyDescriptor(t2, s2, e2);
      void 0 !== i2 && Object.defineProperty(this.prototype, t2, i2);
    }
  }
  static getPropertyDescriptor(t2, e2, s2) {
    return { get() {
      return this[e2];
    }, set(i2) {
      const n2 = this[t2];
      this[e2] = i2, this.requestUpdate(t2, n2, s2);
    }, configurable: true, enumerable: true };
  }
  static getPropertyOptions(t2) {
    return this.elementProperties.get(t2) || m;
  }
  static finalize() {
    if (this.hasOwnProperty(y)) return false;
    this[y] = true;
    const t2 = Object.getPrototypeOf(this);
    if (t2.finalize(), void 0 !== t2.v && (this.v = [...t2.v]), this.elementProperties = new Map(t2.elementProperties), this.m = /* @__PURE__ */ new Map(), this.hasOwnProperty("properties")) {
      const t3 = this.properties, e2 = [...Object.getOwnPropertyNames(t3), ...Object.getOwnPropertySymbols(t3)];
      for (const s2 of e2) this.createProperty(s2, t3[s2]);
    }
    return this.elementStyles = this.finalizeStyles(this.styles), true;
  }
  static finalizeStyles(t2) {
    const e2 = [];
    if (Array.isArray(t2)) {
      const s2 = new Set(t2.flat(1 / 0).reverse());
      for (const t3 of s2) e2.unshift(l(t3));
    } else void 0 !== t2 && e2.push(l(t2));
    return e2;
  }
  static p(t2, e2) {
    const s2 = e2.attribute;
    return false === s2 ? void 0 : "string" == typeof s2 ? s2 : "string" == typeof t2 ? t2.toLowerCase() : void 0;
  }
  u() {
    var t2;
    this._ = new Promise((t3) => this.enableUpdating = t3), this._$AL = /* @__PURE__ */ new Map(), this.g(), this.requestUpdate(), null === (t2 = this.constructor.v) || void 0 === t2 || t2.forEach((t3) => t3(this));
  }
  addController(t2) {
    var e2, s2;
    (null !== (e2 = this.S) && void 0 !== e2 ? e2 : this.S = []).push(t2), void 0 !== this.renderRoot && this.isConnected && (null === (s2 = t2.hostConnected) || void 0 === s2 || s2.call(t2));
  }
  removeController(t2) {
    var e2;
    null === (e2 = this.S) || void 0 === e2 || e2.splice(this.S.indexOf(t2) >>> 0, 1);
  }
  g() {
    this.constructor.elementProperties.forEach((t2, e2) => {
      this.hasOwnProperty(e2) && (this.o.set(e2, this[e2]), delete this[e2]);
    });
  }
  createRenderRoot() {
    var t2;
    const e2 = null !== (t2 = this.shadowRoot) && void 0 !== t2 ? t2 : this.attachShadow(this.constructor.shadowRootOptions);
    return h(e2, this.constructor.elementStyles), e2;
  }
  connectedCallback() {
    var t2;
    void 0 === this.renderRoot && (this.renderRoot = this.createRenderRoot()), this.enableUpdating(true), null === (t2 = this.S) || void 0 === t2 || t2.forEach((t3) => {
      var e2;
      return null === (e2 = t3.hostConnected) || void 0 === e2 ? void 0 : e2.call(t3);
    });
  }
  enableUpdating(t2) {
  }
  disconnectedCallback() {
    var t2;
    null === (t2 = this.S) || void 0 === t2 || t2.forEach((t3) => {
      var e2;
      return null === (e2 = t3.hostDisconnected) || void 0 === e2 ? void 0 : e2.call(t3);
    });
  }
  attributeChangedCallback(t2, e2, s2) {
    this._$AK(t2, s2);
  }
  $(t2, e2, s2 = m) {
    var i2;
    const n2 = this.constructor.p(t2, s2);
    if (void 0 !== n2 && true === s2.reflect) {
      const o2 = (void 0 !== (null === (i2 = s2.converter) || void 0 === i2 ? void 0 : i2.toAttribute) ? s2.converter : p).toAttribute(e2, s2.type);
      this.l = t2, null == o2 ? this.removeAttribute(n2) : this.setAttribute(n2, o2), this.l = null;
    }
  }
  _$AK(t2, e2) {
    var s2;
    const i2 = this.constructor, n2 = i2.m.get(t2);
    if (void 0 !== n2 && this.l !== n2) {
      const t3 = i2.getPropertyOptions(n2), o2 = "function" == typeof t3.converter ? { fromAttribute: t3.converter } : void 0 !== (null === (s2 = t3.converter) || void 0 === s2 ? void 0 : s2.fromAttribute) ? t3.converter : p;
      this.l = n2, this[n2] = o2.fromAttribute(e2, t3.type), this.l = null;
    }
  }
  requestUpdate(t2, e2, s2) {
    let i2 = true;
    void 0 !== t2 && (((s2 = s2 || this.constructor.getPropertyOptions(t2)).hasChanged || f)(this[t2], e2) ? (this._$AL.has(t2) || this._$AL.set(t2, e2), true === s2.reflect && this.l !== t2 && (void 0 === this.C && (this.C = /* @__PURE__ */ new Map()), this.C.set(t2, s2))) : i2 = false), !this.isUpdatePending && i2 && (this._ = this.T());
  }
  async T() {
    this.isUpdatePending = true;
    try {
      await this._;
    } catch (t3) {
      Promise.reject(t3);
    }
    const t2 = this.scheduleUpdate();
    return null != t2 && await t2, !this.isUpdatePending;
  }
  scheduleUpdate() {
    return this.performUpdate();
  }
  performUpdate() {
    var t2;
    if (!this.isUpdatePending) return;
    this.hasUpdated, this.o && (this.o.forEach((t3, e3) => this[e3] = t3), this.o = void 0);
    let e2 = false;
    const s2 = this._$AL;
    try {
      e2 = this.shouldUpdate(s2), e2 ? (this.willUpdate(s2), null === (t2 = this.S) || void 0 === t2 || t2.forEach((t3) => {
        var e3;
        return null === (e3 = t3.hostUpdate) || void 0 === e3 ? void 0 : e3.call(t3);
      }), this.update(s2)) : this.P();
    } catch (t3) {
      throw e2 = false, this.P(), t3;
    }
    e2 && this._$AE(s2);
  }
  willUpdate(t2) {
  }
  _$AE(t2) {
    var e2;
    null === (e2 = this.S) || void 0 === e2 || e2.forEach((t3) => {
      var e3;
      return null === (e3 = t3.hostUpdated) || void 0 === e3 ? void 0 : e3.call(t3);
    }), this.hasUpdated || (this.hasUpdated = true, this.firstUpdated(t2)), this.updated(t2);
  }
  P() {
    this._$AL = /* @__PURE__ */ new Map(), this.isUpdatePending = false;
  }
  get updateComplete() {
    return this.getUpdateComplete();
  }
  getUpdateComplete() {
    return this._;
  }
  shouldUpdate(t2) {
    return true;
  }
  update(t2) {
    void 0 !== this.C && (this.C.forEach((t3, e2) => this.$(e2, this[e2], t3)), this.C = void 0), this.P();
  }
  updated(t2) {
  }
  firstUpdated(t2) {
  }
};
var b;
_[y] = true, _.elementProperties = /* @__PURE__ */ new Map(), _.elementStyles = [], _.shadowRootOptions = { mode: "open" }, null == v || v({ ReactiveElement: _ }), (null !== (a = u.reactiveElementVersions) && void 0 !== a ? a : u.reactiveElementVersions = []).push("1.6.1");
var g = window;
var w = g.trustedTypes;
var S = w ? w.createPolicy("lit-html", { createHTML: (t2) => t2 }) : void 0;
var $ = "$lit$";
var C = `lit$${(Math.random() + "").slice(9)}$`;
var T = "?" + C;
var P = `<${T}>`;
var x = document;
var A = () => x.createComment("");
var k = (t2) => null === t2 || "object" != typeof t2 && "function" != typeof t2;
var E = Array.isArray;
var M = (t2) => E(t2) || "function" == typeof (null == t2 ? void 0 : t2[Symbol.iterator]);
var U = "[ 	\n\f\r]";
var N = /<(?:(!--|\/[^a-zA-Z])|(\/?[a-zA-Z][^>\s]*)|(\/?$))/g;
var R = /-->/g;
var O = />/g;
var V = RegExp(`>|${U}(?:([^\\s"'>=/]+)(${U}*=${U}*(?:[^ 	
\f\r"'\`<>=]|("|')|))|$)`, "g");
var j = /'/g;
var z = /"/g;
var L = /^(?:script|style|textarea|title)$/i;
var I = (t2) => (e2, ...s2) => ({ _$litType$: t2, strings: e2, values: s2 });
var H = I(1);
var B = I(2);
var D = Symbol.for("lit-noChange");
var q = Symbol.for("lit-nothing");
var J = /* @__PURE__ */ new WeakMap();
var W = x.createTreeWalker(x, 129, null, false);
var Z = (t2, e2) => {
  const s2 = t2.length - 1, i2 = [];
  let n2, o2 = 2 === e2 ? "<svg>" : "", r2 = N;
  for (let e3 = 0; e3 < s2; e3++) {
    const s3 = t2[e3];
    let l3, h2, a2 = -1, d2 = 0;
    for (; d2 < s3.length && (r2.lastIndex = d2, h2 = r2.exec(s3), null !== h2); ) d2 = r2.lastIndex, r2 === N ? "!--" === h2[1] ? r2 = R : void 0 !== h2[1] ? r2 = O : void 0 !== h2[2] ? (L.test(h2[2]) && (n2 = RegExp("</" + h2[2], "g")), r2 = V) : void 0 !== h2[3] && (r2 = V) : r2 === V ? ">" === h2[0] ? (r2 = null != n2 ? n2 : N, a2 = -1) : void 0 === h2[1] ? a2 = -2 : (a2 = r2.lastIndex - h2[2].length, l3 = h2[1], r2 = void 0 === h2[3] ? V : '"' === h2[3] ? z : j) : r2 === z || r2 === j ? r2 = V : r2 === R || r2 === O ? r2 = N : (r2 = V, n2 = void 0);
    const c2 = r2 === V && t2[e3 + 1].startsWith("/>") ? " " : "";
    o2 += r2 === N ? s3 + P : a2 >= 0 ? (i2.push(l3), s3.slice(0, a2) + $ + s3.slice(a2) + C + c2) : s3 + C + (-2 === a2 ? (i2.push(void 0), e3) : c2);
  }
  const l2 = o2 + (t2[s2] || "<?>") + (2 === e2 ? "</svg>" : "");
  if (!Array.isArray(t2) || !t2.hasOwnProperty("raw")) throw Error("invalid template strings array");
  return [void 0 !== S ? S.createHTML(l2) : l2, i2];
};
var F = class _F {
  constructor({ strings: t2, _$litType$: e2 }, s2) {
    let i2;
    this.parts = [];
    let n2 = 0, o2 = 0;
    const r2 = t2.length - 1, l2 = this.parts, [h2, a2] = Z(t2, e2);
    if (this.el = _F.createElement(h2, s2), W.currentNode = this.el.content, 2 === e2) {
      const t3 = this.el.content, e3 = t3.firstChild;
      e3.remove(), t3.append(...e3.childNodes);
    }
    for (; null !== (i2 = W.nextNode()) && l2.length < r2; ) {
      if (1 === i2.nodeType) {
        if (i2.hasAttributes()) {
          const t3 = [];
          for (const e3 of i2.getAttributeNames()) if (e3.endsWith($) || e3.startsWith(C)) {
            const s3 = a2[o2++];
            if (t3.push(e3), void 0 !== s3) {
              const t4 = i2.getAttribute(s3.toLowerCase() + $).split(C), e4 = /([.?@])?(.*)/.exec(s3);
              l2.push({ type: 1, index: n2, name: e4[2], strings: t4, ctor: "." === e4[1] ? Y : "?" === e4[1] ? it : "@" === e4[1] ? st : X });
            } else l2.push({ type: 6, index: n2 });
          }
          for (const e3 of t3) i2.removeAttribute(e3);
        }
        if (L.test(i2.tagName)) {
          const t3 = i2.textContent.split(C), e3 = t3.length - 1;
          if (e3 > 0) {
            i2.textContent = w ? w.emptyScript : "";
            for (let s3 = 0; s3 < e3; s3++) i2.append(t3[s3], A()), W.nextNode(), l2.push({ type: 2, index: ++n2 });
            i2.append(t3[e3], A());
          }
        }
      } else if (8 === i2.nodeType) if (i2.data === T) l2.push({ type: 2, index: n2 });
      else {
        let t3 = -1;
        for (; -1 !== (t3 = i2.data.indexOf(C, t3 + 1)); ) l2.push({ type: 7, index: n2 }), t3 += C.length - 1;
      }
      n2++;
    }
  }
  static createElement(t2, e2) {
    const s2 = x.createElement("template");
    return s2.innerHTML = t2, s2;
  }
};
function G(t2, e2, s2 = t2, i2) {
  var n2, o2, r2, l2;
  if (e2 === D) return e2;
  let h2 = void 0 !== i2 ? null === (n2 = s2.A) || void 0 === n2 ? void 0 : n2[i2] : s2.k;
  const a2 = k(e2) ? void 0 : e2._$litDirective$;
  return (null == h2 ? void 0 : h2.constructor) !== a2 && (null === (o2 = null == h2 ? void 0 : h2._$AO) || void 0 === o2 || o2.call(h2, false), void 0 === a2 ? h2 = void 0 : (h2 = new a2(t2), h2._$AT(t2, s2, i2)), void 0 !== i2 ? (null !== (r2 = (l2 = s2).A) && void 0 !== r2 ? r2 : l2.A = [])[i2] = h2 : s2.k = h2), void 0 !== h2 && (e2 = G(t2, h2._$AS(t2, e2.values), h2, i2)), e2;
}
var K = class {
  constructor(t2, e2) {
    this._$AV = [], this._$AN = void 0, this._$AD = t2, this._$AM = e2;
  }
  get parentNode() {
    return this._$AM.parentNode;
  }
  get _$AU() {
    return this._$AM._$AU;
  }
  M(t2) {
    var e2;
    const { el: { content: s2 }, parts: i2 } = this._$AD, n2 = (null !== (e2 = null == t2 ? void 0 : t2.creationScope) && void 0 !== e2 ? e2 : x).importNode(s2, true);
    W.currentNode = n2;
    let o2 = W.nextNode(), r2 = 0, l2 = 0, h2 = i2[0];
    for (; void 0 !== h2; ) {
      if (r2 === h2.index) {
        let e3;
        2 === h2.type ? e3 = new Q(o2, o2.nextSibling, this, t2) : 1 === h2.type ? e3 = new h2.ctor(o2, h2.name, h2.strings, this, t2) : 6 === h2.type && (e3 = new et(o2, this, t2)), this._$AV.push(e3), h2 = i2[++l2];
      }
      r2 !== (null == h2 ? void 0 : h2.index) && (o2 = W.nextNode(), r2++);
    }
    return n2;
  }
  U(t2) {
    let e2 = 0;
    for (const s2 of this._$AV) void 0 !== s2 && (void 0 !== s2.strings ? (s2._$AI(t2, s2, e2), e2 += s2.strings.length - 2) : s2._$AI(t2[e2])), e2++;
  }
};
var Q = class _Q {
  constructor(t2, e2, s2, i2) {
    var n2;
    this.type = 2, this._$AH = q, this._$AN = void 0, this._$AA = t2, this._$AB = e2, this._$AM = s2, this.options = i2, this.N = null === (n2 = null == i2 ? void 0 : i2.isConnected) || void 0 === n2 || n2;
  }
  get _$AU() {
    var t2, e2;
    return null !== (e2 = null === (t2 = this._$AM) || void 0 === t2 ? void 0 : t2._$AU) && void 0 !== e2 ? e2 : this.N;
  }
  get parentNode() {
    let t2 = this._$AA.parentNode;
    const e2 = this._$AM;
    return void 0 !== e2 && 11 === (null == t2 ? void 0 : t2.nodeType) && (t2 = e2.parentNode), t2;
  }
  get startNode() {
    return this._$AA;
  }
  get endNode() {
    return this._$AB;
  }
  _$AI(t2, e2 = this) {
    t2 = G(this, t2, e2), k(t2) ? t2 === q || null == t2 || "" === t2 ? (this._$AH !== q && this._$AR(), this._$AH = q) : t2 !== this._$AH && t2 !== D && this.R(t2) : void 0 !== t2._$litType$ ? this.O(t2) : void 0 !== t2.nodeType ? this.V(t2) : M(t2) ? this.j(t2) : this.R(t2);
  }
  L(t2) {
    return this._$AA.parentNode.insertBefore(t2, this._$AB);
  }
  V(t2) {
    this._$AH !== t2 && (this._$AR(), this._$AH = this.L(t2));
  }
  R(t2) {
    this._$AH !== q && k(this._$AH) ? this._$AA.nextSibling.data = t2 : this.V(x.createTextNode(t2)), this._$AH = t2;
  }
  O(t2) {
    var e2;
    const { values: s2, _$litType$: i2 } = t2, n2 = "number" == typeof i2 ? this._$AC(t2) : (void 0 === i2.el && (i2.el = F.createElement(i2.h, this.options)), i2);
    if ((null === (e2 = this._$AH) || void 0 === e2 ? void 0 : e2._$AD) === n2) this._$AH.U(s2);
    else {
      const t3 = new K(n2, this), e3 = t3.M(this.options);
      t3.U(s2), this.V(e3), this._$AH = t3;
    }
  }
  _$AC(t2) {
    let e2 = J.get(t2.strings);
    return void 0 === e2 && J.set(t2.strings, e2 = new F(t2)), e2;
  }
  j(t2) {
    E(this._$AH) || (this._$AH = [], this._$AR());
    const e2 = this._$AH;
    let s2, i2 = 0;
    for (const n2 of t2) i2 === e2.length ? e2.push(s2 = new _Q(this.L(A()), this.L(A()), this, this.options)) : s2 = e2[i2], s2._$AI(n2), i2++;
    i2 < e2.length && (this._$AR(s2 && s2._$AB.nextSibling, i2), e2.length = i2);
  }
  _$AR(t2 = this._$AA.nextSibling, e2) {
    var s2;
    for (null === (s2 = this._$AP) || void 0 === s2 || s2.call(this, false, true, e2); t2 && t2 !== this._$AB; ) {
      const e3 = t2.nextSibling;
      t2.remove(), t2 = e3;
    }
  }
  setConnected(t2) {
    var e2;
    void 0 === this._$AM && (this.N = t2, null === (e2 = this._$AP) || void 0 === e2 || e2.call(this, t2));
  }
};
var X = class {
  constructor(t2, e2, s2, i2, n2) {
    this.type = 1, this._$AH = q, this._$AN = void 0, this.element = t2, this.name = e2, this._$AM = i2, this.options = n2, s2.length > 2 || "" !== s2[0] || "" !== s2[1] ? (this._$AH = Array(s2.length - 1).fill(new String()), this.strings = s2) : this._$AH = q;
  }
  get tagName() {
    return this.element.tagName;
  }
  get _$AU() {
    return this._$AM._$AU;
  }
  _$AI(t2, e2 = this, s2, i2) {
    const n2 = this.strings;
    let o2 = false;
    if (void 0 === n2) t2 = G(this, t2, e2, 0), o2 = !k(t2) || t2 !== this._$AH && t2 !== D, o2 && (this._$AH = t2);
    else {
      const i3 = t2;
      let r2, l2;
      for (t2 = n2[0], r2 = 0; r2 < n2.length - 1; r2++) l2 = G(this, i3[s2 + r2], e2, r2), l2 === D && (l2 = this._$AH[r2]), o2 || (o2 = !k(l2) || l2 !== this._$AH[r2]), l2 === q ? t2 = q : t2 !== q && (t2 += (null != l2 ? l2 : "") + n2[r2 + 1]), this._$AH[r2] = l2;
    }
    o2 && !i2 && this.I(t2);
  }
  I(t2) {
    t2 === q ? this.element.removeAttribute(this.name) : this.element.setAttribute(this.name, null != t2 ? t2 : "");
  }
};
var Y = class extends X {
  constructor() {
    super(...arguments), this.type = 3;
  }
  I(t2) {
    this.element[this.name] = t2 === q ? void 0 : t2;
  }
};
var tt = w ? w.emptyScript : "";
var it = class extends X {
  constructor() {
    super(...arguments), this.type = 4;
  }
  I(t2) {
    t2 && t2 !== q ? this.element.setAttribute(this.name, tt) : this.element.removeAttribute(this.name);
  }
};
var st = class extends X {
  constructor(t2, e2, s2, i2, n2) {
    super(t2, e2, s2, i2, n2), this.type = 5;
  }
  _$AI(t2, e2 = this) {
    var s2;
    if ((t2 = null !== (s2 = G(this, t2, e2, 0)) && void 0 !== s2 ? s2 : q) === D) return;
    const i2 = this._$AH, n2 = t2 === q && i2 !== q || t2.capture !== i2.capture || t2.once !== i2.once || t2.passive !== i2.passive, o2 = t2 !== q && (i2 === q || n2);
    n2 && this.element.removeEventListener(this.name, this, i2), o2 && this.element.addEventListener(this.name, this, t2), this._$AH = t2;
  }
  handleEvent(t2) {
    var e2, s2;
    "function" == typeof this._$AH ? this._$AH.call(null !== (s2 = null === (e2 = this.options) || void 0 === e2 ? void 0 : e2.host) && void 0 !== s2 ? s2 : this.element, t2) : this._$AH.handleEvent(t2);
  }
};
var et = class {
  constructor(t2, e2, s2) {
    this.element = t2, this.type = 6, this._$AN = void 0, this._$AM = e2, this.options = s2;
  }
  get _$AU() {
    return this._$AM._$AU;
  }
  _$AI(t2) {
    G(this, t2);
  }
};
var nt = g.litHtmlPolyfillSupport;
null == nt || nt(F, Q), (null !== (b = g.litHtmlVersions) && void 0 !== b ? b : g.litHtmlVersions = []).push("2.7.3");
var rt = (t2, e2, s2) => {
  var i2, n2;
  const o2 = null !== (i2 = null == s2 ? void 0 : s2.renderBefore) && void 0 !== i2 ? i2 : e2;
  let r2 = o2._$litPart$;
  if (void 0 === r2) {
    const t3 = null !== (n2 = null == s2 ? void 0 : s2.renderBefore) && void 0 !== n2 ? n2 : null;
    o2._$litPart$ = r2 = new Q(e2.insertBefore(A(), t3), t3, void 0, null != s2 ? s2 : {});
  }
  return r2._$AI(t2), r2;
};
var ht;
var lt;
var ut = class extends _ {
  constructor() {
    super(...arguments), this.renderOptions = { host: this }, this.st = void 0;
  }
  createRenderRoot() {
    var t2, e2;
    const s2 = super.createRenderRoot();
    return null !== (t2 = (e2 = this.renderOptions).renderBefore) && void 0 !== t2 || (e2.renderBefore = s2.firstChild), s2;
  }
  update(t2) {
    const e2 = this.render();
    this.hasUpdated || (this.renderOptions.isConnected = this.isConnected), super.update(t2), this.st = rt(e2, this.renderRoot, this.renderOptions);
  }
  connectedCallback() {
    var t2;
    super.connectedCallback(), null === (t2 = this.st) || void 0 === t2 || t2.setConnected(true);
  }
  disconnectedCallback() {
    var t2;
    super.disconnectedCallback(), null === (t2 = this.st) || void 0 === t2 || t2.setConnected(false);
  }
  render() {
    return D;
  }
};
ut.finalized = true, ut._$litElement$ = true, null === (ht = globalThis.litElementHydrateSupport) || void 0 === ht || ht.call(globalThis, { LitElement: ut });
var ct = globalThis.litElementPolyfillSupport;
null == ct || ct({ LitElement: ut });
(null !== (lt = globalThis.litElementVersions) && void 0 !== lt ? lt : globalThis.litElementVersions = []).push("3.3.2");

// src/ui/settings/SettingsView.js
var SettingsView = class extends ut {
  static styles = r`
        * {
            font-family: 'Helvetica Neue', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            cursor: default;
            user-select: none;
        }

        :host {
            display: block;
            width: 240px;
            height: 100%;
            color: white;
        }

        .settings-container {
            display: flex;
            flex-direction: column;
            height: 100%;
            width: 100%;
            background: rgba(20, 20, 20, 0.8);
            border-radius: 12px;
            outline: 0.5px rgba(255, 255, 255, 0.2) solid;
            outline-offset: -1px;
            box-sizing: border-box;
            position: relative;
            overflow-y: auto;
            padding: 12px 12px;
            z-index: 1000;
        }

        .settings-container::-webkit-scrollbar {
            width: 6px;
        }

        .settings-container::-webkit-scrollbar-track {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 3px;
        }

        .settings-container::-webkit-scrollbar-thumb {
            background: rgba(255, 255, 255, 0.2);
            border-radius: 3px;
        }

        .settings-container::-webkit-scrollbar-thumb:hover {
            background: rgba(255, 255, 255, 0.3);
        }

        .settings-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.15);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
            border-radius: 12px;
            filter: blur(10px);
            z-index: -1;
        }
            
        .settings-button[disabled],
        .api-key-section input[disabled] {
            opacity: 0.4;
            cursor: not-allowed;
            pointer-events: none;
        }

        .header-section {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            padding-bottom: 6px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            position: relative;
            z-index: 1;
        }

        .title-line {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .app-title {
            font-size: 13px;
            font-weight: 500;
            color: white;
            margin: 0 0 4px 0;
        }

        .account-info {
            font-size: 11px;
            color: rgba(255, 255, 255, 0.7);
            margin: 0;
        }

        .invisibility-icon {
            padding-top: 2px;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .invisibility-icon.visible {
            opacity: 1;
        }

        .invisibility-icon svg {
            width: 16px;
            height: 16px;
        }

        .shortcuts-section {
            display: flex;
            flex-direction: column;
            gap: 2px;
            padding: 4px 0;
            position: relative;
            z-index: 1;
        }

        .shortcut-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 4px 0;
            color: white;
            font-size: 11px;
        }

        .shortcut-name {
            font-weight: 300;
        }

        .shortcut-keys {
            display: flex;
            align-items: center;
            gap: 3px;
        }

        .cmd-key, .shortcut-key {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 3px;
            width: 16px;
            height: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 11px;
            font-weight: 500;
            color: rgba(255, 255, 255, 0.9);
        }

        /* Buttons Section */
        .buttons-section {
            display: flex;
            flex-direction: column;
            gap: 4px;
            padding-top: 6px;
            border-top: 1px solid rgba(255, 255, 255, 0.1);
            position: relative;
            z-index: 1;
            flex: 1;
        }

        .settings-button {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 4px;
            color: white;
            padding: 5px 10px;
            font-size: 11px;
            font-weight: 400;
            cursor: pointer;
            transition: all 0.15s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            white-space: nowrap;
        }

        .settings-button:hover {
            background: rgba(255, 255, 255, 0.15);
            border-color: rgba(255, 255, 255, 0.3);
        }

        .settings-button:active {
            transform: translateY(1px);
        }

        .settings-button.full-width {
            width: 100%;
        }

        .settings-button.half-width {
            flex: 1;
        }

        .settings-button.danger {
            background: rgba(255, 59, 48, 0.1);
            border-color: rgba(255, 59, 48, 0.3);
            color: rgba(255, 59, 48, 0.9);
        }

        .settings-button.danger:hover {
            background: rgba(255, 59, 48, 0.15);
            border-color: rgba(255, 59, 48, 0.4);
        }

        .move-buttons, .bottom-buttons {
            display: flex;
            gap: 4px;
        }

        .api-key-section {
            padding: 6px 0;
            border-top: 1px solid rgba(255, 255, 255, 0.1);
        }

        .api-key-section input {
            width: 100%;
            background: rgba(0,0,0,0.2);
            border: 1px solid rgba(255,255,255,0.2);
            color: white;
            border-radius: 4px;
            padding: 4px;
            font-size: 11px;
            margin-bottom: 4px;
            box-sizing: border-box;
        }

        .api-key-section input::placeholder {
            color: rgba(255, 255, 255, 0.4);
        }

        /* Preset Management Section */
        .preset-section {
            padding: 6px 0;
            border-top: 1px solid rgba(255, 255, 255, 0.1);
        }

        .preset-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 4px;
        }

        .preset-title {
            font-size: 11px;
            font-weight: 500;
            color: white;
        }

        .preset-count {
            font-size: 9px;
            color: rgba(255, 255, 255, 0.5);
            margin-left: 4px;
        }

        .preset-toggle {
            font-size: 10px;
            color: rgba(255, 255, 255, 0.6);
            cursor: pointer;
            padding: 2px 4px;
            border-radius: 2px;
            transition: background-color 0.15s ease;
        }

        .preset-toggle:hover {
            background: rgba(255, 255, 255, 0.1);
        }

        .preset-list {
            display: flex;
            flex-direction: column;
            gap: 2px;
            max-height: 120px;
            overflow-y: auto;
        }

        .preset-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 4px 6px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 3px;
            cursor: pointer;
            transition: all 0.15s ease;
            font-size: 11px;
            border: 1px solid transparent;
        }

        .preset-item:hover {
            background: rgba(255, 255, 255, 0.1);
            border-color: rgba(255, 255, 255, 0.1);
        }

        .preset-item.selected {
            background: rgba(0, 122, 255, 0.25);
            border-color: rgba(0, 122, 255, 0.6);
            box-shadow: 0 0 0 1px rgba(0, 122, 255, 0.3);
        }

        .preset-name {
            color: white;
            flex: 1;
            text-overflow: ellipsis;
            overflow: hidden;
            white-space: nowrap;
            font-weight: 300;
        }

        .preset-item.selected .preset-name {
            font-weight: 500;
        }

        .preset-status {
            font-size: 9px;
            color: rgba(0, 122, 255, 0.8);
            font-weight: 500;
            margin-left: 6px;
        }

        .no-presets-message {
            padding: 12px 8px;
            text-align: center;
            color: rgba(255, 255, 255, 0.5);
            font-size: 10px;
            line-height: 1.4;
        }

        .no-presets-message .web-link {
            color: rgba(0, 122, 255, 0.8);
            text-decoration: underline;
            cursor: pointer;
        }

        .no-presets-message .web-link:hover {
            color: rgba(0, 122, 255, 1);
        }

        .loading-state {
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
            color: rgba(255, 255, 255, 0.7);
            font-size: 11px;
        }

        .loading-spinner {
            width: 12px;
            height: 12px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-top: 1px solid rgba(255, 255, 255, 0.8);
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-right: 6px;
        }

        .hidden {
            display: none;
        }

        .api-key-section, .model-selection-section {
            padding: 8px 0;
            border-top: 1px solid rgba(255, 255, 255, 0.1);
            display: flex;
            flex-direction: column;
            gap: 10px;
        }
        .provider-key-group, .model-select-group {
            display: flex;
            flex-direction: column;
            gap: 4px;
        }
        label {
            font-size: 11px;
            font-weight: 500;
            color: rgba(255, 255, 255, 0.8);
            margin-left: 2px;
        }
        label > strong {
            color: white;
            font-weight: 600;
        }
        .provider-key-group input {
            width: 100%; background: rgba(0,0,0,0.2); border: 1px solid rgba(255,255,255,0.2);
            color: white; border-radius: 4px; padding: 5px 8px; font-size: 11px; box-sizing: border-box;
        }
        .key-buttons { display: flex; gap: 4px; }
        .key-buttons .settings-button { flex: 1; padding: 4px; }
        .model-list {
            display: flex; flex-direction: column; gap: 2px; max-height: 120px;
            overflow-y: auto; background: rgba(0,0,0,0.3); border-radius: 4px;
            padding: 4px; margin-top: 4px;
        }
        .model-item { 
            padding: 5px 8px; 
            font-size: 11px; 
            border-radius: 3px; 
            cursor: pointer; 
            transition: background-color 0.15s; 
            display: flex; 
            justify-content: space-between; 
            align-items: center; 
        }
        .model-item:hover { background-color: rgba(255,255,255,0.1); }
        .model-item.selected { background-color: rgba(0, 122, 255, 0.4); font-weight: 500; }
        .model-status { 
            font-size: 9px; 
            color: rgba(255,255,255,0.6); 
            margin-left: 8px; 
        }
        .model-status.installed { color: rgba(0, 255, 0, 0.8); }
        .model-status.not-installed { color: rgba(255, 200, 0, 0.8); }
        .install-progress {
            flex: 1;
            height: 4px;
            background: rgba(255,255,255,0.1);
            border-radius: 2px;
            margin-left: 8px;
            overflow: hidden;
        }
        .install-progress-bar {
            height: 100%;
            background: rgba(0, 122, 255, 0.8);
            transition: width 0.3s ease;
        }
        
        /* Dropdown styles */
        select.model-dropdown {
            background: rgba(0,0,0,0.2);
            color: white;
            cursor: pointer;
        }
        
        select.model-dropdown option {
            background: #1a1a1a;
            color: white;
        }
        
        select.model-dropdown option:disabled {
            color: rgba(255,255,255,0.4);
        }
            
        /* ────────────────[ GLASS BYPASS ]─────────────── */
        :host-context(body.has-glass) {
            animation: none !important;
            transition: none !important;
            transform: none !important;
            will-change: auto !important;
        }

        :host-context(body.has-glass) * {
            background: transparent !important;
            filter: none !important;
            backdrop-filter: none !important;
            box-shadow: none !important;
            outline: none !important;
            border: none !important;
            border-radius: 0 !important;
            transition: none !important;
            animation: none !important;
        }

        :host-context(body.has-glass) .settings-container::before {
            display: none !important;
        }
    `;
  //////// after_modelStateService ////////
  static properties = {
    shortcuts: { type: Object, state: true },
    firebaseUser: { type: Object, state: true },
    isLoading: { type: Boolean, state: true },
    isContentProtectionOn: { type: Boolean, state: true },
    saving: { type: Boolean, state: true },
    providerConfig: { type: Object, state: true },
    apiKeys: { type: Object, state: true },
    availableLlmModels: { type: Array, state: true },
    availableSttModels: { type: Array, state: true },
    selectedLlm: { type: String, state: true },
    selectedStt: { type: String, state: true },
    isLlmListVisible: { type: Boolean },
    isSttListVisible: { type: Boolean },
    presets: { type: Array, state: true },
    selectedPreset: { type: Object, state: true },
    showPresets: { type: Boolean, state: true },
    autoUpdateEnabled: { type: Boolean, state: true },
    autoUpdateLoading: { type: Boolean, state: true },
    // Ollama related properties
    ollamaStatus: { type: Object, state: true },
    ollamaModels: { type: Array, state: true },
    installingModels: { type: Object, state: true },
    // Whisper related properties
    whisperModels: { type: Array, state: true }
  };
  //////// after_modelStateService ////////
  constructor() {
    super();
    this.shortcuts = {};
    this.firebaseUser = null;
    this.apiKeys = { openai: "", gemini: "", anthropic: "", whisper: "" };
    this.providerConfig = {};
    this.isLoading = true;
    this.isContentProtectionOn = true;
    this.saving = false;
    this.availableLlmModels = [];
    this.availableSttModels = [];
    this.selectedLlm = null;
    this.selectedStt = null;
    this.isLlmListVisible = false;
    this.isSttListVisible = false;
    this.presets = [];
    this.selectedPreset = null;
    this.showPresets = false;
    this.ollamaStatus = { installed: false, running: false };
    this.ollamaModels = [];
    this.installingModels = {};
    this.whisperModels = [];
    this.whisperProgressTracker = null;
    this.handleUsePicklesKey = this.handleUsePicklesKey.bind(this);
    this.autoUpdateEnabled = true;
    this.autoUpdateLoading = true;
    this.loadInitialData();
  }
  async loadAutoUpdateSetting() {
    if (!window.api) return;
    this.autoUpdateLoading = true;
    try {
      const enabled = await window.api.settingsView.getAutoUpdate();
      this.autoUpdateEnabled = enabled;
      console.log("Auto-update setting loaded:", enabled);
    } catch (e2) {
      console.error("Error loading auto-update setting:", e2);
      this.autoUpdateEnabled = true;
    }
    this.autoUpdateLoading = false;
    this.requestUpdate();
  }
  async handleToggleAutoUpdate() {
    if (!window.api || this.autoUpdateLoading) return;
    this.autoUpdateLoading = true;
    this.requestUpdate();
    try {
      const newValue = !this.autoUpdateEnabled;
      const result = await window.api.settingsView.setAutoUpdate(newValue);
      if (result && result.success) {
        this.autoUpdateEnabled = newValue;
      } else {
        console.error("Failed to update auto-update setting");
      }
    } catch (e2) {
      console.error("Error toggling auto-update:", e2);
    }
    this.autoUpdateLoading = false;
    this.requestUpdate();
  }
  async loadLocalAIStatus() {
    try {
      const ollamaStatus = await window.api.settingsView.getOllamaStatus();
      if (ollamaStatus?.success) {
        this.ollamaStatus = { installed: ollamaStatus.installed, running: ollamaStatus.running };
        this.ollamaModels = ollamaStatus.models || [];
      }
      if (this.apiKeys?.whisper === "local") {
        const whisperModelsResult = await window.api.settingsView.getWhisperInstalledModels();
        if (whisperModelsResult?.success) {
          const installedWhisperModels = whisperModelsResult.models;
          if (this.providerConfig?.whisper) {
            this.providerConfig.whisper.sttModels.forEach((m2) => {
              const installedInfo = installedWhisperModels.find((i2) => i2.id === m2.id);
              if (installedInfo) {
                m2.installed = installedInfo.installed;
              }
            });
          }
        }
      }
      this.requestUpdate();
    } catch (error) {
      console.error("Error loading LocalAI status:", error);
    }
  }
  //////// after_modelStateService ////////
  async loadInitialData() {
    if (!window.api) return;
    this.isLoading = true;
    try {
      const [userState, modelSettings, presets, contentProtection, shortcuts] = await Promise.all([
        window.api.settingsView.getCurrentUser(),
        window.api.settingsView.getModelSettings(),
        // Facade call
        window.api.settingsView.getPresets(),
        window.api.settingsView.getContentProtectionStatus(),
        window.api.settingsView.getCurrentShortcuts()
      ]);
      if (userState && userState.isLoggedIn) this.firebaseUser = userState;
      if (modelSettings.success) {
        const { config, storedKeys, availableLlm, availableStt, selectedModels } = modelSettings.data;
        this.providerConfig = config;
        this.apiKeys = storedKeys;
        this.availableLlmModels = availableLlm;
        this.availableSttModels = availableStt;
        this.selectedLlm = selectedModels.llm;
        this.selectedStt = selectedModels.stt;
      }
      this.presets = presets || [];
      this.isContentProtectionOn = contentProtection;
      this.shortcuts = shortcuts || {};
      if (this.presets.length > 0) {
        const firstUserPreset = this.presets.find((p2) => p2.is_default === 0);
        if (firstUserPreset) this.selectedPreset = firstUserPreset;
      }
      this.loadLocalAIStatus();
    } catch (error) {
      console.error("Error loading initial settings data:", error);
    } finally {
      this.isLoading = false;
    }
  }
  async handleSaveKey(provider) {
    const input = this.shadowRoot.querySelector(`#key-input-${provider}`);
    if (!input) return;
    const key = input.value;
    if (provider === "ollama") {
      this.saving = true;
      const ensureResult = await window.api.settingsView.ensureOllamaReady();
      if (!ensureResult.success) {
        alert(`Failed to setup Ollama: ${ensureResult.error}`);
        this.saving = false;
        return;
      }
      const result2 = await window.api.settingsView.validateKey({ provider, key: "local" });
      if (result2.success) {
        await this.refreshModelData();
        await this.refreshOllamaStatus();
      } else {
        alert(`Failed to connect to Ollama: ${result2.error}`);
      }
      this.saving = false;
      return;
    }
    if (provider === "whisper") {
      this.saving = true;
      const result2 = await window.api.settingsView.validateKey({ provider, key: "local" });
      if (result2.success) {
        await this.refreshModelData();
      } else {
        alert(`Failed to enable Whisper: ${result2.error}`);
      }
      this.saving = false;
      return;
    }
    this.saving = true;
    const result = await window.api.settingsView.validateKey({ provider, key });
    if (result.success) {
      await this.refreshModelData();
    } else {
      alert(`Failed to save ${provider} key: ${result.error}`);
      input.value = this.apiKeys[provider] || "";
    }
    this.saving = false;
  }
  async handleClearKey(provider) {
    console.log(`[SettingsView] handleClearKey: ${provider}`);
    this.saving = true;
    await window.api.settingsView.removeApiKey(provider);
    this.apiKeys = { ...this.apiKeys, [provider]: "" };
    await this.refreshModelData();
    this.saving = false;
  }
  async refreshModelData() {
    const [availableLlm, availableStt, selected, storedKeys] = await Promise.all([
      window.api.settingsView.getAvailableModels({ type: "llm" }),
      window.api.settingsView.getAvailableModels({ type: "stt" }),
      window.api.settingsView.getSelectedModels(),
      window.api.settingsView.getAllKeys()
    ]);
    this.availableLlmModels = availableLlm;
    this.availableSttModels = availableStt;
    this.selectedLlm = selected.llm;
    this.selectedStt = selected.stt;
    this.apiKeys = storedKeys;
    this.requestUpdate();
  }
  async toggleModelList(type) {
    const visibilityProp = type === "llm" ? "isLlmListVisible" : "isSttListVisible";
    if (!this[visibilityProp]) {
      this.saving = true;
      this.requestUpdate();
      await this.refreshModelData();
      this.saving = false;
    }
    this[visibilityProp] = !this[visibilityProp];
    this.requestUpdate();
  }
  async selectModel(type, modelId) {
    const provider = this.getProviderForModel(type, modelId);
    if (provider === "ollama") {
      const ollamaModel = this.ollamaModels.find((m2) => m2.name === modelId);
      if (ollamaModel && !ollamaModel.installed && !ollamaModel.installing) {
        await this.installOllamaModel(modelId);
        return;
      }
    }
    if (provider === "whisper" && type === "stt") {
      const isInstalling = this.installingModels[modelId] !== void 0;
      const whisperModelInfo = this.providerConfig.whisper.sttModels.find((m2) => m2.id === modelId);
      if (whisperModelInfo && !whisperModelInfo.installed && !isInstalling) {
        await this.downloadWhisperModel(modelId);
        return;
      }
    }
    this.saving = true;
    await window.api.settingsView.setSelectedModel({ type, modelId });
    if (type === "llm") this.selectedLlm = modelId;
    if (type === "stt") this.selectedStt = modelId;
    this.isLlmListVisible = false;
    this.isSttListVisible = false;
    this.saving = false;
    this.requestUpdate();
  }
  async refreshOllamaStatus() {
    const ollamaStatus = await window.api.settingsView.getOllamaStatus();
    if (ollamaStatus?.success) {
      this.ollamaStatus = { installed: ollamaStatus.installed, running: ollamaStatus.running };
      this.ollamaModels = ollamaStatus.models || [];
    }
  }
  async installOllamaModel(modelName) {
    try {
      this.installingModels = { ...this.installingModels, [modelName]: 0 };
      this.requestUpdate();
      const progressHandler = (event, data) => {
        if (data.service === "ollama" && data.model === modelName) {
          this.installingModels = { ...this.installingModels, [modelName]: data.progress || 0 };
          this.requestUpdate();
        }
      };
      window.api.settingsView.onLocalAIInstallProgress(progressHandler);
      try {
        const result = await window.api.settingsView.pullOllamaModel(modelName);
        if (result.success) {
          console.log(`[SettingsView] Model ${modelName} installed successfully`);
          delete this.installingModels[modelName];
          this.requestUpdate();
          await this.refreshOllamaStatus();
          await this.refreshModelData();
        } else {
          throw new Error(result.error || "Installation failed");
        }
      } finally {
        window.api.settingsView.removeOnLocalAIInstallProgress(progressHandler);
      }
    } catch (error) {
      console.error(`[SettingsView] Error installing model ${modelName}:`, error);
      delete this.installingModels[modelName];
      this.requestUpdate();
    }
  }
  async downloadWhisperModel(modelId) {
    this.installingModels = { ...this.installingModels, [modelId]: 0 };
    this.requestUpdate();
    try {
      const progressHandler = (event, data) => {
        if (data.service === "whisper" && data.model === modelId) {
          this.installingModels = { ...this.installingModels, [modelId]: data.progress || 0 };
          this.requestUpdate();
        }
      };
      window.api.settingsView.onLocalAIInstallProgress(progressHandler);
      const result = await window.api.settingsView.downloadWhisperModel(modelId);
      if (result.success) {
        if (this.providerConfig?.whisper?.sttModels) {
          const modelInfo = this.providerConfig.whisper.sttModels.find((m2) => m2.id === modelId);
          if (modelInfo) {
            modelInfo.installed = true;
          }
        }
        delete this.installingModels[modelId];
        this.requestUpdate();
        await this.loadLocalAIStatus();
        await this.selectModel("stt", modelId);
      } else {
        delete this.installingModels[modelId];
        this.requestUpdate();
        alert(`Failed to download Whisper model: ${result.error}`);
      }
      window.api.settingsView.removeOnLocalAIInstallProgress(progressHandler);
    } catch (error) {
      console.error(`[SettingsView] Error downloading Whisper model ${modelId}:`, error);
      delete this.installingModels[modelId];
      this.requestUpdate();
      alert(`Error downloading ${modelId}: ${error.message}`);
    }
  }
  getProviderForModel(type, modelId) {
    for (const [providerId, config] of Object.entries(this.providerConfig)) {
      const models = type === "llm" ? config.llmModels : config.sttModels;
      if (models?.some((m2) => m2.id === modelId)) {
        return providerId;
      }
    }
    return null;
  }
  handleUsePicklesKey(e2) {
    e2.preventDefault();
    if (this.wasJustDragged) return;
    console.log("Requesting Firebase authentication from main process...");
    window.api.settingsView.startFirebaseAuth();
  }
  //////// after_modelStateService ////////
  openShortcutEditor() {
    window.api.settingsView.openShortcutSettingsWindow();
  }
  connectedCallback() {
    super.connectedCallback();
    this.setupEventListeners();
    this.setupIpcListeners();
    this.setupWindowResize();
    this.loadAutoUpdateSetting();
    setTimeout(() => this.updateScrollHeight(), 0);
  }
  disconnectedCallback() {
    super.disconnectedCallback();
    this.cleanupEventListeners();
    this.cleanupIpcListeners();
    this.cleanupWindowResize();
    const installingModels = Object.keys(this.installingModels);
    if (installingModels.length > 0) {
      installingModels.forEach((modelName) => {
        window.api.settingsView.cancelOllamaInstallation(modelName);
      });
    }
  }
  setupEventListeners() {
    this.addEventListener("mouseenter", this.handleMouseEnter);
    this.addEventListener("mouseleave", this.handleMouseLeave);
  }
  cleanupEventListeners() {
    this.removeEventListener("mouseenter", this.handleMouseEnter);
    this.removeEventListener("mouseleave", this.handleMouseLeave);
  }
  setupIpcListeners() {
    if (!window.api) return;
    this._userStateListener = (event, userState) => {
      console.log("[SettingsView] Received user-state-changed:", userState);
      if (userState && userState.isLoggedIn) {
        this.firebaseUser = userState;
      } else {
        this.firebaseUser = null;
      }
      this.loadAutoUpdateSetting();
      this.loadInitialData();
    };
    this._settingsUpdatedListener = (event, settings) => {
      console.log("[SettingsView] Received settings-updated");
      this.settings = settings;
      this.requestUpdate();
    };
    this._presetsUpdatedListener = async (event) => {
      console.log("[SettingsView] Received presets-updated, refreshing presets");
      try {
        const presets = await window.api.settingsView.getPresets();
        this.presets = presets || [];
        const userPresets = this.presets.filter((p2) => p2.is_default === 0);
        if (this.selectedPreset && !userPresets.find((p2) => p2.id === this.selectedPreset.id)) {
          this.selectedPreset = userPresets.length > 0 ? userPresets[0] : null;
        }
        this.requestUpdate();
      } catch (error) {
        console.error("[SettingsView] Failed to refresh presets:", error);
      }
    };
    this._shortcutListener = (event, keybinds) => {
      console.log("[SettingsView] Received updated shortcuts:", keybinds);
      this.shortcuts = keybinds;
    };
    window.api.settingsView.onUserStateChanged(this._userStateListener);
    window.api.settingsView.onSettingsUpdated(this._settingsUpdatedListener);
    window.api.settingsView.onPresetsUpdated(this._presetsUpdatedListener);
    window.api.settingsView.onShortcutsUpdated(this._shortcutListener);
  }
  cleanupIpcListeners() {
    if (!window.api) return;
    if (this._userStateListener) {
      window.api.settingsView.removeOnUserStateChanged(this._userStateListener);
    }
    if (this._settingsUpdatedListener) {
      window.api.settingsView.removeOnSettingsUpdated(this._settingsUpdatedListener);
    }
    if (this._presetsUpdatedListener) {
      window.api.settingsView.removeOnPresetsUpdated(this._presetsUpdatedListener);
    }
    if (this._shortcutListener) {
      window.api.settingsView.removeOnShortcutsUpdated(this._shortcutListener);
    }
  }
  setupWindowResize() {
    this.resizeHandler = () => {
      this.requestUpdate();
      this.updateScrollHeight();
    };
    window.addEventListener("resize", this.resizeHandler);
    setTimeout(() => this.updateScrollHeight(), 100);
  }
  cleanupWindowResize() {
    if (this.resizeHandler) {
      window.removeEventListener("resize", this.resizeHandler);
    }
  }
  updateScrollHeight() {
    const rawHeight = window.innerHeight || (window.screen ? window.screen.height : 0);
    const MIN_HEIGHT = 300;
    const maxHeight = Math.max(MIN_HEIGHT, rawHeight);
    this.style.maxHeight = `${maxHeight}px`;
    const container = this.shadowRoot?.querySelector(".settings-container");
    if (container) {
      container.style.maxHeight = `${maxHeight}px`;
    }
  }
  handleMouseEnter = () => {
    window.api.settingsView.cancelHideSettingsWindow();
    this.updateScrollHeight();
  };
  handleMouseLeave = () => {
    window.api.settingsView.hideSettingsWindow();
  };
  getMainShortcuts() {
    return [
      { name: "Show / Hide", accelerator: this.shortcuts.toggleVisibility },
      { name: "Ask Anything", accelerator: this.shortcuts.nextStep },
      { name: "Scroll Up Response", accelerator: this.shortcuts.scrollUp },
      { name: "Scroll Down Response", accelerator: this.shortcuts.scrollDown }
    ];
  }
  renderShortcutKeys(accelerator) {
    if (!accelerator) return H`N/A`;
    const keyMap = {
      "Cmd": "\u2318",
      "Command": "\u2318",
      "Ctrl": "\u2303",
      "Alt": "\u2325",
      "Shift": "\u21E7",
      "Enter": "\u21B5",
      "Up": "\u2191",
      "Down": "\u2193",
      "Left": "\u2190",
      "Right": "\u2192"
    };
    if (accelerator.includes("\u2195")) {
      const keys2 = accelerator.replace("\u2195", "").split("+");
      keys2.push("\u2195");
      return H`${keys2.map((key) => H`<span class="shortcut-key">${keyMap[key] || key}</span>`)}`;
    }
    const keys = accelerator.split("+");
    return H`${keys.map((key) => H`<span class="shortcut-key">${keyMap[key] || key}</span>`)}`;
  }
  togglePresets() {
    this.showPresets = !this.showPresets;
  }
  async handlePresetSelect(preset) {
    this.selectedPreset = preset;
    console.log("Selected preset:", preset);
  }
  handleMoveLeft() {
    console.log("Move Left clicked");
    window.api.settingsView.moveWindowStep("left");
  }
  handleMoveRight() {
    console.log("Move Right clicked");
    window.api.settingsView.moveWindowStep("right");
  }
  async handlePersonalize() {
    console.log("Personalize clicked");
    try {
      await window.api.settingsView.openPersonalizePage();
    } catch (error) {
      console.error("Failed to open personalize page:", error);
    }
  }
  async handleToggleInvisibility() {
    console.log("Toggle Invisibility clicked");
    this.isContentProtectionOn = await window.api.settingsView.toggleContentProtection();
    this.requestUpdate();
  }
  async handleSaveApiKey() {
    const input = this.shadowRoot.getElementById("api-key-input");
    if (!input || !input.value) return;
    const newApiKey = input.value;
    try {
      const result = await window.api.settingsView.saveApiKey(newApiKey);
      if (result.success) {
        console.log("API Key saved successfully via IPC.");
        this.apiKey = newApiKey;
        this.requestUpdate();
      } else {
        console.error("Failed to save API Key via IPC:", result.error);
      }
    } catch (e2) {
      console.error("Error invoking save-api-key IPC:", e2);
    }
  }
  handleQuit() {
    console.log("Quit clicked");
    window.api.settingsView.quitApplication();
  }
  handleFirebaseLogout() {
    console.log("Firebase Logout clicked");
    window.api.settingsView.firebaseLogout();
  }
  async handleOllamaShutdown() {
    console.log("[SettingsView] Shutting down Ollama service...");
    if (!window.api) return;
    try {
      this.ollamaStatus = { ...this.ollamaStatus, running: false };
      this.requestUpdate();
      const result = await window.api.settingsView.shutdownOllama(false);
      if (result.success) {
        console.log("[SettingsView] Ollama shut down successfully");
        await this.refreshOllamaStatus();
      } else {
        console.error("[SettingsView] Failed to shutdown Ollama:", result.error);
        await this.refreshOllamaStatus();
      }
    } catch (error) {
      console.error("[SettingsView] Error during Ollama shutdown:", error);
      await this.refreshOllamaStatus();
    }
  }
  //////// after_modelStateService ////////
  render() {
    if (this.isLoading) {
      return H`
                <div class="settings-container">
                    <div class="loading-state">
                        <div class="loading-spinner"></div>
                        <span>Loading...</span>
                    </div>
                </div>
            `;
    }
    const loggedIn = !!this.firebaseUser;
    const apiKeyManagementHTML = H`
            <div class="api-key-section">
                ${Object.entries(this.providerConfig).filter(([id, config]) => !id.includes("-glass")).map(([id, config]) => {
      if (id === "ollama") {
        return H`
                                <div class="provider-key-group">
                                    <label>${config.name} (Local)</label>
                                    ${this.ollamaStatus.installed && this.ollamaStatus.running ? H`
                                        <div style="padding: 8px; background: rgba(0,255,0,0.1); border-radius: 4px; font-size: 11px; color: rgba(0,255,0,0.8);">
                                            ✓ Ollama is running
                                        </div>
                                        <button class="settings-button full-width danger" @click=${this.handleOllamaShutdown}>
                                            Stop Ollama Service
                                        </button>
                                    ` : this.ollamaStatus.installed ? H`
                                        <div style="padding: 8px; background: rgba(255,200,0,0.1); border-radius: 4px; font-size: 11px; color: rgba(255,200,0,0.8);">
                                            ⚠ Ollama installed but not running
                                        </div>
                                        <button class="settings-button full-width" @click=${() => this.handleSaveKey(id)}>
                                            Start Ollama
                                        </button>
                                    ` : H`
                                        <div style="padding: 8px; background: rgba(255,100,100,0.1); border-radius: 4px; font-size: 11px; color: rgba(255,100,100,0.8);">
                                            ✗ Ollama not installed
                                        </div>
                                        <button class="settings-button full-width" @click=${() => this.handleSaveKey(id)}>
                                            Install & Setup Ollama
                                        </button>
                                    `}
                                </div>
                            `;
      }
      if (id === "whisper") {
        return H`
                                <div class="provider-key-group">
                                    <label>${config.name} (Local STT)</label>
                                    ${this.apiKeys[id] === "local" ? H`
                                        <div style="padding: 8px; background: rgba(0,255,0,0.1); border-radius: 4px; font-size: 11px; color: rgba(0,255,0,0.8); margin-bottom: 8px;">
                                            ✓ Whisper is enabled
                                        </div>
                                        <button class="settings-button full-width danger" @click=${() => this.handleClearKey(id)}>
                                            Disable Whisper
                                        </button>
                                    ` : H`
                                        <button class="settings-button full-width" @click=${() => this.handleSaveKey(id)}>
                                            Enable Whisper STT
                                        </button>
                                    `}
                                </div>
                            `;
      }
      return H`
                        <div class="provider-key-group">
                            <label for="key-input-${id}">${config.name} API Key</label>
                            <input type="password" id="key-input-${id}"
                                placeholder=${loggedIn ? "Using Pickle's Key" : `Enter ${config.name} API Key`} 
                                .value=${this.apiKeys[id] || ""}
                            >
                            <div class="key-buttons">
                               <button class="settings-button" @click=${() => this.handleSaveKey(id)} >Save</button>
                               <button class="settings-button danger" @click=${() => this.handleClearKey(id)} }>Clear</button>
                            </div>
                        </div>
                        `;
    })}
            </div>
        `;
    const getModelName = (type, id) => {
      const models = type === "llm" ? this.availableLlmModels : this.availableSttModels;
      const model = models.find((m2) => m2.id === id);
      return model ? model.name : id;
    };
    const modelSelectionHTML = H`
            <div class="model-selection-section">
                <div class="model-select-group">
                    <label>LLM Model: <strong>${getModelName("llm", this.selectedLlm) || "Not Set"}</strong></label>
                    <button class="settings-button full-width" @click=${() => this.toggleModelList("llm")} ?disabled=${this.saving || this.availableLlmModels.length === 0}>
                        Change LLM Model
                    </button>
                    ${this.isLlmListVisible ? H`
                        <div class="model-list">
                            ${this.availableLlmModels.map((model) => {
      const isOllama = this.getProviderForModel("llm", model.id) === "ollama";
      const ollamaModel = isOllama ? this.ollamaModels.find((m2) => m2.name === model.id) : null;
      const isInstalling = this.installingModels[model.id] !== void 0;
      const installProgress = this.installingModels[model.id] || 0;
      return H`
                                    <div class="model-item ${this.selectedLlm === model.id ? "selected" : ""}" 
                                         @click=${() => this.selectModel("llm", model.id)}>
                                        <span>${model.name}</span>
                                        ${isOllama ? H`
                                            ${isInstalling ? H`
                                                <div class="install-progress">
                                                    <div class="install-progress-bar" style="width: ${installProgress}%"></div>
                                </div>
                                            ` : ollamaModel?.installed ? H`
                                                <span class="model-status installed">✓ Installed</span>
                                            ` : H`
                                                <span class="model-status not-installed">Click to install</span>
                                            `}
                                        ` : ""}
                                    </div>
                                `;
    })}
                        </div>
                    ` : ""}
                </div>
                <div class="model-select-group">
                    <label>STT Model: <strong>${getModelName("stt", this.selectedStt) || "Not Set"}</strong></label>
                    <button class="settings-button full-width" @click=${() => this.toggleModelList("stt")} ?disabled=${this.saving || this.availableSttModels.length === 0}>
                        Change STT Model
                    </button>
                    ${this.isSttListVisible ? H`
                        <div class="model-list">
                            ${this.availableSttModels.map((model) => {
      const isWhisper = this.getProviderForModel("stt", model.id) === "whisper";
      const whisperModel = isWhisper && this.providerConfig?.whisper?.sttModels ? this.providerConfig.whisper.sttModels.find((m2) => m2.id === model.id) : null;
      const isInstalling = this.installingModels[model.id] !== void 0;
      const installProgress = this.installingModels[model.id] || 0;
      return H`
                                    <div class="model-item ${this.selectedStt === model.id ? "selected" : ""}" 
                                         @click=${() => this.selectModel("stt", model.id)}>
                                        <span>${model.name}</span>
                                        ${isWhisper ? H`
                                            ${isInstalling ? H`
                                                <div class="install-progress">
                                                    <div class="install-progress-bar" style="width: ${installProgress}%"></div>
                                                </div>
                                            ` : whisperModel?.installed ? H`
                                                <span class="model-status installed">✓ Installed</span>
                                            ` : H`
                                                <span class="model-status not-installed">Not Installed</span>
                                            `}
                                        ` : ""}
                                    </div>
                                `;
    })}
                        </div>
                    ` : ""}
                </div>
            </div>
        `;
    return H`
            <div class="settings-container">
                <div class="header-section">
                    <div>
                        <h1 class="app-title">Pickle Glass</h1>
                        <div class="account-info">
                            ${this.firebaseUser ? H`Account: ${this.firebaseUser.email || "Logged In"}` : `Account: Not Logged In`}
                        </div>
                    </div>
                    <div class="invisibility-icon ${this.isContentProtectionOn ? "visible" : ""}" title="Invisibility is On">
                        <svg width="14" height="14" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M9.785 7.41787C8.7 7.41787 7.79 8.19371 7.55667 9.22621C7.0025 8.98704 6.495 9.05121 6.11 9.22037C5.87083 8.18204 4.96083 7.41787 3.88167 7.41787C2.61583 7.41787 1.58333 8.46204 1.58333 9.75121C1.58333 11.0404 2.61583 12.0845 3.88167 12.0845C5.08333 12.0845 6.06333 11.1395 6.15667 9.93787C6.355 9.79787 6.87417 9.53537 7.51 9.94954C7.615 11.1454 8.58333 12.0845 9.785 12.0845C11.0508 12.0845 12.0833 11.0404 12.0833 9.75121C12.0833 8.46204 11.0508 7.41787 9.785 7.41787ZM3.88167 11.4195C2.97167 11.4195 2.2425 10.6729 2.2425 9.75121C2.2425 8.82954 2.9775 8.08287 3.88167 8.08287C4.79167 8.08287 5.52083 8.82954 5.52083 9.75121C5.52083 10.6729 4.79167 11.4195 3.88167 11.4195ZM9.785 11.4195C8.875 11.4195 8.14583 10.6729 8.14583 9.75121C8.14583 8.82954 8.875 8.08287 9.785 8.08287C10.695 8.08287 11.43 8.82954 11.43 9.75121C11.43 10.6729 10.6892 11.4195 9.785 11.4195ZM12.6667 5.95954H1V6.83454H12.6667V5.95954ZM8.8925 1.36871C8.76417 1.08287 8.4375 0.931207 8.12833 1.03037L6.83333 1.46204L5.5325 1.03037L5.50333 1.02454C5.19417 0.93704 4.8675 1.10037 4.75083 1.39787L3.33333 5.08454H10.3333L8.91 1.39787L8.8925 1.36871Z" fill="white"/>
                        </svg>
                    </div>
                </div>

                ${apiKeyManagementHTML}
                ${modelSelectionHTML}

                <div class="buttons-section" style="border-top: 1px solid rgba(255, 255, 255, 0.1); padding-top: 6px; margin-top: 6px;">
                    <button class="settings-button full-width" @click=${this.openShortcutEditor}>
                        Edit Shortcuts
                    </button>
                </div>

                
                <div class="shortcuts-section">
                    ${this.getMainShortcuts().map((shortcut) => H`
                        <div class="shortcut-item">
                            <span class="shortcut-name">${shortcut.name}</span>
                            <div class="shortcut-keys">
                                ${this.renderShortcutKeys(shortcut.accelerator)}
                            </div>
                        </div>
                    `)}
                </div>

                <div class="preset-section">
                    <div class="preset-header">
                        <span class="preset-title">
                            My Presets
                            <span class="preset-count">(${this.presets.filter((p2) => p2.is_default === 0).length})</span>
                        </span>
                        <span class="preset-toggle" @click=${this.togglePresets}>
                            ${this.showPresets ? "\u25BC" : "\u25B6"}
                        </span>
                    </div>
                    
                    <div class="preset-list ${this.showPresets ? "" : "hidden"}">
                        ${this.presets.filter((p2) => p2.is_default === 0).length === 0 ? H`
                            <div class="no-presets-message">
                                No custom presets yet.<br>
                                <span class="web-link" @click=${this.handlePersonalize}>
                                    Create your first preset
                                </span>
                            </div>
                        ` : this.presets.filter((p2) => p2.is_default === 0).map((preset) => H`
                            <div class="preset-item ${this.selectedPreset?.id === preset.id ? "selected" : ""}"
                                 @click=${() => this.handlePresetSelect(preset)}>
                                <span class="preset-name">${preset.title}</span>
                                ${this.selectedPreset?.id === preset.id ? H`<span class="preset-status">Selected</span>` : ""}
                            </div>
                        `)}
                    </div>
                </div>

                <div class="buttons-section">
                    <button class="settings-button full-width" @click=${this.handlePersonalize}>
                        <span>Personalize / Meeting Notes</span>
                    </button>
                    <button class="settings-button full-width" @click=${this.handleToggleAutoUpdate} ?disabled=${this.autoUpdateLoading}>
                        <span>Automatic Updates: ${this.autoUpdateEnabled ? "On" : "Off"}</span>
                    </button>
                    
                    <div class="move-buttons">
                        <button class="settings-button half-width" @click=${this.handleMoveLeft}>
                            <span>← Move</span>
                        </button>
                        <button class="settings-button half-width" @click=${this.handleMoveRight}>
                            <span>Move →</span>
                        </button>
                    </div>
                    
                    <button class="settings-button full-width" @click=${this.handleToggleInvisibility}>
                        <span>${this.isContentProtectionOn ? "Disable Invisibility" : "Enable Invisibility"}</span>
                    </button>
                    
                    <div class="bottom-buttons">
                        ${this.firebaseUser ? H`
                                <button class="settings-button half-width danger" @click=${this.handleFirebaseLogout}>
                                    <span>Logout</span>
                                </button>
                                ` : H`
                                <button class="settings-button half-width" @click=${this.handleUsePicklesKey}>
                                    <span>Login</span>
                                </button>
                                `}
                        <button class="settings-button half-width danger" @click=${this.handleQuit}>
                            <span>Quit</span>
                        </button>
                    </div>
                </div>
            </div>
        `;
  }
  //////// after_modelStateService ////////
};
customElements.define("settings-view", SettingsView);

// src/ui/listen/stt/SttView.js
var SttView = class extends ut {
  static styles = r`
        :host {
            display: block;
            width: 100%;
        }

        /* Inherit font styles from parent */

        .transcription-container {
            overflow-y: auto;
            padding: 12px 12px 16px 12px;
            display: flex;
            flex-direction: column;
            gap: 8px;
            min-height: 150px;
            max-height: 600px;
            position: relative;
            z-index: 1;
            flex: 1;
        }

        /* Visibility handled by parent component */

        .transcription-container::-webkit-scrollbar {
            width: 8px;
        }
        .transcription-container::-webkit-scrollbar-track {
            background: rgba(0, 0, 0, 0.1);
            border-radius: 4px;
        }
        .transcription-container::-webkit-scrollbar-thumb {
            background: rgba(255, 255, 255, 0.3);
            border-radius: 4px;
        }
        .transcription-container::-webkit-scrollbar-thumb:hover {
            background: rgba(255, 255, 255, 0.5);
        }

        .stt-message {
            padding: 8px 12px;
            border-radius: 12px;
            max-width: 80%;
            word-wrap: break-word;
            word-break: break-word;
            line-height: 1.5;
            font-size: 13px;
            margin-bottom: 4px;
            box-sizing: border-box;
        }

        .stt-message.them {
            background: rgba(255, 255, 255, 0.1);
            color: rgba(255, 255, 255, 0.9);
            align-self: flex-start;
            border-bottom-left-radius: 4px;
            margin-right: auto;
        }

        .stt-message.me {
            background: rgba(0, 122, 255, 0.8);
            color: white;
            align-self: flex-end;
            border-bottom-right-radius: 4px;
            margin-left: auto;
        }

        .empty-state {
            display: flex;
            align-items: center;
            justify-content: center;
            height: 100px;
            color: rgba(255, 255, 255, 0.6);
            font-size: 12px;
            font-style: italic;
        }
    `;
  static properties = {
    sttMessages: { type: Array },
    isVisible: { type: Boolean }
  };
  constructor() {
    super();
    this.sttMessages = [];
    this.isVisible = true;
    this.messageIdCounter = 0;
    this._shouldScrollAfterUpdate = false;
    this.handleSttUpdate = this.handleSttUpdate.bind(this);
  }
  connectedCallback() {
    super.connectedCallback();
    if (window.api) {
      window.api.sttView.onSttUpdate(this.handleSttUpdate);
    }
  }
  disconnectedCallback() {
    super.disconnectedCallback();
    if (window.api) {
      window.api.sttView.removeOnSttUpdate(this.handleSttUpdate);
    }
  }
  // Handle session reset from parent
  resetTranscript() {
    this.sttMessages = [];
    this.requestUpdate();
  }
  handleSttUpdate(event, { speaker, text, isFinal, isPartial }) {
    if (text === void 0) return;
    const container = this.shadowRoot.querySelector(".transcription-container");
    this._shouldScrollAfterUpdate = container ? container.scrollTop + container.clientHeight >= container.scrollHeight - 10 : false;
    const findLastPartialIdx = (spk) => {
      for (let i2 = this.sttMessages.length - 1; i2 >= 0; i2--) {
        const m2 = this.sttMessages[i2];
        if (m2.speaker === spk && m2.isPartial) return i2;
      }
      return -1;
    };
    const newMessages = [...this.sttMessages];
    const targetIdx = findLastPartialIdx(speaker);
    if (isPartial) {
      if (targetIdx !== -1) {
        newMessages[targetIdx] = {
          ...newMessages[targetIdx],
          text,
          isPartial: true,
          isFinal: false
        };
      } else {
        newMessages.push({
          id: this.messageIdCounter++,
          speaker,
          text,
          isPartial: true,
          isFinal: false
        });
      }
    } else if (isFinal) {
      if (targetIdx !== -1) {
        newMessages[targetIdx] = {
          ...newMessages[targetIdx],
          text,
          isPartial: false,
          isFinal: true
        };
      } else {
        newMessages.push({
          id: this.messageIdCounter++,
          speaker,
          text,
          isPartial: false,
          isFinal: true
        });
      }
    }
    this.sttMessages = newMessages;
    this.dispatchEvent(new CustomEvent("stt-messages-updated", {
      detail: { messages: this.sttMessages },
      bubbles: true
    }));
  }
  scrollToBottom() {
    setTimeout(() => {
      const container = this.shadowRoot.querySelector(".transcription-container");
      if (container) {
        container.scrollTop = container.scrollHeight;
      }
    }, 0);
  }
  getSpeakerClass(speaker) {
    return speaker.toLowerCase() === "me" ? "me" : "them";
  }
  getTranscriptText() {
    return this.sttMessages.map((msg) => `${msg.speaker}: ${msg.text}`).join("\n");
  }
  updated(changedProperties) {
    super.updated(changedProperties);
    if (changedProperties.has("sttMessages")) {
      if (this._shouldScrollAfterUpdate) {
        this.scrollToBottom();
        this._shouldScrollAfterUpdate = false;
      }
    }
  }
  render() {
    if (!this.isVisible) {
      return H`<div style="display: none;"></div>`;
    }
    return H`
            <div class="transcription-container">
                ${this.sttMessages.length === 0 ? H`<div class="empty-state">Waiting for speech...</div>` : this.sttMessages.map((msg) => H`
                        <div class="stt-message ${this.getSpeakerClass(msg.speaker)}">
                            ${msg.text}
                        </div>
                    `)}
            </div>
        `;
  }
};
customElements.define("stt-view", SttView);

// src/ui/listen/summary/SummaryView.js
var SummaryView = class extends ut {
  static styles = r`
        :host {
            display: block;
            width: 100%;
        }

        /* Inherit font styles from parent */

        /* highlight.js 스타일 추가 */
        .insights-container pre {
            background: rgba(0, 0, 0, 0.4) !important;
            border-radius: 8px !important;
            padding: 12px !important;
            margin: 8px 0 !important;
            overflow-x: auto !important;
            border: 1px solid rgba(255, 255, 255, 0.1) !important;
            white-space: pre !important;
            word-wrap: normal !important;
            word-break: normal !important;
        }

        .insights-container code {
            font-family: 'Monaco', 'Menlo', 'Consolas', monospace !important;
            font-size: 11px !important;
            background: transparent !important;
            white-space: pre !important;
            word-wrap: normal !important;
            word-break: normal !important;
        }

        .insights-container pre code {
            white-space: pre !important;
            word-wrap: normal !important;
            word-break: normal !important;
            display: block !important;
        }

        .insights-container p code {
            background: rgba(255, 255, 255, 0.1) !important;
            padding: 2px 4px !important;
            border-radius: 3px !important;
            color: #ffd700 !important;
        }

        .hljs-keyword {
            color: #ff79c6 !important;
        }
        .hljs-string {
            color: #f1fa8c !important;
        }
        .hljs-comment {
            color: #6272a4 !important;
        }
        .hljs-number {
            color: #bd93f9 !important;
        }
        .hljs-function {
            color: #50fa7b !important;
        }
        .hljs-variable {
            color: #8be9fd !important;
        }
        .hljs-built_in {
            color: #ffb86c !important;
        }
        .hljs-title {
            color: #50fa7b !important;
        }
        .hljs-attr {
            color: #50fa7b !important;
        }
        .hljs-tag {
            color: #ff79c6 !important;
        }

        .insights-container {
            overflow-y: auto;
            padding: 12px 16px 16px 16px;
            position: relative;
            z-index: 1;
            min-height: 150px;
            max-height: 600px;
            flex: 1;
        }

        /* Visibility handled by parent component */

        .insights-container::-webkit-scrollbar {
            width: 8px;
        }
        .insights-container::-webkit-scrollbar-track {
            background: rgba(0, 0, 0, 0.1);
            border-radius: 4px;
        }
        .insights-container::-webkit-scrollbar-thumb {
            background: rgba(255, 255, 255, 0.3);
            border-radius: 4px;
        }
        .insights-container::-webkit-scrollbar-thumb:hover {
            background: rgba(255, 255, 255, 0.5);
        }

        insights-title {
            color: rgba(255, 255, 255, 0.8);
            font-size: 15px;
            font-weight: 500;
            font-family: 'Helvetica Neue', sans-serif;
            margin: 12px 0 8px 0;
            display: block;
        }

        .insights-container h4 {
            color: #ffffff;
            font-size: 12px;
            font-weight: 600;
            margin: 12px 0 8px 0;
            padding: 4px 8px;
            border-radius: 4px;
            background: transparent;
            cursor: default;
        }

        .insights-container h4:hover {
            background: transparent;
        }

        .insights-container h4:first-child {
            margin-top: 0;
        }

        .outline-item {
            color: #ffffff;
            font-size: 11px;
            line-height: 1.4;
            margin: 4px 0;
            padding: 6px 8px;
            border-radius: 4px;
            background: transparent;
            transition: background-color 0.15s ease;
            cursor: pointer;
            word-wrap: break-word;
        }

        .outline-item:hover {
            background: rgba(255, 255, 255, 0.1);
        }

        .request-item {
            color: #ffffff;
            font-size: 12px;
            line-height: 1.2;
            margin: 4px 0;
            padding: 6px 8px;
            border-radius: 4px;
            background: transparent;
            cursor: default;
            word-wrap: break-word;
            transition: background-color 0.15s ease;
        }

        .request-item.clickable {
            cursor: pointer;
            transition: all 0.15s ease;
        }
        .request-item.clickable:hover {
            background: rgba(255, 255, 255, 0.1);
            transform: translateX(2px);
        }

        /* 마크다운 렌더링된 콘텐츠 스타일 */
        .markdown-content {
            color: #ffffff;
            font-size: 11px;
            line-height: 1.4;
            margin: 4px 0;
            padding: 6px 8px;
            border-radius: 4px;
            background: transparent;
            cursor: pointer;
            word-wrap: break-word;
            transition: all 0.15s ease;
        }

        .markdown-content:hover {
            background: rgba(255, 255, 255, 0.1);
            transform: translateX(2px);
        }

        .markdown-content p {
            margin: 4px 0;
        }

        .markdown-content ul,
        .markdown-content ol {
            margin: 4px 0;
            padding-left: 16px;
        }

        .markdown-content li {
            margin: 2px 0;
        }

        .markdown-content a {
            color: #8be9fd;
            text-decoration: none;
        }

        .markdown-content a:hover {
            text-decoration: underline;
        }

        .markdown-content strong {
            font-weight: 600;
            color: #f8f8f2;
        }

        .markdown-content em {
            font-style: italic;
            color: #f1fa8c;
        }

        .empty-state {
            display: flex;
            align-items: center;
            justify-content: center;
            height: 100px;
            color: rgba(255, 255, 255, 0.6);
            font-size: 12px;
            font-style: italic;
        }
    `;
  static properties = {
    structuredData: { type: Object },
    isVisible: { type: Boolean },
    hasCompletedRecording: { type: Boolean }
  };
  constructor() {
    super();
    this.structuredData = {
      summary: [],
      topic: { header: "", bullets: [] },
      actions: [],
      followUps: []
    };
    this.isVisible = true;
    this.hasCompletedRecording = false;
    this.marked = null;
    this.hljs = null;
    this.isLibrariesLoaded = false;
    this.DOMPurify = null;
    this.isDOMPurifyLoaded = false;
    this.loadLibraries();
  }
  connectedCallback() {
    super.connectedCallback();
    if (window.api) {
      window.api.summaryView.onSummaryUpdate((event, data) => {
        this.structuredData = data;
        this.requestUpdate();
      });
    }
  }
  disconnectedCallback() {
    super.disconnectedCallback();
    if (window.api) {
      window.api.summaryView.removeAllSummaryUpdateListeners();
    }
  }
  // Handle session reset from parent
  resetAnalysis() {
    this.structuredData = {
      summary: [],
      topic: { header: "", bullets: [] },
      actions: [],
      followUps: []
    };
    this.requestUpdate();
  }
  async loadLibraries() {
    try {
      if (!window.marked) {
        await this.loadScript("../../../assets/marked-4.3.0.min.js");
      }
      if (!window.hljs) {
        await this.loadScript("../../../assets/highlight-11.9.0.min.js");
      }
      if (!window.DOMPurify) {
        await this.loadScript("../../../assets/dompurify-3.0.7.min.js");
      }
      this.marked = window.marked;
      this.hljs = window.hljs;
      this.DOMPurify = window.DOMPurify;
      if (this.marked && this.hljs) {
        this.marked.setOptions({
          highlight: (code, lang) => {
            if (lang && this.hljs.getLanguage(lang)) {
              try {
                return this.hljs.highlight(code, { language: lang }).value;
              } catch (err) {
                console.warn("Highlight error:", err);
              }
            }
            try {
              return this.hljs.highlightAuto(code).value;
            } catch (err) {
              console.warn("Auto highlight error:", err);
            }
            return code;
          },
          breaks: true,
          gfm: true,
          pedantic: false,
          smartypants: false,
          xhtml: false
        });
        this.isLibrariesLoaded = true;
        console.log("Markdown libraries loaded successfully");
      }
      if (this.DOMPurify) {
        this.isDOMPurifyLoaded = true;
        console.log("DOMPurify loaded successfully in SummaryView");
      }
    } catch (error) {
      console.error("Failed to load libraries:", error);
    }
  }
  loadScript(src) {
    return new Promise((resolve, reject) => {
      const script = document.createElement("script");
      script.src = src;
      script.onload = resolve;
      script.onerror = reject;
      document.head.appendChild(script);
    });
  }
  parseMarkdown(text) {
    if (!text) return "";
    if (!this.isLibrariesLoaded || !this.marked) {
      return text;
    }
    try {
      return this.marked(text);
    } catch (error) {
      console.error("Markdown parsing error:", error);
      return text;
    }
  }
  handleMarkdownClick(originalText) {
    this.handleRequestClick(originalText);
  }
  renderMarkdownContent() {
    if (!this.isLibrariesLoaded || !this.marked) {
      return;
    }
    const markdownElements = this.shadowRoot.querySelectorAll("[data-markdown-id]");
    markdownElements.forEach((element) => {
      const originalText = element.getAttribute("data-original-text");
      if (originalText) {
        try {
          let parsedHTML = this.parseMarkdown(originalText);
          if (this.isDOMPurifyLoaded && this.DOMPurify) {
            parsedHTML = this.DOMPurify.sanitize(parsedHTML);
            if (this.DOMPurify.removed && this.DOMPurify.removed.length > 0) {
              console.warn("Unsafe content detected in insights, showing plain text");
              element.textContent = "\u26A0\uFE0F " + originalText;
              return;
            }
          }
          element.innerHTML = parsedHTML;
        } catch (error) {
          console.error("Error rendering markdown for element:", error);
          element.textContent = originalText;
        }
      }
    });
  }
  async handleRequestClick(requestText) {
    console.log("\u{1F525} Analysis request clicked:", requestText);
    if (window.api) {
      try {
        const result = await window.api.summaryView.sendQuestionFromSummary(requestText);
        if (result.success) {
          console.log("\u2705 Question sent to AskView successfully");
        } else {
          console.error("\u274C Failed to send question to AskView:", result.error);
        }
      } catch (error) {
        console.error("\u274C Error in handleRequestClick:", error);
      }
    }
  }
  getSummaryText() {
    const data = this.structuredData || { summary: [], topic: { header: "", bullets: [] }, actions: [] };
    let sections = [];
    if (data.summary && data.summary.length > 0) {
      sections.push(`Current Summary:
${data.summary.map((s2) => `\u2022 ${s2}`).join("\n")}`);
    }
    if (data.topic && data.topic.header && data.topic.bullets.length > 0) {
      sections.push(`
${data.topic.header}:
${data.topic.bullets.map((b2) => `\u2022 ${b2}`).join("\n")}`);
    }
    if (data.actions && data.actions.length > 0) {
      sections.push(`
Actions:
${data.actions.map((a2) => `\u25B8 ${a2}`).join("\n")}`);
    }
    if (data.followUps && data.followUps.length > 0) {
      sections.push(`
Follow-Ups:
${data.followUps.map((f2) => `\u25B8 ${f2}`).join("\n")}`);
    }
    return sections.join("\n\n").trim();
  }
  updated(changedProperties) {
    super.updated(changedProperties);
    this.renderMarkdownContent();
  }
  render() {
    if (!this.isVisible) {
      return H`<div style="display: none;"></div>`;
    }
    const data = this.structuredData || {
      summary: [],
      topic: { header: "", bullets: [] },
      actions: []
    };
    const hasAnyContent = data.summary.length > 0 || data.topic.bullets.length > 0 || data.actions.length > 0;
    return H`
            <div class="insights-container">
                ${!hasAnyContent ? H`<div class="empty-state">No insights yet...</div>` : H`
                        <insights-title>Current Summary</insights-title>
                        ${data.summary.length > 0 ? data.summary.slice(0, 5).map(
      (bullet, index) => H`
                                          <div
                                              class="markdown-content"
                                              data-markdown-id="summary-${index}"
                                              data-original-text="${bullet}"
                                              @click=${() => this.handleMarkdownClick(bullet)}
                                          >
                                              ${bullet}
                                          </div>
                                      `
    ) : H` <div class="request-item">No content yet...</div> `}
                        ${data.topic.header ? H`
                                  <insights-title>${data.topic.header}</insights-title>
                                  ${data.topic.bullets.slice(0, 3).map(
      (bullet, index) => H`
                                              <div
                                                  class="markdown-content"
                                                  data-markdown-id="topic-${index}"
                                                  data-original-text="${bullet}"
                                                  @click=${() => this.handleMarkdownClick(bullet)}
                                              >
                                                  ${bullet}
                                              </div>
                                          `
    )}
                              ` : ""}
                        ${data.actions.length > 0 ? H`
                                  <insights-title>Actions</insights-title>
                                  ${data.actions.slice(0, 5).map(
      (action, index) => H`
                                              <div
                                                  class="markdown-content"
                                                  data-markdown-id="action-${index}"
                                                  data-original-text="${action}"
                                                  @click=${() => this.handleMarkdownClick(action)}
                                              >
                                                  ${action}
                                              </div>
                                          `
    )}
                              ` : ""}
                        ${this.hasCompletedRecording && data.followUps && data.followUps.length > 0 ? H`
                                  <insights-title>Follow-Ups</insights-title>
                                  ${data.followUps.map(
      (followUp, index) => H`
                                          <div
                                              class="markdown-content"
                                              data-markdown-id="followup-${index}"
                                              data-original-text="${followUp}"
                                              @click=${() => this.handleMarkdownClick(followUp)}
                                          >
                                              ${followUp}
                                          </div>
                                      `
    )}
                              ` : ""}
                    `}
            </div>
        `;
  }
};
customElements.define("summary-view", SummaryView);

// src/ui/listen/ListenView.js
var ListenView = class extends ut {
  static styles = r`
        :host {
            display: block;
            width: 400px;
            transform: translate3d(0, 0, 0);
            backface-visibility: hidden;
            transition: transform 0.2s cubic-bezier(0.23, 1, 0.32, 1), opacity 0.2s ease-out;
            will-change: transform, opacity;
        }

        :host(.hiding) {
            animation: slideUp 0.3s cubic-bezier(0.4, 0, 0.6, 1) forwards;
        }

        :host(.showing) {
            animation: slideDown 0.35s cubic-bezier(0.34, 1.56, 0.64, 1) forwards;
        }

        :host(.hidden) {
            opacity: 0;
            transform: translateY(-150%) scale(0.85);
            pointer-events: none;
        }


        * {
            font-family: 'Helvetica Neue', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            cursor: default;
            user-select: none;
        }

/* Allow text selection in insights responses */
.insights-container, .insights-container *, .markdown-content {
    user-select: text !important;
    cursor: text !important;
}

/* highlight.js 스타일 추가 */
.insights-container pre {
    background: rgba(0, 0, 0, 0.4) !important;
    border-radius: 8px !important;
    padding: 12px !important;
    margin: 8px 0 !important;
    overflow-x: auto !important;
    border: 1px solid rgba(255, 255, 255, 0.1) !important;
    white-space: pre !important;
    word-wrap: normal !important;
    word-break: normal !important;
}

.insights-container code {
    font-family: 'Monaco', 'Menlo', 'Consolas', monospace !important;
    font-size: 11px !important;
    background: transparent !important;
    white-space: pre !important;
    word-wrap: normal !important;
    word-break: normal !important;
}

.insights-container pre code {
    white-space: pre !important;
    word-wrap: normal !important;
    word-break: normal !important;
    display: block !important;
}

.insights-container p code {
    background: rgba(255, 255, 255, 0.1) !important;
    padding: 2px 4px !important;
    border-radius: 3px !important;
    color: #ffd700 !important;
}

.hljs-keyword {
    color: #ff79c6 !important;
}

.hljs-string {
    color: #f1fa8c !important;
}

.hljs-comment {
    color: #6272a4 !important;
}

.hljs-number {
    color: #bd93f9 !important;
}

.hljs-function {
    color: #50fa7b !important;
}

.hljs-title {
    color: #50fa7b !important;
}

.hljs-variable {
    color: #8be9fd !important;
}

.hljs-built_in {
    color: #ffb86c !important;
}

.hljs-attr {
    color: #50fa7b !important;
}

.hljs-tag {
    color: #ff79c6 !important;
}
        .assistant-container {
            display: flex;
            flex-direction: column;
            color: #ffffff;
            box-sizing: border-box;
            position: relative;
            background: rgba(0, 0, 0, 0.6);
            overflow: hidden;
            border-radius: 12px;
            width: 100%;
            height: 100%;
        }

        .assistant-container::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            border-radius: 12px;
            padding: 1px;
            background: linear-gradient(169deg, rgba(255, 255, 255, 0.17) 0%, rgba(255, 255, 255, 0.08) 50%, rgba(255, 255, 255, 0.17) 100%);
            -webkit-mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
            -webkit-mask-composite: destination-out;
            mask-composite: exclude;
            pointer-events: none;
        }

        .assistant-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.15);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
            border-radius: 12px;
            z-index: -1;
        }

        .top-bar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 6px 16px;
            min-height: 32px;
            position: relative;
            z-index: 1;
            width: 100%;
            box-sizing: border-box;
            flex-shrink: 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .bar-left-text {
            color: white;
            font-size: 13px;
            font-family: 'Helvetica Neue', sans-serif;
            font-weight: 500;
            position: relative;
            overflow: hidden;
            white-space: nowrap;
            flex: 1;
            min-width: 0;
            max-width: 200px;
        }

        .bar-left-text-content {
            display: inline-block;
            transition: transform 0.3s ease;
        }

        .bar-left-text-content.slide-in {
            animation: slideIn 0.3s ease forwards;
        }

        .bar-controls {
            display: flex;
            gap: 4px;
            align-items: center;
            flex-shrink: 0;
            width: 120px;
            justify-content: flex-end;
            box-sizing: border-box;
            padding: 4px;
        }

        .toggle-button {
            display: flex;
            align-items: center;
            gap: 5px;
            background: transparent;
            color: rgba(255, 255, 255, 0.9);
            border: none;
            outline: none;
            box-shadow: none;
            padding: 4px 8px;
            border-radius: 5px;
            font-size: 11px;
            font-weight: 500;
            cursor: pointer;
            height: 24px;
            white-space: nowrap;
            transition: background-color 0.15s ease;
            justify-content: center;
        }

        .toggle-button:hover {
            background: rgba(255, 255, 255, 0.1);
        }

        .toggle-button svg {
            flex-shrink: 0;
            width: 12px;
            height: 12px;
        }

        .copy-button {
            background: transparent;
            color: rgba(255, 255, 255, 0.9);
            border: none;
            outline: none;
            box-shadow: none;
            padding: 4px;
            border-radius: 3px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            min-width: 24px;
            height: 24px;
            flex-shrink: 0;
            transition: background-color 0.15s ease;
            position: relative;
            overflow: hidden;
        }

        .copy-button:hover {
            background: rgba(255, 255, 255, 0.15);
        }

        .copy-button svg {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            transition: opacity 0.2s ease-in-out, transform 0.2s ease-in-out;
        }

        .copy-button .check-icon {
            opacity: 0;
            transform: translate(-50%, -50%) scale(0.5);
        }

        .copy-button.copied .copy-icon {
            opacity: 0;
            transform: translate(-50%, -50%) scale(0.5);
        }

        .copy-button.copied .check-icon {
            opacity: 1;
            transform: translate(-50%, -50%) scale(1);
        }

        .timer {
            font-family: 'Monaco', 'Menlo', monospace;
            font-size: 10px;
            color: rgba(255, 255, 255, 0.7);
        }
        
        /* ────────────────[ GLASS BYPASS ]─────────────── */
        :host-context(body.has-glass) .assistant-container,
        :host-context(body.has-glass) .top-bar,
        :host-context(body.has-glass) .toggle-button,
        :host-context(body.has-glass) .copy-button,
        :host-context(body.has-glass) .transcription-container,
        :host-context(body.has-glass) .insights-container,
        :host-context(body.has-glass) .stt-message,
        :host-context(body.has-glass) .outline-item,
        :host-context(body.has-glass) .request-item,
        :host-context(body.has-glass) .markdown-content,
        :host-context(body.has-glass) .insights-container pre,
        :host-context(body.has-glass) .insights-container p code,
        :host-context(body.has-glass) .insights-container pre code {
            background: transparent !important;
            border: none !important;
            outline: none !important;
            box-shadow: none !important;
            filter: none !important;
            backdrop-filter: none !important;
        }

        :host-context(body.has-glass) .assistant-container::before,
        :host-context(body.has-glass) .assistant-container::after {
            display: none !important;
        }

        :host-context(body.has-glass) .toggle-button:hover,
        :host-context(body.has-glass) .copy-button:hover,
        :host-context(body.has-glass) .outline-item:hover,
        :host-context(body.has-glass) .request-item.clickable:hover,
        :host-context(body.has-glass) .markdown-content:hover {
            background: transparent !important;
            transform: none !important;
        }

        :host-context(body.has-glass) .transcription-container::-webkit-scrollbar-track,
        :host-context(body.has-glass) .transcription-container::-webkit-scrollbar-thumb,
        :host-context(body.has-glass) .insights-container::-webkit-scrollbar-track,
        :host-context(body.has-glass) .insights-container::-webkit-scrollbar-thumb {
            background: transparent !important;
        }
        :host-context(body.has-glass) * {
            animation: none !important;
            transition: none !important;
            transform: none !important;
            filter: none !important;
            backdrop-filter: none !important;
            box-shadow: none !important;
        }

        :host-context(body.has-glass) .assistant-container,
        :host-context(body.has-glass) .stt-message,
        :host-context(body.has-glass) .toggle-button,
        :host-context(body.has-glass) .copy-button {
            border-radius: 0 !important;
        }

        :host-context(body.has-glass) ::-webkit-scrollbar,
        :host-context(body.has-glass) ::-webkit-scrollbar-track,
        :host-context(body.has-glass) ::-webkit-scrollbar-thumb {
            background: transparent !important;
            width: 0 !important;      /* 스크롤바 자체 숨기기 */
        }
        :host-context(body.has-glass) .assistant-container,
        :host-context(body.has-glass) .top-bar,
        :host-context(body.has-glass) .toggle-button,
        :host-context(body.has-glass) .copy-button,
        :host-context(body.has-glass) .transcription-container,
        :host-context(body.has-glass) .insights-container,
        :host-context(body.has-glass) .stt-message,
        :host-context(body.has-glass) .outline-item,
        :host-context(body.has-glass) .request-item,
        :host-context(body.has-glass) .markdown-content,
        :host-context(body.has-glass) .insights-container pre,
        :host-context(body.has-glass) .insights-container p code,
        :host-context(body.has-glass) .insights-container pre code {
            background: transparent !important;
            border: none !important;
            outline: none !important;
            box-shadow: none !important;
            filter: none !important;
            backdrop-filter: none !important;
        }

        :host-context(body.has-glass) .assistant-container::before,
        :host-context(body.has-glass) .assistant-container::after {
            display: none !important;
        }

        :host-context(body.has-glass) .toggle-button:hover,
        :host-context(body.has-glass) .copy-button:hover,
        :host-context(body.has-glass) .outline-item:hover,
        :host-context(body.has-glass) .request-item.clickable:hover,
        :host-context(body.has-glass) .markdown-content:hover {
            background: transparent !important;
            transform: none !important;
        }

        :host-context(body.has-glass) .transcription-container::-webkit-scrollbar-track,
        :host-context(body.has-glass) .transcription-container::-webkit-scrollbar-thumb,
        :host-context(body.has-glass) .insights-container::-webkit-scrollbar-track,
        :host-context(body.has-glass) .insights-container::-webkit-scrollbar-thumb {
            background: transparent !important;
        }
        :host-context(body.has-glass) * {
            animation: none !important;
            transition: none !important;
            transform: none !important;
            filter: none !important;
            backdrop-filter: none !important;
            box-shadow: none !important;
        }

        :host-context(body.has-glass) .assistant-container,
        :host-context(body.has-glass) .stt-message,
        :host-context(body.has-glass) .toggle-button,
        :host-context(body.has-glass) .copy-button {
            border-radius: 0 !important;
        }

        :host-context(body.has-glass) ::-webkit-scrollbar,
        :host-context(body.has-glass) ::-webkit-scrollbar-track,
        :host-context(body.has-glass) ::-webkit-scrollbar-thumb {
            background: transparent !important;
            width: 0 !important;
        }
    `;
  static properties = {
    viewMode: { type: String },
    isHovering: { type: Boolean },
    isAnimating: { type: Boolean },
    copyState: { type: String },
    elapsedTime: { type: String },
    captureStartTime: { type: Number },
    isSessionActive: { type: Boolean },
    hasCompletedRecording: { type: Boolean }
  };
  constructor() {
    super();
    this.isSessionActive = false;
    this.hasCompletedRecording = false;
    this.viewMode = "insights";
    this.isHovering = false;
    this.isAnimating = false;
    this.elapsedTime = "00:00";
    this.captureStartTime = null;
    this.timerInterval = null;
    this.adjustHeightThrottle = null;
    this.isThrottled = false;
    this.copyState = "idle";
    this.copyTimeout = null;
    this.adjustWindowHeight = this.adjustWindowHeight.bind(this);
  }
  connectedCallback() {
    super.connectedCallback();
    if (this.isSessionActive) {
      this.startTimer();
    }
    if (window.api) {
      window.api.listenView.onSessionStateChanged((event, { isActive }) => {
        const wasActive = this.isSessionActive;
        this.isSessionActive = isActive;
        if (!wasActive && isActive) {
          this.hasCompletedRecording = false;
          this.startTimer();
          this.updateComplete.then(() => {
            const sttView = this.shadowRoot.querySelector("stt-view");
            const summaryView = this.shadowRoot.querySelector("summary-view");
            if (sttView) sttView.resetTranscript();
            if (summaryView) summaryView.resetAnalysis();
          });
          this.requestUpdate();
        }
        if (wasActive && !isActive) {
          this.hasCompletedRecording = true;
          this.stopTimer();
          this.requestUpdate();
        }
      });
    }
  }
  disconnectedCallback() {
    super.disconnectedCallback();
    this.stopTimer();
    if (this.adjustHeightThrottle) {
      clearTimeout(this.adjustHeightThrottle);
      this.adjustHeightThrottle = null;
    }
    if (this.copyTimeout) {
      clearTimeout(this.copyTimeout);
    }
  }
  startTimer() {
    this.captureStartTime = Date.now();
    this.timerInterval = setInterval(() => {
      const elapsed = Math.floor((Date.now() - this.captureStartTime) / 1e3);
      const minutes = Math.floor(elapsed / 60).toString().padStart(2, "0");
      const seconds = (elapsed % 60).toString().padStart(2, "0");
      this.elapsedTime = `${minutes}:${seconds}`;
      this.requestUpdate();
    }, 1e3);
  }
  stopTimer() {
    if (this.timerInterval) {
      clearInterval(this.timerInterval);
      this.timerInterval = null;
    }
  }
  adjustWindowHeight() {
    if (!window.api) return;
    this.updateComplete.then(() => {
      const topBar = this.shadowRoot.querySelector(".top-bar");
      const activeContent = this.viewMode === "transcript" ? this.shadowRoot.querySelector("stt-view") : this.shadowRoot.querySelector("summary-view");
      if (!topBar || !activeContent) return;
      const topBarHeight = topBar.offsetHeight;
      const contentHeight = activeContent.scrollHeight;
      const idealHeight = topBarHeight + contentHeight;
      const targetHeight = Math.min(700, idealHeight);
      console.log(
        `[Height Adjusted] Mode: ${this.viewMode}, TopBar: ${topBarHeight}px, Content: ${contentHeight}px, Ideal: ${idealHeight}px, Target: ${targetHeight}px`
      );
      window.api.listenView.adjustWindowHeight("listen", targetHeight);
    }).catch((error) => {
      console.error("Error in adjustWindowHeight:", error);
    });
  }
  toggleViewMode() {
    this.viewMode = this.viewMode === "insights" ? "transcript" : "insights";
    this.requestUpdate();
  }
  handleCopyHover(isHovering) {
    this.isHovering = isHovering;
    if (isHovering) {
      this.isAnimating = true;
    } else {
      this.isAnimating = false;
    }
    this.requestUpdate();
  }
  async handleCopy() {
    if (this.copyState === "copied") return;
    let textToCopy = "";
    if (this.viewMode === "transcript") {
      const sttView = this.shadowRoot.querySelector("stt-view");
      textToCopy = sttView ? sttView.getTranscriptText() : "";
    } else {
      const summaryView = this.shadowRoot.querySelector("summary-view");
      textToCopy = summaryView ? summaryView.getSummaryText() : "";
    }
    try {
      await navigator.clipboard.writeText(textToCopy);
      console.log("Content copied to clipboard");
      this.copyState = "copied";
      this.requestUpdate();
      if (this.copyTimeout) {
        clearTimeout(this.copyTimeout);
      }
      this.copyTimeout = setTimeout(() => {
        this.copyState = "idle";
        this.requestUpdate();
      }, 1500);
    } catch (err) {
      console.error("Failed to copy:", err);
    }
  }
  adjustWindowHeightThrottled() {
    if (this.isThrottled) {
      return;
    }
    this.adjustWindowHeight();
    this.isThrottled = true;
    this.adjustHeightThrottle = setTimeout(() => {
      this.isThrottled = false;
    }, 16);
  }
  updated(changedProperties) {
    super.updated(changedProperties);
    if (changedProperties.has("viewMode")) {
      this.adjustWindowHeight();
    }
  }
  handleSttMessagesUpdated(event) {
    this.adjustWindowHeightThrottled();
  }
  firstUpdated() {
    super.firstUpdated();
    setTimeout(() => this.adjustWindowHeight(), 200);
  }
  render() {
    const displayText = this.isHovering ? this.viewMode === "transcript" ? "Copy Transcript" : "Copy Glass Analysis" : this.viewMode === "insights" ? `Live insights` : `Glass is Listening ${this.elapsedTime}`;
    return H`
            <div class="assistant-container">
                <div class="top-bar">
                    <div class="bar-left-text">
                        <span class="bar-left-text-content ${this.isAnimating ? "slide-in" : ""}">${displayText}</span>
                    </div>
                    <div class="bar-controls">
                        <button class="toggle-button" @click=${this.toggleViewMode}>
                            ${this.viewMode === "insights" ? H`
                                      <svg width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                          <path d="M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7z" />
                                          <circle cx="12" cy="12" r="3" />
                                      </svg>
                                      <span>Show Transcript</span>
                                  ` : H`
                                      <svg width="8" height="8" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                          <path d="M9 11l3 3L22 4" />
                                          <path d="M22 12v7a2 2 0 01-2 2H4a2 2 0 01-2-2V5a2 2 0 012-2h11" />
                                      </svg>
                                      <span>Show Insights</span>
                                  `}
                        </button>
                        <button
                            class="copy-button ${this.copyState === "copied" ? "copied" : ""}"
                            @click=${this.handleCopy}
                            @mouseenter=${() => this.handleCopyHover(true)}
                            @mouseleave=${() => this.handleCopyHover(false)}
                        >
                            <svg class="copy-icon" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <rect x="9" y="9" width="13" height="13" rx="2" ry="2" />
                                <path d="M5 15H4a2 2 0 01-2-2V4a2 2 0 012-2h9a2 2 0 012 2v1" />
                            </svg>
                            <svg class="check-icon" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2.5">
                                <path d="M20 6L9 17l-5-5" />
                            </svg>
                        </button>
                    </div>
                </div>

                <stt-view 
                    .isVisible=${this.viewMode === "transcript"}
                    @stt-messages-updated=${this.handleSttMessagesUpdated}
                ></stt-view>

                <summary-view 
                    .isVisible=${this.viewMode === "insights"}
                    .hasCompletedRecording=${this.hasCompletedRecording}
                ></summary-view>
            </div>
        `;
  }
};
customElements.define("listen-view", ListenView);

// src/ui/assets/smd.js
var DOCUMENT = 1;
var PARAGRAPH = 2;
var HEADING_1 = 3;
var HEADING_2 = 4;
var HEADING_3 = 5;
var HEADING_4 = 6;
var HEADING_5 = 7;
var HEADING_6 = 8;
var CODE_BLOCK = 9;
var CODE_FENCE = 10;
var CODE_INLINE = 11;
var ITALIC_AST = 12;
var ITALIC_UND = 13;
var STRONG_AST = 14;
var STRONG_UND = 15;
var STRIKE = 16;
var LINK = 17;
var RAW_URL = 18;
var IMAGE = 19;
var BLOCKQUOTE = 20;
var LINE_BREAK = 21;
var RULE = 22;
var LIST_UNORDERED = 23;
var LIST_ORDERED = 24;
var LIST_ITEM = 25;
var CHECKBOX = 26;
var TABLE = 27;
var TABLE_ROW = 28;
var TABLE_CELL = 29;
var EQUATION_BLOCK = 30;
var EQUATION_INLINE = 31;
var NEWLINE = 101;
var MAYBE_URL = 102;
var MAYBE_TASK = 103;
var MAYBE_BR = 104;
var MAYBE_EQ_BLOCK = 105;
var HREF = 1;
var SRC = 2;
var LANG = 4;
var CHECKED = 8;
var START = 16;
function attr_to_html_attr(type) {
  switch (type) {
    case HREF:
      return "href";
    case SRC:
      return "src";
    case LANG:
      return "class";
    case CHECKED:
      return "checked";
    case START:
      return "start";
  }
}
var level_to_heading = (level) => {
  switch (level) {
    case 1:
      return HEADING_1;
    case 2:
      return HEADING_2;
    case 3:
      return HEADING_3;
    case 4:
      return HEADING_4;
    case 5:
      return HEADING_5;
    default:
      return HEADING_6;
  }
};
var heading_from_level = level_to_heading;
var TOKEN_ARRAY_CAP = 24;
function parser(renderer) {
  const tokens = new Uint32Array(TOKEN_ARRAY_CAP);
  tokens[0] = DOCUMENT;
  return {
    renderer,
    text: "",
    pending: "",
    tokens,
    len: 0,
    token: DOCUMENT,
    fence_end: 0,
    blockquote_idx: 0,
    hr_char: "",
    hr_chars: 0,
    fence_start: 0,
    spaces: new Uint8Array(TOKEN_ARRAY_CAP),
    indent: "",
    indent_len: 0,
    table_state: 0
  };
}
function parser_end(p2) {
  if (p2.pending.length > 0) {
    parser_write(p2, "\n");
  }
}
function add_text(p2) {
  if (p2.text.length === 0) return;
  console.assert(p2.len > 0, "Never adding text to root");
  p2.renderer.add_text(p2.renderer.data, p2.text);
  p2.text = "";
}
function end_token(p2) {
  console.assert(p2.len > 0, "No nodes to end");
  p2.len -= 1;
  p2.token = /** @type {Token} */
  p2.tokens[p2.len];
  p2.renderer.end_token(p2.renderer.data);
}
function add_token(p2, token) {
  if ((p2.tokens[p2.len] === LIST_ORDERED || p2.tokens[p2.len] === LIST_UNORDERED) && token !== LIST_ITEM) {
    end_token(p2);
  }
  p2.len += 1;
  p2.tokens[p2.len] = token;
  p2.token = token;
  p2.renderer.add_token(p2.renderer.data, token);
}
function idx_of_token(p2, token, start_idx) {
  while (start_idx <= p2.len) {
    if (p2.tokens[start_idx] === token) {
      return start_idx;
    }
    start_idx += 1;
  }
  return -1;
}
function end_tokens_to_len(p2, len) {
  p2.fence_start = 0;
  while (p2.len > len) {
    end_token(p2);
  }
}
function end_tokens_to_indent(p2, indent) {
  let idx = 0;
  for (let i2 = 0; i2 <= p2.len; i2 += 1) {
    indent -= p2.spaces[i2];
    if (indent < 0) {
      break;
    }
    switch (p2.tokens[i2]) {
      case CODE_BLOCK:
      case CODE_FENCE:
      case BLOCKQUOTE:
      case LIST_ITEM:
        idx = i2;
        break;
    }
  }
  while (p2.len > idx) {
    end_token(p2);
  }
  return indent;
}
function continue_or_add_list(p2, list_token) {
  let list_idx = -1;
  let item_idx = -1;
  for (let i2 = p2.blockquote_idx + 1; i2 <= p2.len; i2 += 1) {
    if (p2.tokens[i2] === LIST_ITEM) {
      if (p2.indent_len < p2.spaces[i2]) {
        item_idx = -1;
        break;
      }
      item_idx = i2;
    } else if (p2.tokens[i2] === list_token) {
      list_idx = i2;
    }
  }
  if (item_idx === -1) {
    if (list_idx === -1) {
      end_tokens_to_len(p2, p2.blockquote_idx);
      add_token(p2, list_token);
      return true;
    }
    end_tokens_to_len(p2, list_idx);
    return false;
  }
  end_tokens_to_len(p2, item_idx);
  add_token(p2, list_token);
  return true;
}
function add_list_item(p2, prefix_length) {
  add_token(p2, LIST_ITEM);
  p2.spaces[p2.len] = p2.indent_len + prefix_length;
  clear_root_pending(p2);
  p2.token = MAYBE_TASK;
}
function clear_root_pending(p2) {
  p2.indent = "";
  p2.indent_len = 0;
  p2.pending = "";
}
function is_digit(charcode) {
  switch (charcode) {
    case 48:
    case 49:
    case 50:
    case 51:
    case 52:
    case 53:
    case 54:
    case 55:
    case 56:
    case 57:
      return true;
    default:
      return false;
  }
}
function is_delimeter(charcode) {
  switch (charcode) {
    //   " "      ":"      ";"      ")"      ","      "!"      "."      "?"      "]"      "\n"
    case 32:
    case 58:
    case 59:
    case 41:
    case 44:
    case 33:
    case 46:
    case 63:
    case 93:
    case 10:
      return true;
    default:
      return false;
  }
}
function is_delimeter_or_number(charcode) {
  return is_digit(charcode) || is_delimeter(charcode);
}
function parser_write(p2, chunk) {
  for (const char of chunk) {
    if (p2.token === NEWLINE) {
      switch (char) {
        case " ":
          p2.indent_len += 1;
          continue;
        case "	":
          p2.indent_len += 4;
          continue;
      }
      let indent = end_tokens_to_indent(p2, p2.indent_len);
      p2.indent_len = 0;
      p2.token = p2.tokens[p2.len];
      if (indent > 0) {
        parser_write(p2, " ".repeat(indent));
      }
    }
    const pending_with_char = p2.pending + char;
    switch (p2.token) {
      case LINE_BREAK:
      case DOCUMENT:
      case BLOCKQUOTE:
      case LIST_ORDERED:
      case LIST_UNORDERED:
        console.assert(p2.text.length === 0, "Root should not have any text");
        switch (p2.pending[0]) {
          case void 0:
            p2.pending = char;
            continue;
          case " ":
            console.assert(p2.pending.length === 1);
            p2.pending = char;
            p2.indent += " ";
            p2.indent_len += 1;
            continue;
          case "	":
            console.assert(p2.pending.length === 1);
            p2.pending = char;
            p2.indent += "	";
            p2.indent_len += 4;
            continue;
          case "\n":
            console.assert(p2.pending.length === 1);
            if (p2.tokens[p2.len] === LIST_ITEM && p2.token === LINE_BREAK) {
              end_token(p2);
              clear_root_pending(p2);
              p2.pending = char;
              continue;
            }
            end_tokens_to_len(p2, p2.blockquote_idx);
            clear_root_pending(p2);
            p2.blockquote_idx = 0;
            p2.fence_start = 0;
            p2.pending = char;
            continue;
          /* Heading */
          case "#":
            switch (char) {
              case "#":
                if (p2.pending.length < 6) {
                  p2.pending = pending_with_char;
                  continue;
                }
                break;
              // fail
              case " ":
                end_tokens_to_indent(p2, p2.indent_len);
                add_token(p2, heading_from_level(p2.pending.length));
                clear_root_pending(p2);
                continue;
            }
            break;
          // fail
          /* Blockquote */
          case ">": {
            const next_blockquote_idx = idx_of_token(p2, BLOCKQUOTE, p2.blockquote_idx + 1);
            if (next_blockquote_idx === -1) {
              end_tokens_to_len(p2, p2.blockquote_idx);
              p2.blockquote_idx += 1;
              p2.fence_start = 0;
              add_token(p2, BLOCKQUOTE);
            } else {
              p2.blockquote_idx = next_blockquote_idx;
            }
            clear_root_pending(p2);
            p2.pending = char;
            continue;
          }
          /* Horizontal Rule
             "-- - --- - --"
          */
          case "-":
          case "*":
          case "_":
            if (p2.hr_chars === 0) {
              console.assert(p2.pending.length === 1, "Pending should be one character");
              p2.hr_chars = 1;
              p2.hr_char = p2.pending;
            }
            if (p2.hr_chars > 0) {
              switch (char) {
                case p2.hr_char:
                  p2.hr_chars += 1;
                  p2.pending = pending_with_char;
                  continue;
                case " ":
                  p2.pending = pending_with_char;
                  continue;
                case "\n":
                  if (p2.hr_chars < 3) break;
                  end_tokens_to_indent(p2, p2.indent_len);
                  p2.renderer.add_token(p2.renderer.data, RULE);
                  p2.renderer.end_token(p2.renderer.data);
                  clear_root_pending(p2);
                  p2.hr_chars = 0;
                  continue;
              }
              p2.hr_chars = 0;
            }
            if ("_" !== p2.pending[0] && " " === p2.pending[1]) {
              continue_or_add_list(p2, LIST_UNORDERED);
              add_list_item(p2, 2);
              parser_write(p2, pending_with_char.slice(2));
              continue;
            }
            break;
          // fail
          /* Code Fence */
          case "`":
            if (p2.pending.length < 3) {
              if ("`" === char) {
                p2.pending = pending_with_char;
                p2.fence_start = pending_with_char.length;
                continue;
              }
              p2.fence_start = 0;
              break;
            }
            switch (char) {
              case "`":
                if (p2.pending.length === p2.fence_start) {
                  p2.pending = pending_with_char;
                  p2.fence_start = pending_with_char.length;
                } else {
                  add_token(p2, PARAGRAPH);
                  clear_root_pending(p2);
                  p2.fence_start = 0;
                  parser_write(p2, pending_with_char);
                }
                continue;
              case "\n": {
                end_tokens_to_indent(p2, p2.indent_len);
                add_token(p2, CODE_FENCE);
                if (p2.pending.length > p2.fence_start) {
                  p2.renderer.set_attr(p2.renderer.data, LANG, p2.pending.slice(p2.fence_start));
                }
                clear_root_pending(p2);
                p2.token = NEWLINE;
                continue;
              }
              default:
                p2.pending = pending_with_char;
                continue;
            }
          /*
          List Unordered for '+'
          The other list types are handled with HORIZONTAL_RULE
          */
          case "+":
            if (" " !== char) break;
            continue_or_add_list(p2, LIST_UNORDERED);
            add_list_item(p2, 2);
            continue;
          /* List Ordered */
          case "0":
          case "1":
          case "2":
          case "3":
          case "4":
          case "5":
          case "6":
          case "7":
          case "8":
          case "9":
            if ("." === p2.pending[p2.pending.length - 1]) {
              if (" " !== char) break;
              if (continue_or_add_list(p2, LIST_ORDERED) && p2.pending !== "1.") {
                p2.renderer.set_attr(p2.renderer.data, START, p2.pending.slice(0, -1));
              }
              add_list_item(p2, p2.pending.length + 1);
              continue;
            } else {
              const char_code = char.charCodeAt(0);
              if (46 === char_code || // '.'
              is_digit(char_code)) {
                p2.pending = pending_with_char;
                continue;
              }
            }
            break;
          // fail
          /* Table */
          case "|":
            end_tokens_to_len(p2, p2.blockquote_idx);
            add_token(p2, TABLE);
            add_token(p2, TABLE_ROW);
            p2.pending = "";
            parser_write(p2, char);
            continue;
        }
        let to_write = pending_with_char;
        if (p2.token === LINE_BREAK) {
          p2.token = p2.tokens[p2.len];
          p2.renderer.add_token(p2.renderer.data, LINE_BREAK);
          p2.renderer.end_token(p2.renderer.data);
        } else if (p2.indent_len >= 4) {
          let code_start = 0;
          for (; code_start < 4; code_start += 1) {
            if (p2.indent[code_start] === "	") {
              code_start = code_start + 1;
              break;
            }
          }
          to_write = p2.indent.slice(code_start) + pending_with_char;
          add_token(p2, CODE_BLOCK);
        } else {
          add_token(p2, PARAGRAPH);
        }
        clear_root_pending(p2);
        parser_write(p2, to_write);
        continue;
      case TABLE:
        if (p2.table_state === 1) {
          switch (char) {
            case "-":
            case " ":
            case "|":
            case ":":
              p2.pending = pending_with_char;
              continue;
            case "\n":
              p2.table_state = 2;
              p2.pending = "";
              continue;
            default:
              end_token(p2);
              p2.table_state = 0;
              break;
          }
        } else {
          switch (p2.pending) {
            case "|":
              add_token(p2, TABLE_ROW);
              p2.pending = "";
              parser_write(p2, char);
              continue;
            case "\n":
              end_token(p2);
              p2.pending = "";
              p2.table_state = 0;
              parser_write(p2, char);
              continue;
          }
        }
        break;
      case TABLE_ROW:
        switch (p2.pending) {
          case "":
            break;
          case "|":
            add_token(p2, TABLE_CELL);
            end_token(p2);
            p2.pending = "";
            parser_write(p2, char);
            continue;
          case "\n":
            end_token(p2);
            p2.table_state = Math.min(p2.table_state + 1, 2);
            p2.pending = "";
            parser_write(p2, char);
            continue;
          default:
            add_token(p2, TABLE_CELL);
            parser_write(p2, char);
            continue;
        }
        break;
      case TABLE_CELL:
        if (p2.pending === "|") {
          add_text(p2);
          end_token(p2);
          p2.pending = "";
          parser_write(p2, char);
          continue;
        }
        break;
      case CODE_BLOCK:
        switch (pending_with_char) {
          case "\n    ":
          case "\n   	":
          case "\n  	":
          case "\n 	":
          case "\n	":
            p2.text += "\n";
            p2.pending = "";
            continue;
          case "\n":
          case "\n ":
          case "\n  ":
          case "\n   ":
            p2.pending = pending_with_char;
            continue;
          default:
            if (p2.pending.length !== 0) {
              add_text(p2);
              end_token(p2);
              p2.pending = char;
            } else {
              p2.text += char;
            }
            continue;
        }
      case CODE_FENCE:
        switch (char) {
          case "`":
            p2.pending = pending_with_char;
            continue;
          case "\n":
            if (pending_with_char.length === p2.fence_start + p2.fence_end + 1) {
              add_text(p2);
              end_token(p2);
              p2.pending = "";
              p2.fence_start = 0;
              p2.fence_end = 0;
              p2.token = NEWLINE;
              continue;
            }
            p2.token = NEWLINE;
            break;
          case " ":
            if (p2.pending[0] === "\n") {
              p2.pending = pending_with_char;
              p2.fence_end += 1;
              continue;
            }
            break;
        }
        p2.text += p2.pending;
        p2.pending = char;
        p2.fence_end = 1;
        continue;
      case CODE_INLINE:
        switch (char) {
          case "`":
            if (pending_with_char.length === p2.fence_start + Number(p2.pending[0] === " ")) {
              add_text(p2);
              end_token(p2);
              p2.pending = "";
              p2.fence_start = 0;
            } else {
              p2.pending = pending_with_char;
            }
            continue;
          case "\n":
            p2.text += p2.pending;
            p2.pending = "";
            p2.token = LINE_BREAK;
            p2.blockquote_idx = 0;
            add_text(p2);
            continue;
          /* Trim space before ` */
          case " ":
            p2.text += p2.pending;
            p2.pending = char;
            continue;
          default:
            p2.text += pending_with_char;
            p2.pending = "";
            continue;
        }
      /* Checkboxes */
      case MAYBE_TASK:
        switch (p2.pending.length) {
          case 0:
            if ("[" !== char) break;
            p2.pending = pending_with_char;
            continue;
          case 1:
            if (" " !== char && "x" !== char) break;
            p2.pending = pending_with_char;
            continue;
          case 2:
            if ("]" !== char) break;
            p2.pending = pending_with_char;
            continue;
          case 3:
            if (" " !== char) break;
            p2.renderer.add_token(p2.renderer.data, CHECKBOX);
            if ("x" === p2.pending[1]) {
              p2.renderer.set_attr(p2.renderer.data, CHECKED, "");
            }
            p2.renderer.end_token(p2.renderer.data);
            p2.pending = " ";
            continue;
        }
        p2.token = p2.tokens[p2.len];
        p2.pending = "";
        parser_write(p2, pending_with_char);
        continue;
      case STRONG_AST:
      case STRONG_UND: {
        let symbol = "*";
        let italic = ITALIC_AST;
        if (p2.token === STRONG_UND) {
          symbol = "_";
          italic = ITALIC_UND;
        }
        if (symbol === p2.pending) {
          add_text(p2);
          if (symbol === char) {
            end_token(p2);
            p2.pending = "";
            continue;
          }
          add_token(p2, italic);
          p2.pending = char;
          continue;
        }
        break;
      }
      case ITALIC_AST:
      case ITALIC_UND: {
        let symbol = "*";
        let strong = STRONG_AST;
        if (p2.token === ITALIC_UND) {
          symbol = "_";
          strong = STRONG_UND;
        }
        switch (p2.pending) {
          case symbol:
            if (symbol === char) {
              if (p2.tokens[p2.len - 1] === strong) {
                p2.pending = pending_with_char;
              } else {
                add_text(p2);
                add_token(p2, strong);
                p2.pending = "";
              }
            } else {
              add_text(p2);
              end_token(p2);
              p2.pending = char;
            }
            continue;
          case symbol + symbol:
            const italic = p2.token;
            add_text(p2);
            end_token(p2);
            end_token(p2);
            if (symbol !== char) {
              add_token(p2, italic);
              p2.pending = char;
            } else {
              p2.pending = "";
            }
            continue;
        }
        break;
      }
      case STRIKE:
        if ("~~" === pending_with_char) {
          add_text(p2);
          end_token(p2);
          p2.pending = "";
          continue;
        }
        break;
      case MAYBE_EQ_BLOCK:
        if (char === "\n") {
          add_text(p2);
          add_token(p2, EQUATION_BLOCK);
          p2.pending = "";
        } else {
          p2.token = p2.tokens[p2.len];
          if (p2.pending[0] === "\\") {
            p2.text += "[";
          } else {
            p2.text += "$$";
          }
          p2.pending = "";
          parser_write(p2, char);
        }
        continue;
      case EQUATION_BLOCK:
        if ("\\]" === pending_with_char || "$$" === pending_with_char) {
          add_text(p2);
          end_token(p2);
          p2.pending = "";
          continue;
        }
        break;
      case EQUATION_INLINE:
        if ("\\)" === pending_with_char || "$" === p2.pending[0]) {
          add_text(p2);
          end_token(p2);
          if (char === ")") {
            p2.pending = "";
          } else {
            p2.pending = char;
          }
          continue;
        }
        break;
      /* Raw URLs */
      case MAYBE_URL:
        if ("http://" === pending_with_char || "https://" === pending_with_char) {
          add_text(p2);
          add_token(p2, RAW_URL);
          p2.pending = pending_with_char;
          p2.text = pending_with_char;
        } else if ("http:/"[p2.pending.length] === char || "https:/"[p2.pending.length] === char) {
          p2.pending = pending_with_char;
        } else {
          p2.token = p2.tokens[p2.len];
          parser_write(p2, char);
        }
        continue;
      case LINK:
      case IMAGE:
        if ("]" === p2.pending) {
          add_text(p2);
          if ("(" === char) {
            p2.pending = pending_with_char;
          } else {
            end_token(p2);
            p2.pending = char;
          }
          continue;
        }
        if ("]" === p2.pending[0] && "(" === p2.pending[1]) {
          if (")" === char) {
            const type = p2.token === LINK ? HREF : SRC;
            const url = p2.pending.slice(2);
            p2.renderer.set_attr(p2.renderer.data, type, url);
            end_token(p2);
            p2.pending = "";
          } else {
            p2.pending += char;
          }
          continue;
        }
        break;
      case RAW_URL:
        if (" " === char || "\n" === char || "\\" === char) {
          p2.renderer.set_attr(p2.renderer.data, HREF, p2.pending);
          add_text(p2);
          end_token(p2);
          p2.pending = char;
        } else {
          p2.text += char;
          p2.pending = pending_with_char;
        }
        continue;
      case MAYBE_BR:
        if (pending_with_char.startsWith("<br")) {
          if (
            /* "<br" */
            pending_with_char.length === 3 || /* "<br " */
            char === " " || /* "<br/" | "<br /" */
            char === "/" && (pending_with_char.length === 4 || p2.pending[p2.pending.length - 1] === " ")
          ) {
            p2.pending = pending_with_char;
            continue;
          }
          if (char === ">") {
            add_text(p2);
            p2.token = p2.tokens[p2.len];
            p2.renderer.add_token(p2.renderer.data, LINE_BREAK);
            p2.renderer.end_token(p2.renderer.data);
            p2.pending = "";
            continue;
          }
        }
        p2.token = p2.tokens[p2.len];
        p2.text += "<";
        p2.pending = p2.pending.slice(1);
        parser_write(p2, char);
        continue;
    }
    switch (p2.pending[0]) {
      /* Escape character */
      case "\\":
        if (p2.token === IMAGE || p2.token === EQUATION_BLOCK || p2.token === EQUATION_INLINE)
          break;
        switch (char) {
          case "(":
            add_text(p2);
            add_token(p2, EQUATION_INLINE);
            p2.pending = "";
            continue;
          case "[":
            p2.token = MAYBE_EQ_BLOCK;
            p2.pending = pending_with_char;
            continue;
          case "\n":
            p2.pending = char;
            continue;
          default:
            let charcode = char.charCodeAt(0);
            p2.pending = "";
            p2.text += is_digit(charcode) || // 0-9
            charcode >= 65 && charcode <= 90 || // A-Z
            charcode >= 97 && charcode <= 122 ? pending_with_char : char;
            continue;
        }
      /* Newline */
      case "\n":
        switch (p2.token) {
          case IMAGE:
          case EQUATION_BLOCK:
          case EQUATION_INLINE:
            break;
          case HEADING_1:
          case HEADING_2:
          case HEADING_3:
          case HEADING_4:
          case HEADING_5:
          case HEADING_6:
            add_text(p2);
            end_tokens_to_len(p2, p2.blockquote_idx);
            p2.blockquote_idx = 0;
            p2.pending = char;
            continue;
          default:
            add_text(p2);
            p2.pending = char;
            p2.token = LINE_BREAK;
            p2.blockquote_idx = 0;
            continue;
        }
        break;
      /* <br> */
      case "<":
        if (p2.token !== IMAGE && p2.token !== EQUATION_BLOCK && p2.token !== EQUATION_INLINE) {
          add_text(p2);
          p2.pending = pending_with_char;
          p2.token = MAYBE_BR;
          continue;
        }
        break;
      /* `Code Inline` */
      case "`":
        if (p2.token === IMAGE) break;
        if ("`" === char) {
          p2.fence_start += 1;
          p2.pending = pending_with_char;
        } else {
          p2.fence_start += 1;
          add_text(p2);
          add_token(p2, CODE_INLINE);
          p2.text = " " === char || "\n" === char ? "" : char;
          p2.pending = "";
        }
        continue;
      case "_":
      case "*": {
        if (p2.token === IMAGE || p2.token === EQUATION_BLOCK || p2.token === EQUATION_INLINE || p2.token === STRONG_AST)
          break;
        let italic = ITALIC_AST;
        let strong = STRONG_AST;
        const symbol = p2.pending[0];
        if ("_" === symbol) {
          italic = ITALIC_UND;
          strong = STRONG_UND;
        }
        if (p2.pending.length === 1) {
          if (symbol === char) {
            p2.pending = pending_with_char;
            continue;
          }
          if (" " !== char && "\n" !== char) {
            add_text(p2);
            add_token(p2, italic);
            p2.pending = char;
            continue;
          }
        } else {
          if (symbol === char) {
            add_text(p2);
            add_token(p2, strong);
            add_token(p2, italic);
            p2.pending = "";
            continue;
          }
          if (" " !== char && "\n" !== char) {
            add_text(p2);
            add_token(p2, strong);
            p2.pending = char;
            continue;
          }
        }
        break;
      }
      case "~":
        if (p2.token !== IMAGE && p2.token !== STRIKE) {
          if ("~" === p2.pending) {
            if ("~" === char) {
              p2.pending = pending_with_char;
              continue;
            }
          } else {
            if (" " !== char && "\n" !== char) {
              add_text(p2);
              add_token(p2, STRIKE);
              p2.pending = char;
              continue;
            }
          }
        }
        break;
      /* $eq$ | $$eq$$ */
      case "$":
        if (p2.token !== IMAGE && p2.token !== STRIKE && "$" === p2.pending) {
          if ("$" === char) {
            p2.token = MAYBE_EQ_BLOCK;
            p2.pending = pending_with_char;
            continue;
          } else if (is_delimeter_or_number(char.charCodeAt(0))) {
            break;
          } else {
            add_text(p2);
            add_token(p2, EQUATION_INLINE);
            p2.pending = char;
            continue;
          }
        }
        break;
      /* [Image](url) */
      case "[":
        if (p2.token !== IMAGE && p2.token !== LINK && p2.token !== EQUATION_BLOCK && p2.token !== EQUATION_INLINE && "]" !== char) {
          add_text(p2);
          add_token(p2, LINK);
          p2.pending = char;
          continue;
        }
        break;
      /* ![Image](url) */
      case "!":
        if (!(p2.token === IMAGE) && "[" === char) {
          add_text(p2);
          add_token(p2, IMAGE);
          p2.pending = "";
          continue;
        }
        break;
      /* Trim spaces */
      case " ":
        if (p2.pending.length === 1 && " " === char) {
          continue;
        }
        break;
    }
    if (p2.token !== IMAGE && p2.token !== LINK && p2.token !== EQUATION_BLOCK && p2.token !== EQUATION_INLINE && "h" === char && (" " === p2.pending || "" === p2.pending)) {
      p2.text += p2.pending;
      p2.pending = char;
      p2.token = MAYBE_URL;
      continue;
    }
    p2.text += p2.pending;
    p2.pending = char;
  }
  add_text(p2);
}
function default_renderer(root) {
  return {
    add_token: default_add_token,
    end_token: default_end_token,
    add_text: default_add_text,
    set_attr: default_set_attr,
    data: {
      nodes: (
        /**@type {*}*/
        [root, , , , ,]
      ),
      index: 0
    }
  };
}
function default_add_token(data, type) {
  let parent = data.nodes[data.index];
  let slot;
  switch (type) {
    case DOCUMENT:
      return;
    // document is provided
    case BLOCKQUOTE:
      slot = document.createElement("blockquote");
      break;
    case PARAGRAPH:
      slot = document.createElement("p");
      break;
    case LINE_BREAK:
      slot = document.createElement("br");
      break;
    case RULE:
      slot = document.createElement("hr");
      break;
    case HEADING_1:
      slot = document.createElement("h1");
      break;
    case HEADING_2:
      slot = document.createElement("h2");
      break;
    case HEADING_3:
      slot = document.createElement("h3");
      break;
    case HEADING_4:
      slot = document.createElement("h4");
      break;
    case HEADING_5:
      slot = document.createElement("h5");
      break;
    case HEADING_6:
      slot = document.createElement("h6");
      break;
    case ITALIC_AST:
    case ITALIC_UND:
      slot = document.createElement("em");
      break;
    case STRONG_AST:
    case STRONG_UND:
      slot = document.createElement("strong");
      break;
    case STRIKE:
      slot = document.createElement("s");
      break;
    case CODE_INLINE:
      slot = document.createElement("code");
      break;
    case RAW_URL:
    case LINK:
      slot = document.createElement("a");
      break;
    case IMAGE:
      slot = document.createElement("img");
      break;
    case LIST_UNORDERED:
      slot = document.createElement("ul");
      break;
    case LIST_ORDERED:
      slot = document.createElement("ol");
      break;
    case LIST_ITEM:
      slot = document.createElement("li");
      break;
    case CHECKBOX:
      let checkbox = slot = document.createElement("input");
      checkbox.type = "checkbox";
      checkbox.disabled = true;
      break;
    case CODE_BLOCK:
    case CODE_FENCE:
      parent = parent.appendChild(document.createElement("pre"));
      slot = document.createElement("code");
      break;
    case TABLE:
      slot = document.createElement("table");
      break;
    case TABLE_ROW:
      switch (parent.children.length) {
        case 0:
          parent = parent.appendChild(document.createElement("thead"));
          break;
        case 1:
          parent = parent.appendChild(document.createElement("tbody"));
          break;
        default:
          parent = parent.children[1];
      }
      slot = document.createElement("tr");
      break;
    case TABLE_CELL:
      slot = document.createElement(parent.parentElement?.tagName === "THEAD" ? "th" : "td");
      break;
    case EQUATION_BLOCK:
      slot = document.createElement("equation-block");
      break;
    case EQUATION_INLINE:
      slot = document.createElement("equation-inline");
      break;
  }
  data.nodes[++data.index] = parent.appendChild(slot);
}
function default_end_token(data) {
  data.index -= 1;
}
function default_add_text(data, text) {
  data.nodes[data.index].appendChild(document.createTextNode(text));
}
function default_set_attr(data, type, value) {
  data.nodes[data.index].setAttribute(attr_to_html_attr(type), value);
}

// src/ui/ask/AskView.js
var AskView = class extends ut {
  static properties = {
    currentResponse: { type: String },
    currentQuestion: { type: String },
    isLoading: { type: Boolean },
    copyState: { type: String },
    isHovering: { type: Boolean },
    hoveredLineIndex: { type: Number },
    lineCopyState: { type: Object },
    showTextInput: { type: Boolean },
    headerText: { type: String },
    headerAnimating: { type: Boolean },
    isStreaming: { type: Boolean }
  };
  static styles = r`
        :host {
            display: block;
            width: 100%;
            height: 100%;
            color: white;
            transform: translate3d(0, 0, 0);
            backface-visibility: hidden;
            transition: transform 0.2s cubic-bezier(0.23, 1, 0.32, 1), opacity 0.2s ease-out;
            will-change: transform, opacity;
        }

        :host(.hiding) {
            animation: slideUp 0.3s cubic-bezier(0.4, 0, 0.6, 1) forwards;
        }

        :host(.showing) {
            animation: slideDown 0.35s cubic-bezier(0.34, 1.56, 0.64, 1) forwards;
        }

        :host(.hidden) {
            opacity: 0;
            transform: translateY(-150%) scale(0.85);
            pointer-events: none;
        }

        @keyframes slideUp {
            0% {
                opacity: 1;
                transform: translateY(0) scale(1);
                filter: blur(0px);
            }
            30% {
                opacity: 0.7;
                transform: translateY(-20%) scale(0.98);
                filter: blur(0.5px);
            }
            70% {
                opacity: 0.3;
                transform: translateY(-80%) scale(0.92);
                filter: blur(1.5px);
            }
            100% {
                opacity: 0;
                transform: translateY(-150%) scale(0.85);
                filter: blur(2px);
            }
        }

        @keyframes slideDown {
            0% {
                opacity: 0;
                transform: translateY(-150%) scale(0.85);
                filter: blur(2px);
            }
            30% {
                opacity: 0.5;
                transform: translateY(-50%) scale(0.92);
                filter: blur(1px);
            }
            65% {
                opacity: 0.9;
                transform: translateY(-5%) scale(0.99);
                filter: blur(0.2px);
            }
            85% {
                opacity: 0.98;
                transform: translateY(2%) scale(1.005);
                filter: blur(0px);
            }
            100% {
                opacity: 1;
                transform: translateY(0) scale(1);
                filter: blur(0px);
            }
        }

        * {
            font-family: 'Helvetica Neue', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            cursor: default;
            user-select: none;
        }

        /* Allow text selection in assistant responses */
        .response-container, .response-container * {
            user-select: text !important;
            cursor: text !important;
        }

        .response-container pre {
            background: rgba(0, 0, 0, 0.4) !important;
            border-radius: 8px !important;
            padding: 12px !important;
            margin: 8px 0 !important;
            overflow-x: auto !important;
            border: 1px solid rgba(255, 255, 255, 0.1) !important;
            white-space: pre !important;
            word-wrap: normal !important;
            word-break: normal !important;
        }

        .response-container code {
            font-family: 'Monaco', 'Menlo', 'Consolas', monospace !important;
            font-size: 11px !important;
            background: transparent !important;
            white-space: pre !important;
            word-wrap: normal !important;
            word-break: normal !important;
        }

        .response-container pre code {
            white-space: pre !important;
            word-wrap: normal !important;
            word-break: normal !important;
            display: block !important;
        }

        .response-container p code {
            background: rgba(255, 255, 255, 0.1) !important;
            padding: 2px 4px !important;
            border-radius: 3px !important;
            color: #ffd700 !important;
        }

        .hljs-keyword {
            color: #ff79c6 !important;
        }
        .hljs-string {
            color: #f1fa8c !important;
        }
        .hljs-comment {
            color: #6272a4 !important;
        }
        .hljs-number {
            color: #bd93f9 !important;
        }
        .hljs-function {
            color: #50fa7b !important;
        }
        .hljs-variable {
            color: #8be9fd !important;
        }
        .hljs-built_in {
            color: #ffb86c !important;
        }
        .hljs-title {
            color: #50fa7b !important;
        }
        .hljs-attr {
            color: #50fa7b !important;
        }
        .hljs-tag {
            color: #ff79c6 !important;
        }

        .ask-container {
            display: flex;
            flex-direction: column;
            height: 100%;
            width: 100%;
            background: rgba(0, 0, 0, 0.6);
            border-radius: 12px;
            outline: 0.5px rgba(255, 255, 255, 0.3) solid;
            outline-offset: -1px;
            backdrop-filter: blur(1px);
            box-sizing: border-box;
            position: relative;
            overflow: hidden;
        }

        .ask-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.15);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
            border-radius: 12px;
            filter: blur(10px);
            z-index: -1;
        }

        .response-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 16px;
            background: transparent;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            flex-shrink: 0;
        }

        .response-header.hidden {
            display: none;
        }

        .header-left {
            display: flex;
            align-items: center;
            gap: 8px;
            flex-shrink: 0;
        }

        .response-icon {
            width: 20px;
            height: 20px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-shrink: 0;
        }

        .response-icon svg {
            width: 12px;
            height: 12px;
            stroke: rgba(255, 255, 255, 0.9);
        }

        .response-label {
            font-size: 13px;
            font-weight: 500;
            color: rgba(255, 255, 255, 0.9);
            white-space: nowrap;
            position: relative;
            overflow: hidden;
        }

        .response-label.animating {
            animation: fadeInOut 0.3s ease-in-out;
        }

        @keyframes fadeInOut {
            0% {
                opacity: 1;
                transform: translateY(0);
            }
            50% {
                opacity: 0;
                transform: translateY(-10px);
            }
            100% {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .header-right {
            display: flex;
            align-items: center;
            gap: 8px;
            flex: 1;
            justify-content: flex-end;
        }

        .question-text {
            font-size: 13px;
            color: rgba(255, 255, 255, 0.7);
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            max-width: 300px;
            margin-right: 8px;
        }

        .header-controls {
            display: flex;
            gap: 8px;
            align-items: center;
            flex-shrink: 0;
        }

        .copy-button {
            background: transparent;
            color: rgba(255, 255, 255, 0.9);
            border: 1px solid rgba(255, 255, 255, 0.2);
            padding: 4px;
            border-radius: 3px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            min-width: 24px;
            height: 24px;
            flex-shrink: 0;
            transition: background-color 0.15s ease;
            position: relative;
            overflow: hidden;
        }

        .copy-button:hover {
            background: rgba(255, 255, 255, 0.15);
        }

        .copy-button svg {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            transition: opacity 0.2s ease-in-out, transform 0.2s ease-in-out;
        }

        .copy-button .check-icon {
            opacity: 0;
            transform: translate(-50%, -50%) scale(0.5);
        }

        .copy-button.copied .copy-icon {
            opacity: 0;
            transform: translate(-50%, -50%) scale(0.5);
        }

        .copy-button.copied .check-icon {
            opacity: 1;
            transform: translate(-50%, -50%) scale(1);
        }

        .close-button {
            background: rgba(255, 255, 255, 0.07);
            color: white;
            border: none;
            padding: 4px;
            border-radius: 20px;
            outline: 1px rgba(255, 255, 255, 0.3) solid;
            outline-offset: -1px;
            backdrop-filter: blur(0.5px);
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            width: 20px;
            height: 20px;
        }

        .close-button:hover {
            background: rgba(255, 255, 255, 0.1);
            color: rgba(255, 255, 255, 1);
        }

        .response-container {
            flex: 1;
            padding: 16px;
            padding-left: 48px;
            overflow-y: auto;
            font-size: 14px;
            line-height: 1.6;
            background: transparent;
            min-height: 0;
            max-height: 400px;
            position: relative;
        }

        .response-container.hidden {
            display: none;
        }

        .response-container::-webkit-scrollbar {
            width: 6px;
        }

        .response-container::-webkit-scrollbar-track {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 3px;
        }

        .response-container::-webkit-scrollbar-thumb {
            background: rgba(255, 255, 255, 0.2);
            border-radius: 3px;
        }

        .response-container::-webkit-scrollbar-thumb:hover {
            background: rgba(255, 255, 255, 0.3);
        }

        .loading-dots {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 6px;
            padding: 40px;
        }

        .loading-dot {
            width: 8px;
            height: 8px;
            background: rgba(255, 255, 255, 0.6);
            border-radius: 50%;
            animation: pulse 1.5s ease-in-out infinite;
        }

        .loading-dot:nth-child(1) {
            animation-delay: 0s;
        }

        .loading-dot:nth-child(2) {
            animation-delay: 0.2s;
        }

        .loading-dot:nth-child(3) {
            animation-delay: 0.4s;
        }

        @keyframes pulse {
            0%,
            80%,
            100% {
                opacity: 0.3;
                transform: scale(0.8);
            }
            40% {
                opacity: 1;
                transform: scale(1.2);
            }
        }

        .response-line {
            position: relative;
            padding: 2px 0;
            margin: 0;
            transition: background-color 0.15s ease;
        }

        .response-line:hover {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 4px;
        }

        .line-copy-button {
            position: absolute;
            left: -32px;
            top: 50%;
            transform: translateY(-50%);
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 3px;
            padding: 2px;
            cursor: pointer;
            opacity: 0;
            transition: opacity 0.15s ease, background-color 0.15s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            width: 20px;
            height: 20px;
        }

        .response-line:hover .line-copy-button {
            opacity: 1;
        }

        .line-copy-button:hover {
            background: rgba(255, 255, 255, 0.2);
        }

        .line-copy-button.copied {
            background: rgba(40, 167, 69, 0.3);
        }

        .line-copy-button svg {
            width: 12px;
            height: 12px;
            stroke: rgba(255, 255, 255, 0.9);
        }

        .text-input-container {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 12px 16px;
            background: rgba(0, 0, 0, 0.1);
            border-top: 1px solid rgba(255, 255, 255, 0.1);
            flex-shrink: 0;
            transition: opacity 0.1s ease-in-out, transform 0.1s ease-in-out;
            transform-origin: bottom;
        }

        .text-input-container.hidden {
            opacity: 0;
            transform: scaleY(0);
            padding: 0;
            height: 0;
            overflow: hidden;
            border-top: none;
        }

        .text-input-container.no-response {
            border-top: none;
        }

        #textInput {
            flex: 1;
            padding: 10px 14px;
            background: rgba(0, 0, 0, 0.2);
            border-radius: 20px;
            outline: none;
            border: none;
            color: white;
            font-size: 14px;
            font-family: 'Helvetica Neue', sans-serif;
            font-weight: 400;
        }

        #textInput::placeholder {
            color: rgba(255, 255, 255, 0.5);
        }

        #textInput:focus {
            outline: none;
        }

        .response-line h1,
        .response-line h2,
        .response-line h3,
        .response-line h4,
        .response-line h5,
        .response-line h6 {
            color: rgba(255, 255, 255, 0.95);
            margin: 16px 0 8px 0;
            font-weight: 600;
        }

        .response-line p {
            margin: 8px 0;
            color: rgba(255, 255, 255, 0.9);
        }

        .response-line ul,
        .response-line ol {
            margin: 8px 0;
            padding-left: 20px;
        }

        .response-line li {
            margin: 4px 0;
            color: rgba(255, 255, 255, 0.9);
        }

        .response-line code {
            background: rgba(255, 255, 255, 0.1);
            color: rgba(255, 255, 255, 0.95);
            padding: 2px 6px;
            border-radius: 4px;
            font-family: 'Monaco', 'Menlo', monospace;
            font-size: 13px;
        }

        .response-line pre {
            background: rgba(255, 255, 255, 0.05);
            color: rgba(255, 255, 255, 0.95);
            padding: 12px;
            border-radius: 6px;
            overflow-x: auto;
            margin: 12px 0;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .response-line pre code {
            background: none;
            padding: 0;
        }

        .response-line blockquote {
            border-left: 3px solid rgba(255, 255, 255, 0.3);
            margin: 12px 0;
            padding: 8px 16px;
            background: rgba(255, 255, 255, 0.05);
            color: rgba(255, 255, 255, 0.8);
        }

        .empty-state {
            display: flex;
            align-items: center;
            justify-content: center;
            height: 100%;
            color: rgba(255, 255, 255, 0.5);
            font-size: 14px;
        }

        .btn-gap {
            display: flex;
            align-items: center;
            justify-content: center;
            height: 100%;
            gap: 4px;
        }

        /* ────────────────[ GLASS BYPASS ]─────────────── */
        :host-context(body.has-glass) .ask-container,
        :host-context(body.has-glass) .response-header,
        :host-context(body.has-glass) .response-icon,
        :host-context(body.has-glass) .copy-button,
        :host-context(body.has-glass) .close-button,
        :host-context(body.has-glass) .line-copy-button,
        :host-context(body.has-glass) .text-input-container,
        :host-context(body.has-glass) .response-container pre,
        :host-context(body.has-glass) .response-container p code,
        :host-context(body.has-glass) .response-container pre code {
            background: transparent !important;
            border: none !important;
            outline: none !important;
            box-shadow: none !important;
            filter: none !important;
            backdrop-filter: none !important;
        }

        :host-context(body.has-glass) .ask-container::before {
            display: none !important;
        }

        :host-context(body.has-glass) .copy-button:hover,
        :host-context(body.has-glass) .close-button:hover,
        :host-context(body.has-glass) .line-copy-button,
        :host-context(body.has-glass) .line-copy-button:hover,
        :host-context(body.has-glass) .response-line:hover {
            background: transparent !important;
        }

        :host-context(body.has-glass) .response-container::-webkit-scrollbar-track,
        :host-context(body.has-glass) .response-container::-webkit-scrollbar-thumb {
            background: transparent !important;
        }

        .submit-btn, .clear-btn {
            display: flex;
            align-items: center;
            background: transparent;
            color: white;
            border: none;
            border-radius: 6px;
            margin-left: 8px;
            font-size: 13px;
            font-family: 'Helvetica Neue', sans-serif;
            font-weight: 500;
            overflow: hidden;
            cursor: pointer;
            transition: background 0.15s;
            height: 32px;
            padding: 0 10px;
            box-shadow: none;
        }
        .submit-btn:hover, .clear-btn:hover {
            background: rgba(255,255,255,0.1);
        }
        .btn-label {
            margin-right: 8px;
            display: flex;
            align-items: center;
            height: 100%;
        }
        .btn-icon {
            background: rgba(255,255,255,0.1);
            border-radius: 13%;
            display: flex;
            align-items: center;
            justify-content: center;
            width: 18px;
            height: 18px;
        }
        .btn-icon img, .btn-icon svg {
            width: 13px;
            height: 13px;
            display: block;
        }
        .header-clear-btn {
            background: transparent;
            border: none;
            display: flex;
            align-items: center;
            gap: 2px;
            cursor: pointer;
            padding: 0 2px;
        }
        .header-clear-btn .icon-box {
            color: white;
            font-size: 12px;
            font-family: 'Helvetica Neue', sans-serif;
            font-weight: 500;
            background-color: rgba(255, 255, 255, 0.1);
            border-radius: 13%;
            width: 18px;
            height: 18px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .header-clear-btn:hover .icon-box {
            background-color: rgba(255,255,255,0.18);
        }
    `;
  constructor() {
    super();
    this.currentResponse = "";
    this.currentQuestion = "";
    this.isLoading = false;
    this.copyState = "idle";
    this.showTextInput = true;
    this.headerText = "AI Response";
    this.headerAnimating = false;
    this.isStreaming = false;
    this.marked = null;
    this.hljs = null;
    this.DOMPurify = null;
    this.isLibrariesLoaded = false;
    this.smdParser = null;
    this.smdContainer = null;
    this.lastProcessedLength = 0;
    this.handleSendText = this.handleSendText.bind(this);
    this.handleTextKeydown = this.handleTextKeydown.bind(this);
    this.handleCopy = this.handleCopy.bind(this);
    this.clearResponseContent = this.clearResponseContent.bind(this);
    this.handleEscKey = this.handleEscKey.bind(this);
    this.handleScroll = this.handleScroll.bind(this);
    this.handleCloseAskWindow = this.handleCloseAskWindow.bind(this);
    this.handleCloseIfNoContent = this.handleCloseIfNoContent.bind(this);
    this.loadLibraries();
    this.isThrottled = false;
  }
  connectedCallback() {
    super.connectedCallback();
    console.log("\u{1F4F1} AskView connectedCallback - IPC \uC774\uBCA4\uD2B8 \uB9AC\uC2A4\uB108 \uC124\uC815");
    document.addEventListener("keydown", this.handleEscKey);
    this.resizeObserver = new ResizeObserver((entries) => {
      for (const entry of entries) {
        const needed = entry.contentRect.height;
        const current = window.innerHeight;
        if (needed > current - 4) {
          this.requestWindowResize(Math.ceil(needed));
        }
      }
    });
    const container = this.shadowRoot?.querySelector(".ask-container");
    if (container) this.resizeObserver.observe(container);
    this.handleQuestionFromAssistant = (event, question) => {
      console.log("AskView: Received question from ListenView:", question);
      this.handleSendText(null, question);
    };
    if (window.api) {
      window.api.askView.onShowTextInput(() => {
        console.log("Show text input signal received");
        if (!this.showTextInput) {
          this.showTextInput = true;
          this.updateComplete.then(() => this.focusTextInput());
        } else {
          this.focusTextInput();
        }
      });
      window.api.askView.onScrollResponseUp(() => this.handleScroll("up"));
      window.api.askView.onScrollResponseDown(() => this.handleScroll("down"));
      window.api.askView.onAskStateUpdate((event, newState) => {
        this.currentResponse = newState.currentResponse;
        this.currentQuestion = newState.currentQuestion;
        this.isLoading = newState.isLoading;
        this.isStreaming = newState.isStreaming;
        const wasHidden = !this.showTextInput;
        this.showTextInput = newState.showTextInput;
        if (newState.showTextInput) {
          if (wasHidden) {
            this.updateComplete.then(() => this.focusTextInput());
          } else {
            this.focusTextInput();
          }
        }
      });
      console.log("AskView: IPC \uC774\uBCA4\uD2B8 \uB9AC\uC2A4\uB108 \uB4F1\uB85D \uC644\uB8CC");
    }
  }
  disconnectedCallback() {
    super.disconnectedCallback();
    this.resizeObserver?.disconnect();
    console.log("\u{1F4F1} AskView disconnectedCallback - IPC \uC774\uBCA4\uD2B8 \uB9AC\uC2A4\uB108 \uC81C\uAC70");
    document.removeEventListener("keydown", this.handleEscKey);
    if (this.copyTimeout) {
      clearTimeout(this.copyTimeout);
    }
    if (this.headerAnimationTimeout) {
      clearTimeout(this.headerAnimationTimeout);
    }
    if (this.streamingTimeout) {
      clearTimeout(this.streamingTimeout);
    }
    Object.values(this.lineCopyTimeouts).forEach((timeout) => clearTimeout(timeout));
    if (window.api) {
      window.api.askView.removeOnAskStateUpdate(this.handleAskStateUpdate);
      window.api.askView.removeOnShowTextInput(this.handleShowTextInput);
      window.api.askView.removeOnScrollResponseUp(this.handleScroll);
      window.api.askView.removeOnScrollResponseDown(this.handleScroll);
      console.log("\u2705 AskView: IPC \uC774\uBCA4\uD2B8 \uB9AC\uC2A4\uB108 \uC81C\uAC70 \uD544\uC694");
    }
  }
  async loadLibraries() {
    try {
      if (!window.marked) {
        await this.loadScript("../../assets/marked-4.3.0.min.js");
      }
      if (!window.hljs) {
        await this.loadScript("../../assets/highlight-11.9.0.min.js");
      }
      if (!window.DOMPurify) {
        await this.loadScript("../../assets/dompurify-3.0.7.min.js");
      }
      this.marked = window.marked;
      this.hljs = window.hljs;
      this.DOMPurify = window.DOMPurify;
      if (this.marked && this.hljs) {
        this.marked.setOptions({
          highlight: (code, lang) => {
            if (lang && this.hljs.getLanguage(lang)) {
              try {
                return this.hljs.highlight(code, { language: lang }).value;
              } catch (err) {
                console.warn("Highlight error:", err);
              }
            }
            try {
              return this.hljs.highlightAuto(code).value;
            } catch (err) {
              console.warn("Auto highlight error:", err);
            }
            return code;
          },
          breaks: true,
          gfm: true,
          pedantic: false,
          smartypants: false,
          xhtml: false
        });
        this.isLibrariesLoaded = true;
        this.renderContent();
        console.log("Markdown libraries loaded successfully in AskView");
      }
      if (this.DOMPurify) {
        this.isDOMPurifyLoaded = true;
        console.log("DOMPurify loaded successfully in AskView");
      }
    } catch (error) {
      console.error("Failed to load libraries in AskView:", error);
    }
  }
  handleCloseAskWindow() {
    window.api.askView.closeAskWindow();
  }
  handleCloseIfNoContent() {
    if (!this.currentResponse && !this.isLoading && !this.isStreaming) {
      this.handleCloseAskWindow();
    }
  }
  handleEscKey(e2) {
    if (e2.key === "Escape") {
      e2.preventDefault();
      this.handleCloseIfNoContent();
    }
  }
  clearResponseContent() {
    this.currentResponse = "";
    this.currentQuestion = "";
    this.isLoading = false;
    this.isStreaming = false;
    this.headerText = "AI Response";
    this.showTextInput = true;
    this.lastProcessedLength = 0;
    this.smdParser = null;
    this.smdContainer = null;
  }
  handleInputFocus() {
    this.isInputFocused = true;
  }
  focusTextInput() {
    requestAnimationFrame(() => {
      const textInput = this.shadowRoot?.getElementById("textInput");
      if (textInput) {
        textInput.focus();
      }
    });
  }
  loadScript(src) {
    return new Promise((resolve, reject) => {
      const script = document.createElement("script");
      script.src = src;
      script.onload = resolve;
      script.onerror = reject;
      document.head.appendChild(script);
    });
  }
  parseMarkdown(text) {
    if (!text) return "";
    if (!this.isLibrariesLoaded || !this.marked) {
      return text;
    }
    try {
      return this.marked(text);
    } catch (error) {
      console.error("Markdown parsing error in AskView:", error);
      return text;
    }
  }
  fixIncompleteCodeBlocks(text) {
    if (!text) return text;
    const codeBlockMarkers = text.match(/```/g) || [];
    const markerCount = codeBlockMarkers.length;
    if (markerCount % 2 === 1) {
      return text + "\n```";
    }
    return text;
  }
  handleScroll(direction) {
    const scrollableElement = this.shadowRoot.querySelector("#responseContainer");
    if (scrollableElement) {
      const scrollAmount = 100;
      if (direction === "up") {
        scrollableElement.scrollTop -= scrollAmount;
      } else {
        scrollableElement.scrollTop += scrollAmount;
      }
    }
  }
  renderContent() {
    const responseContainer = this.shadowRoot.getElementById("responseContainer");
    if (!responseContainer) return;
    if (this.isLoading) {
      responseContainer.innerHTML = `
              <div class="loading-dots">
                <div class="loading-dot"></div>
                <div class="loading-dot"></div>
                <div class="loading-dot"></div>
              </div>`;
      this.resetStreamingParser();
      return;
    }
    if (!this.currentResponse) {
      responseContainer.innerHTML = `<div class="empty-state">...</div>`;
      this.resetStreamingParser();
      return;
    }
    this.renderStreamingMarkdown(responseContainer);
    this.adjustWindowHeightThrottled();
  }
  resetStreamingParser() {
    this.smdParser = null;
    this.smdContainer = null;
    this.lastProcessedLength = 0;
  }
  renderStreamingMarkdown(responseContainer) {
    try {
      if (!this.smdParser || this.smdContainer !== responseContainer) {
        this.smdContainer = responseContainer;
        this.smdContainer.innerHTML = "";
        const renderer = default_renderer(this.smdContainer);
        this.smdParser = parser(renderer);
        this.lastProcessedLength = 0;
      }
      const currentText = this.currentResponse;
      const newText = currentText.slice(this.lastProcessedLength);
      if (newText.length > 0) {
        parser_write(this.smdParser, newText);
        this.lastProcessedLength = currentText.length;
      }
      if (!this.isStreaming && !this.isLoading) {
        parser_end(this.smdParser);
      }
      if (this.hljs) {
        responseContainer.querySelectorAll("pre code").forEach((block) => {
          if (!block.hasAttribute("data-highlighted")) {
            this.hljs.highlightElement(block);
            block.setAttribute("data-highlighted", "true");
          }
        });
      }
      responseContainer.scrollTop = responseContainer.scrollHeight;
    } catch (error) {
      console.error("Error rendering streaming markdown:", error);
      this.renderFallbackContent(responseContainer);
    }
  }
  renderFallbackContent(responseContainer) {
    const textToRender = this.currentResponse || "";
    if (this.isLibrariesLoaded && this.marked && this.DOMPurify) {
      try {
        const parsedHtml = this.marked.parse(textToRender);
        const cleanHtml = this.DOMPurify.sanitize(parsedHtml, {
          ALLOWED_TAGS: [
            "h1",
            "h2",
            "h3",
            "h4",
            "h5",
            "h6",
            "p",
            "br",
            "strong",
            "b",
            "em",
            "i",
            "ul",
            "ol",
            "li",
            "blockquote",
            "code",
            "pre",
            "a",
            "img",
            "table",
            "thead",
            "tbody",
            "tr",
            "th",
            "td",
            "hr",
            "sup",
            "sub",
            "del",
            "ins"
          ],
          ALLOWED_ATTR: ["href", "src", "alt", "title", "class", "id", "target", "rel"]
        });
        responseContainer.innerHTML = cleanHtml;
        if (this.hljs) {
          responseContainer.querySelectorAll("pre code").forEach((block) => {
            this.hljs.highlightElement(block);
          });
        }
      } catch (error) {
        console.error("Error in fallback rendering:", error);
        responseContainer.textContent = textToRender;
      }
    } else {
      const basicHtml = textToRender.replace(/&/g, "&amp;").replace(/</g, "&lt;").replace(/>/g, "&gt;").replace(/\n\n/g, "</p><p>").replace(/\n/g, "<br>").replace(/\*\*(.*?)\*\*/g, "<strong>$1</strong>").replace(/\*(.*?)\*/g, "<em>$1</em>").replace(/`([^`]+)`/g, "<code>$1</code>");
      responseContainer.innerHTML = `<p>${basicHtml}</p>`;
    }
  }
  requestWindowResize(targetHeight) {
    if (window.api) {
      window.api.askView.adjustWindowHeight(targetHeight);
    }
  }
  animateHeaderText(text) {
    this.headerAnimating = true;
    this.requestUpdate();
    setTimeout(() => {
      this.headerText = text;
      this.headerAnimating = false;
      this.requestUpdate();
    }, 150);
  }
  startHeaderAnimation() {
    this.animateHeaderText("analyzing screen...");
    if (this.headerAnimationTimeout) {
      clearTimeout(this.headerAnimationTimeout);
    }
    this.headerAnimationTimeout = setTimeout(() => {
      this.animateHeaderText("thinking...");
    }, 1500);
  }
  renderMarkdown(content) {
    if (!content) return "";
    if (this.isLibrariesLoaded && this.marked) {
      return this.parseMarkdown(content);
    }
    return content.replace(/\*\*(.*?)\*\*/g, "<strong>$1</strong>").replace(/\*(.*?)\*/g, "<em>$1</em>").replace(/`(.*?)`/g, "<code>$1</code>");
  }
  fixIncompleteMarkdown(text) {
    if (!text) return text;
    const boldCount = (text.match(/\*\*/g) || []).length;
    if (boldCount % 2 === 1) {
      text += "**";
    }
    const italicCount = (text.match(/(?<!\*)\*(?!\*)/g) || []).length;
    if (italicCount % 2 === 1) {
      text += "*";
    }
    const inlineCodeCount = (text.match(/`/g) || []).length;
    if (inlineCodeCount % 2 === 1) {
      text += "`";
    }
    const openBrackets = (text.match(/\[/g) || []).length;
    const closeBrackets = (text.match(/\]/g) || []).length;
    if (openBrackets > closeBrackets) {
      text += "]";
    }
    const openParens = (text.match(/\]\(/g) || []).length;
    const closeParens = (text.match(/\)\s*$/g) || []).length;
    if (openParens > closeParens && text.endsWith("(")) {
      text += ")";
    }
    return text;
  }
  async handleCopy() {
    if (this.copyState === "copied") return;
    let responseToCopy = this.currentResponse;
    if (this.isDOMPurifyLoaded && this.DOMPurify) {
      const testHtml = this.renderMarkdown(responseToCopy);
      const sanitized = this.DOMPurify.sanitize(testHtml);
      if (this.DOMPurify.removed && this.DOMPurify.removed.length > 0) {
        console.warn("Unsafe content detected, copy blocked");
        return;
      }
    }
    const textToCopy = `Question: ${this.currentQuestion}

Answer: ${responseToCopy}`;
    try {
      await navigator.clipboard.writeText(textToCopy);
      console.log("Content copied to clipboard");
      this.copyState = "copied";
      this.requestUpdate();
      if (this.copyTimeout) {
        clearTimeout(this.copyTimeout);
      }
      this.copyTimeout = setTimeout(() => {
        this.copyState = "idle";
        this.requestUpdate();
      }, 1500);
    } catch (err) {
      console.error("Failed to copy:", err);
    }
  }
  async handleLineCopy(lineIndex) {
    const originalLines = this.currentResponse.split("\n");
    const lineToCopy = originalLines[lineIndex];
    if (!lineToCopy) return;
    try {
      await navigator.clipboard.writeText(lineToCopy);
      console.log("Line copied to clipboard");
      this.lineCopyState = { ...this.lineCopyState, [lineIndex]: true };
      this.requestUpdate();
      if (this.lineCopyTimeouts && this.lineCopyTimeouts[lineIndex]) {
        clearTimeout(this.lineCopyTimeouts[lineIndex]);
      }
      this.lineCopyTimeouts[lineIndex] = setTimeout(() => {
        const updatedState = { ...this.lineCopyState };
        delete updatedState[lineIndex];
        this.lineCopyState = updatedState;
        this.requestUpdate();
      }, 1500);
    } catch (err) {
      console.error("Failed to copy line:", err);
    }
  }
  async handleSendText(e2, overridingText = "") {
    const textInput = this.shadowRoot?.getElementById("textInput");
    const text = (overridingText || textInput?.value || "").trim();
    textInput.value = "";
    if (window.api) {
      window.api.askView.sendMessage(text).catch((error) => {
        console.error("Error sending text:", error);
      });
    }
  }
  handleTextKeydown(e2) {
    if (e2.isComposing) {
      return;
    }
    const isPlainEnter = e2.key === "Enter" && !e2.shiftKey && !e2.metaKey && !e2.ctrlKey;
    const isModifierEnter = e2.key === "Enter" && (e2.metaKey || e2.ctrlKey);
    if (isPlainEnter || isModifierEnter) {
      e2.preventDefault();
      this.handleSendText();
    }
  }
  updated(changedProperties) {
    super.updated(changedProperties);
    if (changedProperties.has("isLoading") || changedProperties.has("currentResponse")) {
      this.renderContent();
    }
    if (changedProperties.has("showTextInput") || changedProperties.has("isLoading") || changedProperties.has("currentResponse")) {
      this.adjustWindowHeightThrottled();
    }
    if (changedProperties.has("showTextInput") && this.showTextInput) {
      this.focusTextInput();
    }
  }
  firstUpdated() {
    setTimeout(() => this.adjustWindowHeight(), 200);
  }
  getTruncatedQuestion(question, maxLength = 30) {
    if (!question) return "";
    if (question.length <= maxLength) return question;
    return question.substring(0, maxLength) + "...";
  }
  render() {
    const hasResponse = this.isLoading || this.currentResponse || this.isStreaming;
    const headerText = this.isLoading ? "Thinking..." : "AI Response";
    return H`
            <div class="ask-container">
                <!-- Response Header -->
                <div class="response-header ${!hasResponse ? "hidden" : ""}">
                    <div class="header-left">
                        <div class="response-icon">
                            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2z" />
                                <path d="M8 12l2 2 4-4" />
                            </svg>
                        </div>
                        <span class="response-label">${headerText}</span>
                    </div>
                    <div class="header-right">
                        <span class="question-text">${this.getTruncatedQuestion(this.currentQuestion)}</span>
                        <div class="header-controls">
                            <button class="copy-button ${this.copyState === "copied" ? "copied" : ""}" @click=${this.handleCopy}>
                                <svg class="copy-icon" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <rect x="9" y="9" width="13" height="13" rx="2" ry="2" />
                                    <path d="M5 15H4a2 2 0 01-2-2V4a2 2 0 012-2h9a2 2 0 012 2v1" />
                                </svg>
                                <svg
                                    class="check-icon"
                                    width="16"
                                    height="16"
                                    viewBox="0 0 24 24"
                                    fill="none"
                                    stroke="currentColor"
                                    stroke-width="2.5"
                                >
                                    <path d="M20 6L9 17l-5-5" />
                                </svg>
                            </button>
                            <button class="close-button" @click=${this.handleCloseAskWindow}>
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <line x1="18" y1="6" x2="6" y2="18" />
                                    <line x1="6" y1="6" x2="18" y2="18" />
                                </svg>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Response Container -->
                <div class="response-container ${!hasResponse ? "hidden" : ""}" id="responseContainer">
                    <!-- Content is dynamically generated in updateResponseContent() -->
                </div>

                <!-- Text Input Container -->
                <div class="text-input-container ${!hasResponse ? "no-response" : ""} ${!this.showTextInput ? "hidden" : ""}">
                    <input
                        type="text"
                        id="textInput"
                        placeholder="Ask about your screen or audio"
                        @keydown=${this.handleTextKeydown}
                        @focus=${this.handleInputFocus}
                    />
                    <button
                        class="submit-btn"
                        @click=${this.handleSendText}
                    >
                        <span class="btn-label">Submit</span>
                        <span class="btn-icon">
                            ↵
                        </span>
                    </button>
                </div>
            </div>
        `;
  }
  // Dynamically resize the BrowserWindow to fit current content
  adjustWindowHeight() {
    if (!window.api) return;
    this.updateComplete.then(() => {
      const headerEl = this.shadowRoot.querySelector(".response-header");
      const responseEl = this.shadowRoot.querySelector(".response-container");
      const inputEl = this.shadowRoot.querySelector(".text-input-container");
      if (!headerEl || !responseEl) return;
      const headerHeight = headerEl.classList.contains("hidden") ? 0 : headerEl.offsetHeight;
      const responseHeight = responseEl.scrollHeight;
      const inputHeight = inputEl && !inputEl.classList.contains("hidden") ? inputEl.offsetHeight : 0;
      const idealHeight = headerHeight + responseHeight + inputHeight;
      const targetHeight = Math.min(700, idealHeight);
      window.api.askView.adjustWindowHeight("ask", targetHeight);
    }).catch((err) => console.error("AskView adjustWindowHeight error:", err));
  }
  // Throttled wrapper to avoid excessive IPC spam (executes at most once per animation frame)
  adjustWindowHeightThrottled() {
    if (this.isThrottled) return;
    this.isThrottled = true;
    requestAnimationFrame(() => {
      this.adjustWindowHeight();
      this.isThrottled = false;
    });
  }
};
customElements.define("ask-view", AskView);

// src/ui/settings/ShortCutSettingsView.js
var commonSystemShortcuts = /* @__PURE__ */ new Set([
  "Cmd+Q",
  "Cmd+W",
  "Cmd+A",
  "Cmd+S",
  "Cmd+Z",
  "Cmd+X",
  "Cmd+C",
  "Cmd+V",
  "Cmd+P",
  "Cmd+F",
  "Cmd+G",
  "Cmd+H",
  "Cmd+M",
  "Cmd+N",
  "Cmd+O",
  "Cmd+T",
  "Ctrl+Q",
  "Ctrl+W",
  "Ctrl+A",
  "Ctrl+S",
  "Ctrl+Z",
  "Ctrl+X",
  "Ctrl+C",
  "Ctrl+V",
  "Ctrl+P",
  "Ctrl+F",
  "Ctrl+G",
  "Ctrl+H",
  "Ctrl+M",
  "Ctrl+N",
  "Ctrl+O",
  "Ctrl+T"
]);
var displayNameMap = {
  nextStep: "Ask Anything",
  moveUp: "Move Up Window",
  moveDown: "Move Down Window",
  scrollUp: "Scroll Up Response",
  scrollDown: "Scroll Down Response"
};
var ShortcutSettingsView = class extends ut {
  static styles = r`
        * { font-family:'Helvetica Neue',-apple-system,BlinkMacSystemFont,'Segoe UI',Roboto,sans-serif;
            cursor:default; user-select:none; box-sizing:border-box; }

        :host { display:flex; width:100%; height:100%; color:white; }

        .container { display:flex; flex-direction:column; height:100%;
            background:rgba(20,20,20,.9); border-radius:12px;
            outline:.5px rgba(255,255,255,.2) solid; outline-offset:-1px;
            position:relative; overflow:hidden; padding:12px; }

        .close-button{position:absolute;top:10px;right:10px;inline-size:14px;block-size:14px;
            background:rgba(255,255,255,.1);border:none;border-radius:3px;
            color:rgba(255,255,255,.7);display:grid;place-items:center;
            font-size:14px;line-height:0;cursor:pointer;transition:.15s;z-index:10;}
        .close-button:hover{background:rgba(255,255,255,.2);color:rgba(255,255,255,.9);}

        .title{font-size:14px;font-weight:500;margin:0 0 8px;padding-bottom:8px;
            border-bottom:1px solid rgba(255,255,255,.1);text-align:center;}

        .scroll-area{flex:1 1 auto;overflow-y:auto;margin:0 -4px;padding:4px;}

        .shortcut-entry{display:flex;align-items:center;width:100%;gap:8px;
            margin-bottom:8px;font-size:12px;padding:4px;}
        .shortcut-name{flex:1 1 auto;color:rgba(255,255,255,.9);font-weight:300;
            white-space:nowrap;overflow:hidden;text-overflow:ellipsis;}

        .action-btn{background:none;border:none;color:rgba(0,122,255,.8);
            font-size:11px;padding:0 4px;cursor:pointer;transition:.15s;}
        .action-btn:hover{color:#0a84ff;text-decoration:underline;}

        .shortcut-input{inline-size:120px;background:rgba(0,0,0,.2);
            border:1px solid rgba(255,255,255,.2);border-radius:4px;
            padding:4px 6px;font:11px 'SF Mono','Menlo',monospace;
            color:white;text-align:right;cursor:text;margin-left:auto;}
        .shortcut-input:focus,.shortcut-input.capturing{
            outline:none;border-color:rgba(0,122,255,.6);
            box-shadow:0 0 0 1px rgba(0,122,255,.3);}

        .feedback{font-size:10px;margin-top:2px;min-height:12px;}
        .feedback.error{color:#ef4444;}
        .feedback.success{color:#22c55e;}

        .actions{display:flex;gap:4px;padding-top:8px;border-top:1px solid rgba(255,255,255,.1);}
        .settings-button{flex:1;background:rgba(255,255,255,.1);
            border:1px solid rgba(255,255,255,.2);border-radius:4px;
            color:white;padding:5px 10px;font-size:11px;cursor:pointer;transition:.15s;}
        .settings-button:hover{background:rgba(255,255,255,.15);}
        .settings-button.primary{background:rgba(0,122,255,.25);border-color:rgba(0,122,255,.6);}
        .settings-button.primary:hover{background:rgba(0,122,255,.35);}
        .settings-button.danger{background:rgba(255,59,48,.1);border-color:rgba(255,59,48,.3);
            color:rgba(255,59,48,.9);}
        .settings-button.danger:hover{background:rgba(255,59,48,.15);
        }

        /* ────────────────[ GLASS BYPASS ]─────────────── */
        :host-context(body.has-glass) {
          animation: none !important;
          transition: none !important;
          transform: none !important;
          will-change: auto !important;
        }
        :host-context(body.has-glass) * {
          background: transparent !important;   /* 요청한 투명 처리 */
          filter: none !important;
          backdrop-filter: none !important;
          box-shadow: none !important;
          outline: none !important;
          border: none !important;
          border-radius: 0 !important;
          transition: none !important;
          animation: none !important;
        }
    `;
  static properties = {
    shortcuts: { type: Object, state: true },
    isLoading: { type: Boolean, state: true },
    capturingKey: { type: String, state: true },
    feedback: { type: Object, state: true }
  };
  constructor() {
    super();
    this.shortcuts = {};
    this.feedback = {};
    this.isLoading = true;
    this.capturingKey = null;
  }
  connectedCallback() {
    super.connectedCallback();
    if (!window.api) return;
    this.loadShortcutsHandler = (event, keybinds) => {
      this.shortcuts = keybinds;
      this.isLoading = false;
    };
    window.api.shortcutSettingsView.onLoadShortcuts(this.loadShortcutsHandler);
  }
  disconnectedCallback() {
    super.disconnectedCallback();
    if (window.api && this.loadShortcutsHandler) {
      window.api.shortcutSettingsView.removeOnLoadShortcuts(this.loadShortcutsHandler);
    }
  }
  handleKeydown(e2, shortcutKey) {
    e2.preventDefault();
    e2.stopPropagation();
    const result = this._parseAccelerator(e2);
    if (!result) return;
    const { accel, error } = result;
    if (error) {
      this.feedback = { ...this.feedback, [shortcutKey]: { type: "error", msg: error } };
      return;
    }
    this.shortcuts = { ...this.shortcuts, [shortcutKey]: accel };
    this.feedback = { ...this.feedback, [shortcutKey]: { type: "success", msg: "Shortcut set" } };
    this.stopCapture();
  }
  _parseAccelerator(e2) {
    const parts = [];
    if (e2.metaKey) parts.push("Cmd");
    if (e2.ctrlKey) parts.push("Ctrl");
    if (e2.altKey) parts.push("Alt");
    if (e2.shiftKey) parts.push("Shift");
    const isModifier = ["Meta", "Control", "Alt", "Shift"].includes(e2.key);
    if (isModifier) return null;
    const map = { ArrowUp: "Up", ArrowDown: "Down", ArrowLeft: "Left", ArrowRight: "Right", " ": "Space" };
    parts.push(e2.key.length === 1 ? e2.key.toUpperCase() : map[e2.key] || e2.key);
    const accel = parts.join("+");
    if (parts.length === 1) return { error: "Invalid shortcut: needs a modifier" };
    if (parts.length > 4) return { error: "Invalid shortcut: max 4 keys" };
    if (commonSystemShortcuts.has(accel)) return { error: "Invalid shortcut: system reserved" };
    return { accel };
  }
  startCapture(key) {
    this.capturingKey = key;
    this.feedback = { ...this.feedback, [key]: void 0 };
  }
  disableShortcut(key) {
    this.shortcuts = { ...this.shortcuts, [key]: "" };
    this.feedback = { ...this.feedback, [key]: { type: "success", msg: "Shortcut disabled" } };
  }
  stopCapture() {
    this.capturingKey = null;
  }
  async handleSave() {
    if (!window.api) return;
    this.feedback = {};
    const result = await window.api.shortcutSettingsView.saveShortcuts(this.shortcuts);
    if (!result.success) {
      alert("Failed to save shortcuts: " + result.error);
    }
  }
  handleClose() {
    if (!window.api) return;
    this.feedback = {};
    window.api.shortcutSettingsView.closeShortcutSettingsWindow();
  }
  async handleResetToDefault() {
    if (!window.api) return;
    const confirmation = confirm("Are you sure you want to reset all shortcuts to their default values?");
    if (!confirmation) return;
    try {
      const defaultShortcuts = await window.api.shortcutSettingsView.getDefaultShortcuts();
      this.shortcuts = defaultShortcuts;
    } catch (error) {
      alert("Failed to load default settings.");
    }
  }
  formatShortcutName(name) {
    if (displayNameMap[name]) {
      return displayNameMap[name];
    }
    const result = name.replace(/([A-Z])/g, " $1");
    return result.charAt(0).toUpperCase() + result.slice(1);
  }
  render() {
    if (this.isLoading) {
      return H`<div class="container"><div class="loading-state">Loading Shortcuts...</div></div>`;
    }
    return H`
          <div class="container">
            <button class="close-button" @click=${this.handleClose} title="Close">&times;</button>
            <h1 class="title">Edit Shortcuts</h1>
    
            <div class="scroll-area">
              ${Object.keys(this.shortcuts).map((key) => H`
                <div>
                  <div class="shortcut-entry">
                    <span class="shortcut-name">${this.formatShortcutName(key)}</span>
    
                    <!-- Edit & Disable 버튼 -->
                    <button class="action-btn" @click=${() => this.startCapture(key)}>Edit</button>
                    <button class="action-btn" @click=${() => this.disableShortcut(key)}>Disable</button>
    
                    <input readonly
                      class="shortcut-input ${this.capturingKey === key ? "capturing" : ""}"
                      .value=${this.shortcuts[key] || ""}
                      placeholder=${this.capturingKey === key ? "Press new shortcut\u2026" : "Click to edit"}
                      @click=${() => this.startCapture(key)}
                      @keydown=${(e2) => this.handleKeydown(e2, key)}
                      @blur=${() => this.stopCapture()}
                    />
                  </div>
    
                  ${this.feedback[key] ? H`
                    <div class="feedback ${this.feedback[key].type}">
                      ${this.feedback[key].msg}
                    </div>` : H`<div class="feedback"></div>`}
                </div>
              `)}
            </div>
    
            <div class="actions">
              <button class="settings-button" @click=${this.handleClose}>Cancel</button>
              <button class="settings-button danger" @click=${this.handleResetToDefault}>Reset to Default</button>
              <button class="settings-button primary" @click=${this.handleSave}>Save</button>
            </div>
          </div>
        `;
  }
};
customElements.define("shortcut-settings-view", ShortcutSettingsView);

// src/ui/listen/audioCore/renderer.js
var listenCapture = require_listenCapture();
var params = new URLSearchParams(window.location.search);
var isListenView = params.get("view") === "listen";
window.pickleGlass = {
  startCapture: listenCapture.startCapture,
  stopCapture: listenCapture.stopCapture,
  isLinux: listenCapture.isLinux,
  isMacOS: listenCapture.isMacOS,
  captureManualScreenshot: listenCapture.captureManualScreenshot,
  getCurrentScreenshot: listenCapture.getCurrentScreenshot
};
window.api.renderer.onChangeListenCaptureState((_event, { status }) => {
  if (!isListenView) {
    console.log("[Renderer] Non-listen view: ignoring capture-state change");
    return;
  }
  if (status === "stop") {
    console.log("[Renderer] Session ended \u2013 stopping local capture");
    listenCapture.stopCapture();
  } else {
    console.log("[Renderer] Session initialized \u2013 starting local capture");
    listenCapture.startCapture();
  }
});

// src/ui/app/PickleGlassApp.js
var PickleGlassApp = class extends ut {
  static styles = r`
        :host {
            display: block;
            width: 100%;
            height: 100%;
            color: var(--text-color);
            background: transparent;
            border-radius: 7px;
        }

        listen-view {
            display: block;
            width: 100%;
            height: 100%;
        }

        ask-view, settings-view, history-view, help-view, setup-view {
            display: block;
            width: 100%;
            height: 100%;
        }

    `;
  static properties = {
    currentView: { type: String },
    statusText: { type: String },
    startTime: { type: Number },
    currentResponseIndex: { type: Number },
    isMainViewVisible: { type: Boolean },
    selectedProfile: { type: String },
    selectedLanguage: { type: String },
    selectedScreenshotInterval: { type: String },
    selectedImageQuality: { type: String },
    isClickThrough: { type: Boolean, state: true },
    layoutMode: { type: String },
    _viewInstances: { type: Object, state: true },
    _isClickThrough: { state: true },
    structuredData: { type: Object }
  };
  constructor() {
    super();
    const urlParams = new URLSearchParams(window.location.search);
    this.currentView = urlParams.get("view") || "listen";
    this.currentResponseIndex = -1;
    this.selectedProfile = localStorage.getItem("selectedProfile") || "interview";
    let lang = localStorage.getItem("selectedLanguage") || "en";
    if (lang.includes("-")) {
      const newLang = lang.split("-")[0];
      console.warn(`[Migration] Correcting language format from "${lang}" to "${newLang}".`);
      localStorage.setItem("selectedLanguage", newLang);
      lang = newLang;
    }
    this.selectedLanguage = lang;
    this.selectedScreenshotInterval = localStorage.getItem("selectedScreenshotInterval") || "5";
    this.selectedImageQuality = localStorage.getItem("selectedImageQuality") || "medium";
    this._isClickThrough = false;
  }
  connectedCallback() {
    super.connectedCallback();
    if (window.api) {
      window.api.pickleGlassApp.onClickThroughToggled((_2, isEnabled) => {
        this._isClickThrough = isEnabled;
      });
    }
  }
  disconnectedCallback() {
    super.disconnectedCallback();
    if (window.api) {
      window.api.pickleGlassApp.removeAllClickThroughListeners();
    }
  }
  updated(changedProperties) {
    if (changedProperties.has("currentView")) {
      const viewContainer = this.shadowRoot?.querySelector(".view-container");
      if (viewContainer) {
        viewContainer.classList.add("entering");
        requestAnimationFrame(() => {
          viewContainer.classList.remove("entering");
        });
      }
    }
    if (changedProperties.has("selectedProfile")) {
      localStorage.setItem("selectedProfile", this.selectedProfile);
    }
    if (changedProperties.has("selectedLanguage")) {
      localStorage.setItem("selectedLanguage", this.selectedLanguage);
    }
    if (changedProperties.has("selectedScreenshotInterval")) {
      localStorage.setItem("selectedScreenshotInterval", this.selectedScreenshotInterval);
    }
    if (changedProperties.has("selectedImageQuality")) {
      localStorage.setItem("selectedImageQuality", this.selectedImageQuality);
    }
    if (changedProperties.has("layoutMode")) {
      this.updateLayoutMode();
    }
  }
  async handleClose() {
    if (window.api) {
      await window.api.common.quitApplication();
    }
  }
  render() {
    switch (this.currentView) {
      case "listen":
        return H`<listen-view
                    .currentResponseIndex=${this.currentResponseIndex}
                    .selectedProfile=${this.selectedProfile}
                    .structuredData=${this.structuredData}
                    @response-index-changed=${(e2) => this.currentResponseIndex = e2.detail.index}
                ></listen-view>`;
      case "ask":
        return H`<ask-view></ask-view>`;
      case "settings":
        return H`<settings-view
                    .selectedProfile=${this.selectedProfile}
                    .selectedLanguage=${this.selectedLanguage}
                    .onProfileChange=${(profile) => this.selectedProfile = profile}
                    .onLanguageChange=${(lang) => this.selectedLanguage = lang}
                ></settings-view>`;
      case "shortcut-settings":
        return H`<shortcut-settings-view></shortcut-settings-view>`;
      case "history":
        return H`<history-view></history-view>`;
      case "help":
        return H`<help-view></help-view>`;
      case "setup":
        return H`<setup-view></setup-view>`;
      default:
        return H`<div>Unknown view: ${this.currentView}</div>`;
    }
  }
};
customElements.define("pickle-glass-app", PickleGlassApp);
export {
  PickleGlassApp
};
/**
 * @license
 * Copyright 2019 Google LLC
 * SPDX-License-Identifier: BSD-3-Clause
 */
/**
 * @license
 * Copyright 2017 Google LLC
 * SPDX-License-Identifier: BSD-3-Clause
 */
/**
 * @license
 * Copyright 2022 Google LLC
 * SPDX-License-Identifier: BSD-3-Clause
 */
//# sourceMappingURL=content.js.map
