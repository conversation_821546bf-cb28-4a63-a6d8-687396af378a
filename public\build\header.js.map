{"version": 3, "sources": ["../../src/ui/assets/lit-core-2.7.4.min.js", "../../src/ui/app/MainHeader.js", "../../src/ui/app/ApiKeyHeader.js", "../../src/ui/app/PermissionHeader.js", "../../src/ui/app/WelcomeHeader.js", "../../src/ui/app/HeaderController.js"], "sourcesContent": ["/**\r\n * @license\r\n * Copyright 2019 Google LLC\r\n * SPDX-License-Identifier: BSD-3-Clause\r\n */\r\nconst t=window,i=t.ShadowRoot&&(void 0===t.ShadyCSS||t.ShadyCSS.nativeShadow)&&\"adoptedStyleSheets\"in Document.prototype&&\"replace\"in CSSStyleSheet.prototype,s=Symbol(),e=new WeakMap;class o{constructor(t,e,i){if(this._$cssResult$=!0,i!==s)throw Error(\"CSSResult is not constructable. Use `unsafeCSS` or `css` instead.\");this.cssText=t,this.t=e}get styleSheet(){let t=this.i;const s=this.t;if(i&&void 0===t){const i=void 0!==s&&1===s.length;i&&(t=e.get(s)),void 0===t&&((this.i=t=new CSSStyleSheet).replaceSync(this.cssText),i&&e.set(s,t))}return t}toString(){return this.cssText}}const n=t=>new o(\"string\"==typeof t?t:t+\"\",void 0,s),r=(t,...e)=>{const i=1===t.length?t[0]:e.reduce(((e,s,i)=>e+(t=>{if(!0===t._$cssResult$)return t.cssText;if(\"number\"==typeof t)return t;throw Error(\"Value passed to 'css' function must be a 'css' function result: \"+t+\". Use 'unsafeCSS' to pass non-literal values, but take care to ensure page security.\")})(s)+t[i+1]),t[0]);return new o(i,t,s)},h=(e,s)=>{i?e.adoptedStyleSheets=s.map((t=>t instanceof CSSStyleSheet?t:t.styleSheet)):s.forEach((s=>{const i=document.createElement(\"style\"),n=t.litNonce;void 0!==n&&i.setAttribute(\"nonce\",n),i.textContent=s.cssText,e.appendChild(i)}))},l=i?t=>t:t=>t instanceof CSSStyleSheet?(t=>{let e=\"\";for(const s of t.cssRules)e+=s.cssText;return n(e)})(t):t\r\n/**\r\n * @license\r\n * Copyright 2017 Google LLC\r\n * SPDX-License-Identifier: BSD-3-Clause\r\n */;var a;const u=window,c=u.trustedTypes,d=c?c.emptyScript:\"\",v=u.reactiveElementPolyfillSupport,p={toAttribute(t,e){switch(e){case Boolean:t=t?d:null;break;case Object:case Array:t=null==t?t:JSON.stringify(t)}return t},fromAttribute(t,e){let s=t;switch(e){case Boolean:s=null!==t;break;case Number:s=null===t?null:Number(t);break;case Object:case Array:try{s=JSON.parse(t)}catch(t){s=null}}return s}},f=(t,e)=>e!==t&&(e==e||t==t),m={attribute:!0,type:String,converter:p,reflect:!1,hasChanged:f},y=\"finalized\";class _ extends HTMLElement{constructor(){super(),this.o=new Map,this.isUpdatePending=!1,this.hasUpdated=!1,this.l=null,this.u()}static addInitializer(t){var e;this.finalize(),(null!==(e=this.v)&&void 0!==e?e:this.v=[]).push(t)}static get observedAttributes(){this.finalize();const t=[];return this.elementProperties.forEach(((e,s)=>{const i=this.p(s,e);void 0!==i&&(this.m.set(i,s),t.push(i))})),t}static createProperty(t,e=m){if(e.state&&(e.attribute=!1),this.finalize(),this.elementProperties.set(t,e),!e.noAccessor&&!this.prototype.hasOwnProperty(t)){const s=\"symbol\"==typeof t?Symbol():\"__\"+t,i=this.getPropertyDescriptor(t,s,e);void 0!==i&&Object.defineProperty(this.prototype,t,i)}}static getPropertyDescriptor(t,e,s){return{get(){return this[e]},set(i){const n=this[t];this[e]=i,this.requestUpdate(t,n,s)},configurable:!0,enumerable:!0}}static getPropertyOptions(t){return this.elementProperties.get(t)||m}static finalize(){if(this.hasOwnProperty(y))return!1;this[y]=!0;const t=Object.getPrototypeOf(this);if(t.finalize(),void 0!==t.v&&(this.v=[...t.v]),this.elementProperties=new Map(t.elementProperties),this.m=new Map,this.hasOwnProperty(\"properties\")){const t=this.properties,e=[...Object.getOwnPropertyNames(t),...Object.getOwnPropertySymbols(t)];for(const s of e)this.createProperty(s,t[s])}return this.elementStyles=this.finalizeStyles(this.styles),!0}static finalizeStyles(t){const e=[];if(Array.isArray(t)){const s=new Set(t.flat(1/0).reverse());for(const t of s)e.unshift(l(t))}else void 0!==t&&e.push(l(t));return e}static p(t,e){const s=e.attribute;return!1===s?void 0:\"string\"==typeof s?s:\"string\"==typeof t?t.toLowerCase():void 0}u(){var t;this._=new Promise((t=>this.enableUpdating=t)),this._$AL=new Map,this.g(),this.requestUpdate(),null===(t=this.constructor.v)||void 0===t||t.forEach((t=>t(this)))}addController(t){var e,s;(null!==(e=this.S)&&void 0!==e?e:this.S=[]).push(t),void 0!==this.renderRoot&&this.isConnected&&(null===(s=t.hostConnected)||void 0===s||s.call(t))}removeController(t){var e;null===(e=this.S)||void 0===e||e.splice(this.S.indexOf(t)>>>0,1)}g(){this.constructor.elementProperties.forEach(((t,e)=>{this.hasOwnProperty(e)&&(this.o.set(e,this[e]),delete this[e])}))}createRenderRoot(){var t;const e=null!==(t=this.shadowRoot)&&void 0!==t?t:this.attachShadow(this.constructor.shadowRootOptions);return h(e,this.constructor.elementStyles),e}connectedCallback(){var t;void 0===this.renderRoot&&(this.renderRoot=this.createRenderRoot()),this.enableUpdating(!0),null===(t=this.S)||void 0===t||t.forEach((t=>{var e;return null===(e=t.hostConnected)||void 0===e?void 0:e.call(t)}))}enableUpdating(t){}disconnectedCallback(){var t;null===(t=this.S)||void 0===t||t.forEach((t=>{var e;return null===(e=t.hostDisconnected)||void 0===e?void 0:e.call(t)}))}attributeChangedCallback(t,e,s){this._$AK(t,s)}$(t,e,s=m){var i;const n=this.constructor.p(t,s);if(void 0!==n&&!0===s.reflect){const o=(void 0!==(null===(i=s.converter)||void 0===i?void 0:i.toAttribute)?s.converter:p).toAttribute(e,s.type);this.l=t,null==o?this.removeAttribute(n):this.setAttribute(n,o),this.l=null}}_$AK(t,e){var s;const i=this.constructor,n=i.m.get(t);if(void 0!==n&&this.l!==n){const t=i.getPropertyOptions(n),o=\"function\"==typeof t.converter?{fromAttribute:t.converter}:void 0!==(null===(s=t.converter)||void 0===s?void 0:s.fromAttribute)?t.converter:p;this.l=n,this[n]=o.fromAttribute(e,t.type),this.l=null}}requestUpdate(t,e,s){let i=!0;void 0!==t&&(((s=s||this.constructor.getPropertyOptions(t)).hasChanged||f)(this[t],e)?(this._$AL.has(t)||this._$AL.set(t,e),!0===s.reflect&&this.l!==t&&(void 0===this.C&&(this.C=new Map),this.C.set(t,s))):i=!1),!this.isUpdatePending&&i&&(this._=this.T())}async T(){this.isUpdatePending=!0;try{await this._}catch(t){Promise.reject(t)}const t=this.scheduleUpdate();return null!=t&&await t,!this.isUpdatePending}scheduleUpdate(){return this.performUpdate()}performUpdate(){var t;if(!this.isUpdatePending)return;this.hasUpdated,this.o&&(this.o.forEach(((t,e)=>this[e]=t)),this.o=void 0);let e=!1;const s=this._$AL;try{e=this.shouldUpdate(s),e?(this.willUpdate(s),null===(t=this.S)||void 0===t||t.forEach((t=>{var e;return null===(e=t.hostUpdate)||void 0===e?void 0:e.call(t)})),this.update(s)):this.P()}catch(t){throw e=!1,this.P(),t}e&&this._$AE(s)}willUpdate(t){}_$AE(t){var e;null===(e=this.S)||void 0===e||e.forEach((t=>{var e;return null===(e=t.hostUpdated)||void 0===e?void 0:e.call(t)})),this.hasUpdated||(this.hasUpdated=!0,this.firstUpdated(t)),this.updated(t)}P(){this._$AL=new Map,this.isUpdatePending=!1}get updateComplete(){return this.getUpdateComplete()}getUpdateComplete(){return this._}shouldUpdate(t){return!0}update(t){void 0!==this.C&&(this.C.forEach(((t,e)=>this.$(e,this[e],t))),this.C=void 0),this.P()}updated(t){}firstUpdated(t){}}\r\n/**\r\n * @license\r\n * Copyright 2017 Google LLC\r\n * SPDX-License-Identifier: BSD-3-Clause\r\n */var b;_[y]=!0,_.elementProperties=new Map,_.elementStyles=[],_.shadowRootOptions={mode:\"open\"},null==v||v({ReactiveElement:_}),(null!==(a=u.reactiveElementVersions)&&void 0!==a?a:u.reactiveElementVersions=[]).push(\"1.6.1\");const g=window,w=g.trustedTypes,S=w?w.createPolicy(\"lit-html\",{createHTML:t=>t}):void 0,$=\"$lit$\",C=`lit$${(Math.random()+\"\").slice(9)}$`,T=\"?\"+C,P=`<${T}>`,x=document,A=()=>x.createComment(\"\"),k=t=>null===t||\"object\"!=typeof t&&\"function\"!=typeof t,E=Array.isArray,M=t=>E(t)||\"function\"==typeof(null==t?void 0:t[Symbol.iterator]),U=\"[ \\t\\n\\f\\r]\",N=/<(?:(!--|\\/[^a-zA-Z])|(\\/?[a-zA-Z][^>\\s]*)|(\\/?$))/g,R=/-->/g,O=/>/g,V=RegExp(`>|${U}(?:([^\\\\s\"'>=/]+)(${U}*=${U}*(?:[^ \\t\\n\\f\\r\"'\\`<>=]|(\"|')|))|$)`,\"g\"),j=/'/g,z=/\"/g,L=/^(?:script|style|textarea|title)$/i,I=t=>(e,...s)=>({_$litType$:t,strings:e,values:s}),H=I(1),B=I(2),D=Symbol.for(\"lit-noChange\"),q=Symbol.for(\"lit-nothing\"),J=new WeakMap,W=x.createTreeWalker(x,129,null,!1),Z=(t,e)=>{const s=t.length-1,i=[];let n,o=2===e?\"<svg>\":\"\",r=N;for(let e=0;e<s;e++){const s=t[e];let l,h,a=-1,d=0;for(;d<s.length&&(r.lastIndex=d,h=r.exec(s),null!==h);)d=r.lastIndex,r===N?\"!--\"===h[1]?r=R:void 0!==h[1]?r=O:void 0!==h[2]?(L.test(h[2])&&(n=RegExp(\"</\"+h[2],\"g\")),r=V):void 0!==h[3]&&(r=V):r===V?\">\"===h[0]?(r=null!=n?n:N,a=-1):void 0===h[1]?a=-2:(a=r.lastIndex-h[2].length,l=h[1],r=void 0===h[3]?V:'\"'===h[3]?z:j):r===z||r===j?r=V:r===R||r===O?r=N:(r=V,n=void 0);const c=r===V&&t[e+1].startsWith(\"/>\")?\" \":\"\";o+=r===N?s+P:a>=0?(i.push(l),s.slice(0,a)+$+s.slice(a)+C+c):s+C+(-2===a?(i.push(void 0),e):c)}const l=o+(t[s]||\"<?>\")+(2===e?\"</svg>\":\"\");if(!Array.isArray(t)||!t.hasOwnProperty(\"raw\"))throw Error(\"invalid template strings array\");return[void 0!==S?S.createHTML(l):l,i]};class F{constructor({strings:t,_$litType$:e},s){let i;this.parts=[];let n=0,o=0;const r=t.length-1,l=this.parts,[h,a]=Z(t,e);if(this.el=F.createElement(h,s),W.currentNode=this.el.content,2===e){const t=this.el.content,e=t.firstChild;e.remove(),t.append(...e.childNodes)}for(;null!==(i=W.nextNode())&&l.length<r;){if(1===i.nodeType){if(i.hasAttributes()){const t=[];for(const e of i.getAttributeNames())if(e.endsWith($)||e.startsWith(C)){const s=a[o++];if(t.push(e),void 0!==s){const t=i.getAttribute(s.toLowerCase()+$).split(C),e=/([.?@])?(.*)/.exec(s);l.push({type:1,index:n,name:e[2],strings:t,ctor:\".\"===e[1]?Y:\"?\"===e[1]?it:\"@\"===e[1]?st:X})}else l.push({type:6,index:n})}for(const e of t)i.removeAttribute(e)}if(L.test(i.tagName)){const t=i.textContent.split(C),e=t.length-1;if(e>0){i.textContent=w?w.emptyScript:\"\";for(let s=0;s<e;s++)i.append(t[s],A()),W.nextNode(),l.push({type:2,index:++n});i.append(t[e],A())}}}else if(8===i.nodeType)if(i.data===T)l.push({type:2,index:n});else{let t=-1;for(;-1!==(t=i.data.indexOf(C,t+1));)l.push({type:7,index:n}),t+=C.length-1}n++}}static createElement(t,e){const s=x.createElement(\"template\");return s.innerHTML=t,s}}function G(t,e,s=t,i){var n,o,r,l;if(e===D)return e;let h=void 0!==i?null===(n=s.A)||void 0===n?void 0:n[i]:s.k;const a=k(e)?void 0:e._$litDirective$;return(null==h?void 0:h.constructor)!==a&&(null===(o=null==h?void 0:h._$AO)||void 0===o||o.call(h,!1),void 0===a?h=void 0:(h=new a(t),h._$AT(t,s,i)),void 0!==i?(null!==(r=(l=s).A)&&void 0!==r?r:l.A=[])[i]=h:s.k=h),void 0!==h&&(e=G(t,h._$AS(t,e.values),h,i)),e}class K{constructor(t,e){this._$AV=[],this._$AN=void 0,this._$AD=t,this._$AM=e}get parentNode(){return this._$AM.parentNode}get _$AU(){return this._$AM._$AU}M(t){var e;const{el:{content:s},parts:i}=this._$AD,n=(null!==(e=null==t?void 0:t.creationScope)&&void 0!==e?e:x).importNode(s,!0);W.currentNode=n;let o=W.nextNode(),r=0,l=0,h=i[0];for(;void 0!==h;){if(r===h.index){let e;2===h.type?e=new Q(o,o.nextSibling,this,t):1===h.type?e=new h.ctor(o,h.name,h.strings,this,t):6===h.type&&(e=new et(o,this,t)),this._$AV.push(e),h=i[++l]}r!==(null==h?void 0:h.index)&&(o=W.nextNode(),r++)}return n}U(t){let e=0;for(const s of this._$AV)void 0!==s&&(void 0!==s.strings?(s._$AI(t,s,e),e+=s.strings.length-2):s._$AI(t[e])),e++}}class Q{constructor(t,e,s,i){var n;this.type=2,this._$AH=q,this._$AN=void 0,this._$AA=t,this._$AB=e,this._$AM=s,this.options=i,this.N=null===(n=null==i?void 0:i.isConnected)||void 0===n||n}get _$AU(){var t,e;return null!==(e=null===(t=this._$AM)||void 0===t?void 0:t._$AU)&&void 0!==e?e:this.N}get parentNode(){let t=this._$AA.parentNode;const e=this._$AM;return void 0!==e&&11===(null==t?void 0:t.nodeType)&&(t=e.parentNode),t}get startNode(){return this._$AA}get endNode(){return this._$AB}_$AI(t,e=this){t=G(this,t,e),k(t)?t===q||null==t||\"\"===t?(this._$AH!==q&&this._$AR(),this._$AH=q):t!==this._$AH&&t!==D&&this.R(t):void 0!==t._$litType$?this.O(t):void 0!==t.nodeType?this.V(t):M(t)?this.j(t):this.R(t)}L(t){return this._$AA.parentNode.insertBefore(t,this._$AB)}V(t){this._$AH!==t&&(this._$AR(),this._$AH=this.L(t))}R(t){this._$AH!==q&&k(this._$AH)?this._$AA.nextSibling.data=t:this.V(x.createTextNode(t)),this._$AH=t}O(t){var e;const{values:s,_$litType$:i}=t,n=\"number\"==typeof i?this._$AC(t):(void 0===i.el&&(i.el=F.createElement(i.h,this.options)),i);if((null===(e=this._$AH)||void 0===e?void 0:e._$AD)===n)this._$AH.U(s);else{const t=new K(n,this),e=t.M(this.options);t.U(s),this.V(e),this._$AH=t}}_$AC(t){let e=J.get(t.strings);return void 0===e&&J.set(t.strings,e=new F(t)),e}j(t){E(this._$AH)||(this._$AH=[],this._$AR());const e=this._$AH;let s,i=0;for(const n of t)i===e.length?e.push(s=new Q(this.L(A()),this.L(A()),this,this.options)):s=e[i],s._$AI(n),i++;i<e.length&&(this._$AR(s&&s._$AB.nextSibling,i),e.length=i)}_$AR(t=this._$AA.nextSibling,e){var s;for(null===(s=this._$AP)||void 0===s||s.call(this,!1,!0,e);t&&t!==this._$AB;){const e=t.nextSibling;t.remove(),t=e}}setConnected(t){var e;void 0===this._$AM&&(this.N=t,null===(e=this._$AP)||void 0===e||e.call(this,t))}}class X{constructor(t,e,s,i,n){this.type=1,this._$AH=q,this._$AN=void 0,this.element=t,this.name=e,this._$AM=i,this.options=n,s.length>2||\"\"!==s[0]||\"\"!==s[1]?(this._$AH=Array(s.length-1).fill(new String),this.strings=s):this._$AH=q}get tagName(){return this.element.tagName}get _$AU(){return this._$AM._$AU}_$AI(t,e=this,s,i){const n=this.strings;let o=!1;if(void 0===n)t=G(this,t,e,0),o=!k(t)||t!==this._$AH&&t!==D,o&&(this._$AH=t);else{const i=t;let r,l;for(t=n[0],r=0;r<n.length-1;r++)l=G(this,i[s+r],e,r),l===D&&(l=this._$AH[r]),o||(o=!k(l)||l!==this._$AH[r]),l===q?t=q:t!==q&&(t+=(null!=l?l:\"\")+n[r+1]),this._$AH[r]=l}o&&!i&&this.I(t)}I(t){t===q?this.element.removeAttribute(this.name):this.element.setAttribute(this.name,null!=t?t:\"\")}}class Y extends X{constructor(){super(...arguments),this.type=3}I(t){this.element[this.name]=t===q?void 0:t}}const tt=w?w.emptyScript:\"\";class it extends X{constructor(){super(...arguments),this.type=4}I(t){t&&t!==q?this.element.setAttribute(this.name,tt):this.element.removeAttribute(this.name)}}class st extends X{constructor(t,e,s,i,n){super(t,e,s,i,n),this.type=5}_$AI(t,e=this){var s;if((t=null!==(s=G(this,t,e,0))&&void 0!==s?s:q)===D)return;const i=this._$AH,n=t===q&&i!==q||t.capture!==i.capture||t.once!==i.once||t.passive!==i.passive,o=t!==q&&(i===q||n);n&&this.element.removeEventListener(this.name,this,i),o&&this.element.addEventListener(this.name,this,t),this._$AH=t}handleEvent(t){var e,s;\"function\"==typeof this._$AH?this._$AH.call(null!==(s=null===(e=this.options)||void 0===e?void 0:e.host)&&void 0!==s?s:this.element,t):this._$AH.handleEvent(t)}}class et{constructor(t,e,s){this.element=t,this.type=6,this._$AN=void 0,this._$AM=e,this.options=s}get _$AU(){return this._$AM._$AU}_$AI(t){G(this,t)}}const ot={H:$,B:C,D:T,q:1,J:Z,W:K,Z:M,F:G,G:Q,K:X,X:it,Y:st,tt:Y,it:et},nt=g.litHtmlPolyfillSupport;null==nt||nt(F,Q),(null!==(b=g.litHtmlVersions)&&void 0!==b?b:g.litHtmlVersions=[]).push(\"2.7.3\");const rt=(t,e,s)=>{var i,n;const o=null!==(i=null==s?void 0:s.renderBefore)&&void 0!==i?i:e;let r=o._$litPart$;if(void 0===r){const t=null!==(n=null==s?void 0:s.renderBefore)&&void 0!==n?n:null;o._$litPart$=r=new Q(e.insertBefore(A(),t),t,void 0,null!=s?s:{})}return r._$AI(t),r};\r\n/**\r\n * @license\r\n * Copyright 2017 Google LLC\r\n * SPDX-License-Identifier: BSD-3-Clause\r\n */var ht,lt;const at=_;class ut extends _{constructor(){super(...arguments),this.renderOptions={host:this},this.st=void 0}createRenderRoot(){var t,e;const s=super.createRenderRoot();return null!==(t=(e=this.renderOptions).renderBefore)&&void 0!==t||(e.renderBefore=s.firstChild),s}update(t){const e=this.render();this.hasUpdated||(this.renderOptions.isConnected=this.isConnected),super.update(t),this.st=rt(e,this.renderRoot,this.renderOptions)}connectedCallback(){var t;super.connectedCallback(),null===(t=this.st)||void 0===t||t.setConnected(!0)}disconnectedCallback(){var t;super.disconnectedCallback(),null===(t=this.st)||void 0===t||t.setConnected(!1)}render(){return D}}ut.finalized=!0,ut._$litElement$=!0,null===(ht=globalThis.litElementHydrateSupport)||void 0===ht||ht.call(globalThis,{LitElement:ut});const ct=globalThis.litElementPolyfillSupport;null==ct||ct({LitElement:ut});const dt={_$AK:(t,e,s)=>{t._$AK(e,s)},_$AL:t=>t._$AL};(null!==(lt=globalThis.litElementVersions)&&void 0!==lt?lt:globalThis.litElementVersions=[]).push(\"3.3.2\");\r\n/**\r\n * @license\r\n * Copyright 2022 Google LLC\r\n * SPDX-License-Identifier: BSD-3-Clause\r\n */\r\nconst vt=!1;export{o as CSSResult,ut as LitElement,_ as ReactiveElement,at as UpdatingElement,dt as _$LE,ot as _$LH,h as adoptStyles,r as css,p as defaultConverter,l as getCompatibleStyle,H as html,vt as isServer,D as noChange,f as notEqual,q as nothing,rt as render,i as supportsAdoptingStyleSheets,B as svg,n as unsafeCSS};\r\n", "import { html, css, LitElement } from '../assets/lit-core-2.7.4.min.js';\r\n\r\nexport class MainHeader extends LitElement {\r\n    static properties = {\r\n        isTogglingSession: { type: Boolean, state: true },\r\n        shortcuts: { type: Object, state: true },\r\n        listenSessionStatus: { type: String, state: true },\r\n    };\r\n\r\n    static styles = css`\r\n        :host {\r\n            display: flex;\r\n            transition: transform 0.2s cubic-bezier(0.23, 1, 0.32, 1), opacity 0.2s ease-out;\r\n        }\r\n\r\n        :host(.hiding) {\r\n            animation: slideUp 0.3s cubic-bezier(0.4, 0, 0.6, 1) forwards;\r\n        }\r\n\r\n        :host(.showing) {\r\n            animation: slideDown 0.35s cubic-bezier(0.34, 1.56, 0.64, 1) forwards;\r\n        }\r\n\r\n        :host(.sliding-in) {\r\n            animation: fadeIn 0.2s ease-out forwards;\r\n        }\r\n\r\n        :host(.hidden) {\r\n            opacity: 0;\r\n            transform: translateY(-150%) scale(0.85);\r\n            pointer-events: none;\r\n        }\r\n\r\n\r\n        * {\r\n            font-family: 'Helvetica Neue', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;\r\n            cursor: default;\r\n            user-select: none;\r\n        }\r\n\r\n        .header {\r\n            -webkit-app-region: drag;\r\n            width: max-content;\r\n            height: 47px;\r\n            padding: 2px 10px 2px 13px;\r\n            background: transparent;\r\n            overflow: hidden;\r\n            border-radius: 9000px;\r\n            /* backdrop-filter: blur(1px); */\r\n            justify-content: space-between;\r\n            align-items: center;\r\n            display: inline-flex;\r\n            box-sizing: border-box;\r\n            position: relative;\r\n        }\r\n\r\n        .header::before {\r\n            content: '';\r\n            position: absolute;\r\n            top: 0; left: 0; right: 0; bottom: 0;\r\n            width: 100%;\r\n            height: 100%;\r\n            background: rgba(0, 0, 0, 0.6);\r\n            border-radius: 9000px;\r\n            z-index: -1;\r\n        }\r\n\r\n        .header::after {\r\n            content: '';\r\n            position: absolute;\r\n            top: 0; left: 0; right: 0; bottom: 0;\r\n            border-radius: 9000px;\r\n            padding: 1px;\r\n            background: linear-gradient(169deg, rgba(255, 255, 255, 0.17) 0%, rgba(255, 255, 255, 0.08) 50%, rgba(255, 255, 255, 0.17) 100%); \r\n            -webkit-mask:\r\n                linear-gradient(#fff 0 0) content-box,\r\n                linear-gradient(#fff 0 0);\r\n            -webkit-mask-composite: destination-out;\r\n            mask-composite: exclude;\r\n            pointer-events: none;\r\n        }\r\n\r\n        .listen-button {\r\n            -webkit-app-region: no-drag;\r\n            height: 26px;\r\n            padding: 0 13px;\r\n            background: transparent;\r\n            border-radius: 9000px;\r\n            justify-content: center;\r\n            width: 78px;\r\n            align-items: center;\r\n            gap: 6px;\r\n            display: flex;\r\n            border: none;\r\n            cursor: pointer;\r\n            position: relative;\r\n        }\r\n\r\n        .listen-button:disabled {\r\n            cursor: default;\r\n            opacity: 0.8;\r\n        }\r\n\r\n        .listen-button.active::before {\r\n            background: rgba(215, 0, 0, 0.5);\r\n        }\r\n\r\n        .listen-button.active:hover::before {\r\n            background: rgba(255, 20, 20, 0.6);\r\n        }\r\n\r\n        .listen-button.done {\r\n            background-color: rgba(255, 255, 255, 0.6);\r\n            transition: background-color 0.15s ease;\r\n        }\r\n\r\n        .listen-button.done .action-text-content {\r\n            color: black;\r\n        }\r\n        \r\n        .listen-button.done .listen-icon svg rect,\r\n        .listen-button.done .listen-icon svg path {\r\n            fill: black;\r\n        }\r\n\r\n        .listen-button.done:hover {\r\n            background-color: #f0f0f0;\r\n        }\r\n\r\n        .listen-button:hover::before {\r\n            background: rgba(255, 255, 255, 0.18);\r\n        }\r\n\r\n        .listen-button::before {\r\n            content: '';\r\n            position: absolute;\r\n            top: 0; left: 0; right: 0; bottom: 0;\r\n            background: rgba(255, 255, 255, 0.14);\r\n            border-radius: 9000px;\r\n            z-index: -1;\r\n            transition: background 0.15s ease;\r\n        }\r\n\r\n        .listen-button::after {\r\n            content: '';\r\n            position: absolute;\r\n            top: 0; left: 0; right: 0; bottom: 0;\r\n            border-radius: 9000px;\r\n            padding: 1px;\r\n            background: linear-gradient(169deg, rgba(255, 255, 255, 0.17) 0%, rgba(255, 255, 255, 0.08) 50%, rgba(255, 255, 255, 0.17) 100%);\r\n            -webkit-mask:\r\n                linear-gradient(#fff 0 0) content-box,\r\n                linear-gradient(#fff 0 0);\r\n            -webkit-mask-composite: destination-out;\r\n            mask-composite: exclude;\r\n            pointer-events: none;\r\n        }\r\n\r\n        .listen-button.done::after {\r\n            display: none;\r\n        }\r\n\r\n        .loading-dots {\r\n            display: flex;\r\n            align-items: center;\r\n            gap: 5px;\r\n        }\r\n\r\n        .loading-dots span {\r\n            width: 6px;\r\n            height: 6px;\r\n            background-color: white;\r\n            border-radius: 50%;\r\n            animation: pulse 1.4s infinite ease-in-out both;\r\n        }\r\n        .loading-dots span:nth-of-type(1) {\r\n            animation-delay: -0.32s;\r\n        }\r\n        .loading-dots span:nth-of-type(2) {\r\n            animation-delay: -0.16s;\r\n        }\r\n        @keyframes pulse {\r\n            0%, 80%, 100% {\r\n                opacity: 0.2;\r\n            }\r\n            40% {\r\n                opacity: 1.0;\r\n            }\r\n        }\r\n\r\n        .header-actions {\r\n            -webkit-app-region: no-drag;\r\n            height: 26px;\r\n            box-sizing: border-box;\r\n            justify-content: flex-start;\r\n            align-items: center;\r\n            gap: 9px;\r\n            display: flex;\r\n            padding: 0 8px;\r\n            border-radius: 6px;\r\n            transition: background 0.15s ease;\r\n        }\r\n\r\n        .header-actions:hover {\r\n            background: rgba(255, 255, 255, 0.1);\r\n        }\r\n\r\n        .ask-action {\r\n            margin-left: 4px;\r\n        }\r\n\r\n        .action-button,\r\n        .action-text {\r\n            padding-bottom: 1px;\r\n            justify-content: center;\r\n            align-items: center;\r\n            gap: 10px;\r\n            display: flex;\r\n        }\r\n\r\n        .action-text-content {\r\n            color: white;\r\n            font-size: 12px;\r\n            font-family: 'Helvetica Neue', sans-serif;\r\n            font-weight: 500; /* Medium */\r\n            word-wrap: break-word;\r\n        }\r\n\r\n        .icon-container {\r\n            justify-content: flex-start;\r\n            align-items: center;\r\n            gap: 4px;\r\n            display: flex;\r\n        }\r\n\r\n        .icon-container.ask-icons svg,\r\n        .icon-container.showhide-icons svg {\r\n            width: 12px;\r\n            height: 12px;\r\n        }\r\n\r\n        .listen-icon svg {\r\n            width: 12px;\r\n            height: 11px;\r\n            position: relative;\r\n            top: 1px;\r\n        }\r\n\r\n        .icon-box {\r\n            color: white;\r\n            font-size: 12px;\r\n            font-family: 'Helvetica Neue', sans-serif;\r\n            font-weight: 500;\r\n            background-color: rgba(255, 255, 255, 0.1);\r\n            border-radius: 13%;\r\n            width: 18px;\r\n            height: 18px;\r\n            display: flex;\r\n            align-items: center;\r\n            justify-content: center;\r\n        }\r\n\r\n        .settings-button {\r\n            -webkit-app-region: no-drag;\r\n            padding: 5px;\r\n            border-radius: 50%;\r\n            background: transparent;\r\n            transition: background 0.15s ease;\r\n            color: white;\r\n            border: none;\r\n            cursor: pointer;\r\n            display: flex;\r\n            align-items: center;\r\n            gap: 6px;\r\n        }\r\n\r\n        .settings-button:hover {\r\n            background: rgba(255, 255, 255, 0.1);\r\n        }\r\n\r\n        .settings-icon {\r\n            display: flex;\r\n            align-items: center;\r\n            justify-content: center;\r\n            padding: 3px;\r\n        }\r\n\r\n        .settings-icon svg {\r\n            width: 16px;\r\n            height: 16px;\r\n        }\r\n        /* ────────────────[ GLASS BYPASS ]─────────────── */\r\n        :host-context(body.has-glass) .header,\r\n        :host-context(body.has-glass) .listen-button,\r\n        :host-context(body.has-glass) .header-actions,\r\n        :host-context(body.has-glass) .settings-button {\r\n            background: transparent !important;\r\n            filter: none !important;\r\n            box-shadow: none !important;\r\n            backdrop-filter: none !important;\r\n        }\r\n        :host-context(body.has-glass) .icon-box {\r\n            background: transparent !important;\r\n            border: none !important;\r\n        }\r\n\r\n        :host-context(body.has-glass) .header::before,\r\n        :host-context(body.has-glass) .header::after,\r\n        :host-context(body.has-glass) .listen-button::before,\r\n        :host-context(body.has-glass) .listen-button::after {\r\n            display: none !important;\r\n        }\r\n\r\n        :host-context(body.has-glass) .header-actions:hover,\r\n        :host-context(body.has-glass) .settings-button:hover,\r\n        :host-context(body.has-glass) .listen-button:hover::before {\r\n            background: transparent !important;\r\n        }\r\n        :host-context(body.has-glass) * {\r\n            animation: none !important;\r\n            transition: none !important;\r\n            transform: none !important;\r\n            filter: none !important;\r\n            backdrop-filter: none !important;\r\n            box-shadow: none !important;\r\n        }\r\n\r\n        :host-context(body.has-glass) .header,\r\n        :host-context(body.has-glass) .listen-button,\r\n        :host-context(body.has-glass) .header-actions,\r\n        :host-context(body.has-glass) .settings-button,\r\n        :host-context(body.has-glass) .icon-box {\r\n            border-radius: 0 !important;\r\n        }\r\n        :host-context(body.has-glass) {\r\n            animation: none !important;\r\n            transition: none !important;\r\n            transform: none !important;\r\n            will-change: auto !important;\r\n        }\r\n        `;\r\n\r\n    constructor() {\r\n        super();\r\n        this.shortcuts = {};\r\n        this.isVisible = true;\r\n        this.isAnimating = false;\r\n        this.hasSlidIn = false;\r\n        this.settingsHideTimer = null;\r\n        this.isTogglingSession = false;\r\n        this.listenSessionStatus = 'beforeSession';\r\n        this.animationEndTimer = null;\r\n        this.handleAnimationEnd = this.handleAnimationEnd.bind(this);\r\n        this.handleMouseMove = this.handleMouseMove.bind(this);\r\n        this.handleMouseUp = this.handleMouseUp.bind(this);\r\n        this.dragState = null;\r\n        this.wasJustDragged = false;\r\n    }\r\n\r\n    _getListenButtonText(status) {\r\n        switch (status) {\r\n            case 'beforeSession': return 'Listen';\r\n            case 'inSession'   : return 'Stop';\r\n            case 'afterSession': return 'Done';\r\n            default            : return 'Listen';\r\n        }\r\n    }\r\n\r\n    async handleMouseDown(e) {\r\n        e.preventDefault();\r\n\r\n        const initialPosition = await window.api.mainHeader.getHeaderPosition();\r\n\r\n        this.dragState = {\r\n            initialMouseX: e.screenX,\r\n            initialMouseY: e.screenY,\r\n            initialWindowX: initialPosition.x,\r\n            initialWindowY: initialPosition.y,\r\n            moved: false,\r\n        };\r\n\r\n        window.addEventListener('mousemove', this.handleMouseMove, { capture: true });\r\n        window.addEventListener('mouseup', this.handleMouseUp, { once: true, capture: true });\r\n    }\r\n\r\n    handleMouseMove(e) {\r\n        if (!this.dragState) return;\r\n\r\n        const deltaX = Math.abs(e.screenX - this.dragState.initialMouseX);\r\n        const deltaY = Math.abs(e.screenY - this.dragState.initialMouseY);\r\n        \r\n        if (deltaX > 3 || deltaY > 3) {\r\n            this.dragState.moved = true;\r\n        }\r\n\r\n        const newWindowX = this.dragState.initialWindowX + (e.screenX - this.dragState.initialMouseX);\r\n        const newWindowY = this.dragState.initialWindowY + (e.screenY - this.dragState.initialMouseY);\r\n\r\n        window.api.mainHeader.moveHeaderTo(newWindowX, newWindowY);\r\n    }\r\n\r\n    handleMouseUp(e) {\r\n        if (!this.dragState) return;\r\n\r\n        const wasDragged = this.dragState.moved;\r\n\r\n        window.removeEventListener('mousemove', this.handleMouseMove, { capture: true });\r\n        this.dragState = null;\r\n\r\n        if (wasDragged) {\r\n            this.wasJustDragged = true;\r\n            setTimeout(() => {\r\n                this.wasJustDragged = false;\r\n            }, 0);\r\n        }\r\n    }\r\n\r\n    toggleVisibility() {\r\n        if (this.isAnimating) {\r\n            console.log('[MainHeader] Animation already in progress, ignoring toggle');\r\n            return;\r\n        }\r\n        \r\n        if (this.animationEndTimer) {\r\n            clearTimeout(this.animationEndTimer);\r\n            this.animationEndTimer = null;\r\n        }\r\n        \r\n        this.isAnimating = true;\r\n        \r\n        if (this.isVisible) {\r\n            this.hide();\r\n        } else {\r\n            this.show();\r\n        }\r\n    }\r\n\r\n    hide() {\r\n        this.classList.remove('showing');\r\n        this.classList.add('hiding');\r\n    }\r\n    \r\n    show() {\r\n        this.classList.remove('hiding', 'hidden');\r\n        this.classList.add('showing');\r\n    }\r\n    \r\n    handleAnimationEnd(e) {\r\n        if (e.target !== this) return;\r\n    \r\n        this.isAnimating = false;\r\n    \r\n        if (this.classList.contains('hiding')) {\r\n            this.classList.add('hidden');\r\n            if (window.api) {\r\n                window.api.mainHeader.sendHeaderAnimationFinished('hidden');\r\n            }\r\n        } else if (this.classList.contains('showing')) {\r\n            if (window.api) {\r\n                window.api.mainHeader.sendHeaderAnimationFinished('visible');\r\n            }\r\n        }\r\n    }\r\n\r\n    startSlideInAnimation() {\r\n        if (this.hasSlidIn) return;\r\n        this.classList.add('sliding-in');\r\n    }\r\n\r\n    connectedCallback() {\r\n        super.connectedCallback();\r\n        this.addEventListener('animationend', this.handleAnimationEnd);\r\n\r\n        if (window.api) {\r\n\r\n            this._sessionStateTextListener = (event, { success }) => {\r\n                if (success) {\r\n                    this.listenSessionStatus = ({\r\n                        beforeSession: 'inSession',\r\n                        inSession: 'afterSession',\r\n                        afterSession: 'beforeSession',\r\n                    })[this.listenSessionStatus] || 'beforeSession';\r\n                } else {\r\n                    this.listenSessionStatus = 'beforeSession';\r\n                }\r\n                this.isTogglingSession = false; // ✨ 로딩 상태만 해제\r\n            };\r\n            window.api.mainHeader.onListenChangeSessionResult(this._sessionStateTextListener);\r\n\r\n            this._shortcutListener = (event, keybinds) => {\r\n                console.log('[MainHeader] Received updated shortcuts:', keybinds);\r\n                this.shortcuts = keybinds;\r\n            };\r\n            window.api.mainHeader.onShortcutsUpdated(this._shortcutListener);\r\n        }\r\n    }\r\n\r\n    disconnectedCallback() {\r\n        super.disconnectedCallback();\r\n        this.removeEventListener('animationend', this.handleAnimationEnd);\r\n        \r\n        if (this.animationEndTimer) {\r\n            clearTimeout(this.animationEndTimer);\r\n            this.animationEndTimer = null;\r\n        }\r\n        \r\n        if (window.api) {\r\n            if (this._sessionStateTextListener) {\r\n                window.api.mainHeader.removeOnListenChangeSessionResult(this._sessionStateTextListener);\r\n            }\r\n            if (this._shortcutListener) {\r\n                window.api.mainHeader.removeOnShortcutsUpdated(this._shortcutListener);\r\n            }\r\n        }\r\n    }\r\n\r\n    showSettingsWindow(element) {\r\n        if (this.wasJustDragged) return;\r\n        if (window.api) {\r\n            console.log(`[MainHeader] showSettingsWindow called at ${Date.now()}`);\r\n            window.api.mainHeader.showSettingsWindow();\r\n\r\n        }\r\n    }\r\n\r\n    hideSettingsWindow() {\r\n        if (this.wasJustDragged) return;\r\n        if (window.api) {\r\n            console.log(`[MainHeader] hideSettingsWindow called at ${Date.now()}`);\r\n            window.api.mainHeader.hideSettingsWindow();\r\n        }\r\n    }\r\n\r\n    async _handleListenClick() {\r\n        if (this.wasJustDragged) return;\r\n        if (this.isTogglingSession) {\r\n            return;\r\n        }\r\n\r\n        this.isTogglingSession = true;\r\n\r\n        try {\r\n            const listenButtonText = this._getListenButtonText(this.listenSessionStatus);\r\n            if (window.api) {\r\n                await window.api.mainHeader.sendListenButtonClick(listenButtonText);\r\n            }\r\n        } catch (error) {\r\n            console.error('IPC invoke for session change failed:', error);\r\n            this.isTogglingSession = false;\r\n        }\r\n    }\r\n\r\n    async _handleAskClick() {\r\n        if (this.wasJustDragged) return;\r\n\r\n        try {\r\n            if (window.api) {\r\n                await window.api.mainHeader.sendAskButtonClick();\r\n            }\r\n        } catch (error) {\r\n            console.error('IPC invoke for ask button failed:', error);\r\n        }\r\n    }\r\n\r\n    async _handleToggleAllWindowsVisibility() {\r\n        if (this.wasJustDragged) return;\r\n\r\n        try {\r\n            if (window.api) {\r\n                await window.api.mainHeader.sendToggleAllWindowsVisibility();\r\n            }\r\n        } catch (error) {\r\n            console.error('IPC invoke for all windows visibility button failed:', error);\r\n        }\r\n    }\r\n\r\n\r\n    renderShortcut(accelerator) {\r\n        if (!accelerator) return html``;\r\n\r\n        const keyMap = {\r\n            'Cmd': '⌘', 'Command': '⌘',\r\n            'Ctrl': '⌃', 'Control': '⌃',\r\n            'Alt': '⌥', 'Option': '⌥',\r\n            'Shift': '⇧',\r\n            'Enter': '↵',\r\n            'Backspace': '⌫',\r\n            'Delete': '⌦',\r\n            'Tab': '⇥',\r\n            'Escape': '⎋',\r\n            'Up': '↑', 'Down': '↓', 'Left': '←', 'Right': '→',\r\n            '\\\\': html`<svg viewBox=\"0 0 6 12\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\" style=\"width:6px; height:12px;\"><path d=\"M1.5 1.3L5.1 10.6\" stroke=\"white\" stroke-width=\"1.4\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/></svg>`,\r\n        };\r\n\r\n        const keys = accelerator.split('+');\r\n        return html`${keys.map(key => html`\r\n            <div class=\"icon-box\">${keyMap[key] || key}</div>\r\n        `)}`;\r\n    }\r\n\r\n    render() {\r\n        const listenButtonText = this._getListenButtonText(this.listenSessionStatus);\r\n    \r\n        const buttonClasses = {\r\n            active: listenButtonText === 'Stop',\r\n            done: listenButtonText === 'Done',\r\n        };\r\n        const showStopIcon = listenButtonText === 'Stop' || listenButtonText === 'Done';\r\n\r\n        return html`\r\n            <div class=\"header\" @mousedown=${this.handleMouseDown}>\r\n                <button \r\n                    class=\"listen-button ${Object.keys(buttonClasses).filter(k => buttonClasses[k]).join(' ')}\"\r\n                    @click=${this._handleListenClick}\r\n                    ?disabled=${this.isTogglingSession}\r\n                >\r\n                    ${this.isTogglingSession\r\n                        ? html`\r\n                            <div class=\"loading-dots\">\r\n                                <span></span><span></span><span></span>\r\n                            </div>\r\n                        `\r\n                        : html`\r\n                            <div class=\"action-text\">\r\n                                <div class=\"action-text-content\">${listenButtonText}</div>\r\n                            </div>\r\n                            <div class=\"listen-icon\">\r\n                                ${showStopIcon\r\n                                    ? html`\r\n                                        <svg width=\"9\" height=\"9\" viewBox=\"0 0 9 9\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\r\n                                            <rect width=\"9\" height=\"9\" rx=\"1\" fill=\"white\"/>\r\n                                        </svg>\r\n                                    `\r\n                                    : html`\r\n                                        <svg width=\"12\" height=\"11\" viewBox=\"0 0 12 11\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\r\n                                            <path d=\"M1.69922 2.7515C1.69922 2.37153 2.00725 2.0635 2.38722 2.0635H2.73122C3.11119 2.0635 3.41922 2.37153 3.41922 2.7515V8.2555C3.41922 8.63547 3.11119 8.9435 2.73122 8.9435H2.38722C2.00725 8.9435 1.69922 8.63547 1.69922 8.2555V2.7515Z\" fill=\"white\"/>\r\n                                            <path d=\"M5.13922 1.3755C5.13922 0.995528 5.44725 0.6875 5.82722 0.6875H6.17122C6.55119 0.6875 6.85922 0.995528 6.85922 1.3755V9.6315C6.85922 10.0115 6.55119 10.3195 6.17122 10.3195H5.82722C5.44725 10.3195 5.13922 10.0115 5.13922 9.6315V1.3755Z\" fill=\"white\"/>\r\n                                            <path d=\"M8.57922 3.0955C8.57922 2.71553 8.88725 2.4075 9.26722 2.4075H9.61122C9.99119 2.4075 10.2992 2.71553 10.2992 3.0955V7.9115C10.2992 8.29147 9.99119 8.5995 9.61122 8.5995H9.26722C8.88725 8.5995 8.57922 8.29147 8.57922 7.9115V3.0955Z\" fill=\"white\"/>\r\n                                        </svg>\r\n                                    `}\r\n                            </div>\r\n                        `}\r\n                </button>\r\n\r\n                <div class=\"header-actions ask-action\" @click=${() => this._handleAskClick()}>\r\n                    <div class=\"action-text\">\r\n                        <div class=\"action-text-content\">Ask</div>\r\n                    </div>\r\n                    <div class=\"icon-container\">\r\n                        ${this.renderShortcut(this.shortcuts.nextStep)}\r\n                    </div>\r\n                </div>\r\n\r\n                <div class=\"header-actions\" @click=${() => this._handleToggleAllWindowsVisibility()}>\r\n                    <div class=\"action-text\">\r\n                        <div class=\"action-text-content\">Show/Hide</div>\r\n                    </div>\r\n                    <div class=\"icon-container\">\r\n                        ${this.renderShortcut(this.shortcuts.toggleVisibility)}\r\n                    </div>\r\n                </div>\r\n\r\n                <button \r\n                    class=\"settings-button\"\r\n                    @mouseenter=${(e) => this.showSettingsWindow(e.currentTarget)}\r\n                    @mouseleave=${() => this.hideSettingsWindow()}\r\n                >\r\n                    <div class=\"settings-icon\">\r\n                        <svg width=\"16\" height=\"17\" viewBox=\"0 0 16 17\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\r\n                            <path d=\"M8.0013 3.16406C7.82449 3.16406 7.65492 3.2343 7.5299 3.35932C7.40487 3.48435 7.33464 3.65392 7.33464 3.83073C7.33464 4.00754 7.40487 4.17711 7.5299 4.30213C7.65492 4.42716 7.82449 4.4974 8.0013 4.4974C8.17811 4.4974 8.34768 4.42716 8.47271 4.30213C8.59773 4.17711 8.66797 4.00754 8.66797 3.83073C8.66797 3.65392 8.59773 3.48435 8.47271 3.35932C8.34768 3.2343 8.17811 3.16406 8.0013 3.16406ZM8.0013 7.83073C7.82449 7.83073 7.65492 7.90097 7.5299 8.02599C7.40487 8.15102 7.33464 8.32058 7.33464 8.4974C7.33464 8.67421 7.40487 8.84378 7.5299 8.9688C7.65492 9.09382 7.82449 9.16406 8.0013 9.16406C8.17811 9.16406 8.34768 9.09382 8.47271 8.9688C8.59773 8.84378 8.66797 8.67421 8.66797 8.4974C8.66797 8.32058 8.59773 8.15102 8.47271 8.02599C8.34768 7.90097 8.17811 7.83073 8.0013 7.83073ZM8.0013 12.4974C7.82449 12.4974 7.65492 12.5676 7.5299 12.6927C7.40487 12.8177 7.33464 12.9873 7.33464 13.1641C7.33464 13.3409 7.40487 13.5104 7.5299 13.6355C7.65492 13.7605 7.82449 13.8307 8.0013 13.8307C8.17811 13.8307 8.34768 13.7605 8.47271 13.6355C8.59773 13.5104 8.66797 13.3409 8.66797 13.1641C8.66797 12.9873 8.59773 12.8177 8.47271 12.6927C8.34768 12.5676 8.17811 12.4974 8.0013 12.4974Z\" fill=\"white\" stroke=\"white\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\r\n                        </svg>\r\n                    </div>\r\n                </button>\r\n            </div>\r\n        `;\r\n    }\r\n}\r\n\r\ncustomElements.define('main-header', MainHeader);\r\n", "import { html, css, LitElement } from '../assets/lit-core-2.7.4.min.js';\r\n\r\nexport class <PERSON><PERSON><PERSON>eyHeader extends LitElement {\r\n    //////// after_modelStateService ////////\r\n    static properties = {\r\n        llmApiKey: { type: String },\r\n        sttApiKey: { type: String },\r\n        llmProvider: { type: String },\r\n        sttProvider: { type: String },\r\n        isLoading: { type: Boolean },\r\n        errorMessage: { type: String },\r\n        successMessage: { type: String },\r\n        providers: { type: Object, state: true },\r\n        modelSuggestions: { type: Array, state: true },\r\n        userModelHistory: { type: Array, state: true },\r\n        selectedLlmModel: { type: String, state: true },\r\n        selectedSttModel: { type: String, state: true },\r\n        ollamaStatus: { type: Object, state: true },\r\n        installingModel: { type: String, state: true },\r\n        installProgress: { type: Number, state: true },\r\n        whisperInstallingModels: { type: Object, state: true },\r\n        backCallback: { type: Function },\r\n        llmError: { type: String },\r\n        sttError: { type: String },\r\n    };\r\n    //////// after_modelStateService ////////\r\n\r\n    static styles = css`\r\n        :host {\r\n            display: block;\r\n            font-family:\r\n                'Inter',\r\n                -apple-system,\r\n                BlinkMacSystemFont,\r\n                'Segoe UI',\r\n                Roboto,\r\n                sans-serif;\r\n        }\r\n        * {\r\n            box-sizing: border-box;\r\n        }\r\n        .container {\r\n            width: 100%;\r\n            height: 100%;\r\n            padding: 24px 16px;\r\n            background: rgba(0, 0, 0, 0.64);\r\n            box-shadow: 0px 0px 0px 1.5px rgba(255, 255, 255, 0.64) inset;\r\n            border-radius: 16px;\r\n            flex-direction: column;\r\n            justify-content: flex-start;\r\n            align-items: flex-start;\r\n            gap: 24px;\r\n            display: flex;\r\n            -webkit-app-region: drag;\r\n        }\r\n        .header {\r\n            width: 100%;\r\n            position: relative;\r\n            display: flex;\r\n            justify-content: center;\r\n            align-items: center;\r\n            margin-bottom: 8px;\r\n        }\r\n        .close-button {\r\n            -webkit-app-region: no-drag;\r\n            position: absolute;\r\n            top: 16px;\r\n            right: 16px;\r\n            width: 20px;\r\n            height: 20px;\r\n            background: rgba(255, 255, 255, 0.1);\r\n            border: none;\r\n            border-radius: 5px;\r\n            color: rgba(255, 255, 255, 0.7);\r\n            cursor: pointer;\r\n            display: flex;\r\n            align-items: center;\r\n            justify-content: center;\r\n            transition: all 0.15s ease;\r\n            z-index: 10;\r\n            font-size: 16px;\r\n            line-height: 1;\r\n            padding: 0;\r\n        }\r\n        .close-button:hover {\r\n            background: rgba(255, 255, 255, 0.2);\r\n            color: rgba(255, 255, 255, 0.9);\r\n        }\r\n        .back-button {\r\n            -webkit-app-region: no-drag;\r\n            padding: 8px;\r\n            left: 0px;\r\n            top: -7px;\r\n            position: absolute;\r\n            background: rgba(132.6, 132.6, 132.6, 0.8);\r\n            border-radius: 16px;\r\n            border: 0.5px solid rgba(255, 255, 255, 0.5);\r\n            justify-content: center;\r\n            align-items: center;\r\n            gap: 4px;\r\n            display: flex;\r\n            cursor: pointer;\r\n            transition: background-color 0.2s ease;\r\n        }\r\n        .back-button:hover {\r\n            background: rgba(150, 150, 150, 0.9);\r\n        }\r\n        .arrow-icon-left {\r\n            border: solid #dcdcdc;\r\n            border-width: 0 1.2px 1.2px 0;\r\n            display: inline-block;\r\n            padding: 3px;\r\n            transform: rotate(135deg);\r\n        }\r\n        .back-button-text {\r\n            color: white;\r\n            font-size: 12px;\r\n            font-weight: 500;\r\n            padding-right: 4px;\r\n        }\r\n        .title {\r\n            color: white;\r\n            font-size: 14px;\r\n            font-weight: 700;\r\n        }\r\n        .section {\r\n            width: 100%;\r\n            display: flex;\r\n            flex-direction: column;\r\n            gap: 10px;\r\n        }\r\n        .row {\r\n            width: 100%;\r\n            display: flex;\r\n            justify-content: space-between;\r\n            align-items: center;\r\n        }\r\n        .label {\r\n            color: white;\r\n            font-size: 12px;\r\n            font-weight: 600;\r\n        }\r\n        .provider-selector {\r\n            display: flex;\r\n            width: 240px;\r\n            overflow: hidden;\r\n            border-radius: 12px;\r\n            border: 0.5px solid rgba(255, 255, 255, 0.5);\r\n        }\r\n        .provider-button {\r\n            -webkit-app-region: no-drag;\r\n            padding: 4px 8px;\r\n            background: rgba(20.4, 20.4, 20.4, 0.32);\r\n            color: #dcdcdc;\r\n            font-size: 11px;\r\n            font-weight: 450;\r\n            letter-spacing: 0.11px;\r\n            border: none;\r\n            cursor: pointer;\r\n            transition: background-color 0.2s ease;\r\n            flex: 1;\r\n        }\r\n        .provider-button:hover {\r\n            background: rgba(80, 80, 80, 0.48);\r\n        }\r\n        .provider-button[data-status='active'] {\r\n            background: rgba(142.8, 142.8, 142.8, 0.48);\r\n            color: white;\r\n        }\r\n        .api-input {\r\n            -webkit-app-region: no-drag;\r\n            width: 240px;\r\n            padding: 10px 8px;\r\n            background: rgba(61.2, 61.2, 61.2, 0.8);\r\n            border-radius: 6px;\r\n            border: 1px solid rgba(255, 255, 255, 0.24);\r\n            color: white;\r\n            font-size: 11px;\r\n            text-overflow: ellipsis;\r\n            font-family: inherit;\r\n            line-height: inherit;\r\n        }\r\n        .ollama-action-button {\r\n            -webkit-app-region: no-drag;\r\n            width: 240px;\r\n            padding: 10px 8px;\r\n            border-radius: 16px;\r\n            border: none;\r\n            color: white;\r\n            font-size: 12px;\r\n            font-weight: 500;\r\n            font-family: inherit;\r\n            cursor: pointer;\r\n            text-align: center;\r\n            transition: background-color 0.2s ease;\r\n        }\r\n        .ollama-action-button.install {\r\n            background: rgba(0, 122, 255, 0.2);\r\n        }\r\n        .ollama-action-button.start {\r\n            background: rgba(255, 200, 0, 0.2);\r\n        }\r\n        select.api-input {\r\n            -webkit-appearance: none;\r\n            appearance: none;\r\n            background-image: url(\"data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='white' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3e%3c/svg%3e\");\r\n            background-position: right 0.5rem center;\r\n            background-repeat: no-repeat;\r\n            background-size: 1.5em 1.5em;\r\n            padding-right: 2.5rem;\r\n        }\r\n        select.api-input option {\r\n            background: #333;\r\n            color: white;\r\n        }\r\n        .api-input::placeholder {\r\n            color: #a0a0a0;\r\n        }\r\n        .confirm-button-container {\r\n            width: 100%;\r\n            display: flex;\r\n            justify-content: flex-end;\r\n        }\r\n        .confirm-button {\r\n            -webkit-app-region: no-drag;\r\n            width: 240px;\r\n            padding: 8px;\r\n            background: rgba(132.6, 132.6, 132.6, 0.8);\r\n            box-shadow: 0px 2px 2px rgba(0, 0, 0, 0.16);\r\n            border-radius: 16px;\r\n            border: 1px solid rgba(255, 255, 255, 0.5);\r\n            color: white;\r\n            font-size: 12px;\r\n            font-weight: 500;\r\n            cursor: pointer;\r\n            transition: background-color 0.2s ease;\r\n        }\r\n        .confirm-button:hover {\r\n            background: rgba(150, 150, 150, 0.9);\r\n        }\r\n        .confirm-button:disabled {\r\n            background: rgba(255, 255, 255, 0.12);\r\n            color: #bebebe;\r\n            border: 0.5px solid rgba(255, 255, 255, 0.24);\r\n            box-shadow: none;\r\n            cursor: not-allowed;\r\n        }\r\n        .footer {\r\n            width: 100%;\r\n            text-align: center;\r\n            color: #dcdcdc;\r\n            font-size: 12px;\r\n            font-weight: 500;\r\n            line-height: 18px;\r\n        }\r\n        .footer-link {\r\n            text-decoration: underline;\r\n            cursor: pointer;\r\n            -webkit-app-region: no-drag;\r\n        }\r\n        .error-message,\r\n        .success-message {\r\n            position: absolute;\r\n            bottom: 70px;\r\n            left: 16px;\r\n            right: 16px;\r\n            text-align: center;\r\n            font-size: 11px;\r\n            font-weight: 500;\r\n            padding: 4px;\r\n            border-radius: 4px;\r\n        }\r\n        .error-message {\r\n            color: rgba(239, 68, 68, 0.9);\r\n        }\r\n        .success-message {\r\n            color: rgba(74, 222, 128, 0.9);\r\n        }\r\n        .message-fade-out {\r\n            animation: fadeOut 3s ease-in-out forwards;\r\n        }\r\n        @keyframes fadeOut {\r\n            0% {\r\n                opacity: 1;\r\n            }\r\n            66% {\r\n                opacity: 1;\r\n            }\r\n            100% {\r\n                opacity: 0;\r\n            }\r\n        }\r\n        .sliding-out {\r\n            animation: slideOut 0.3s ease-out forwards;\r\n        }\r\n        @keyframes slideOut {\r\n            from {\r\n                transform: translateY(0);\r\n                opacity: 1;\r\n            }\r\n            to {\r\n                transform: translateY(-100%);\r\n                opacity: 0;\r\n            }\r\n        }\r\n        .api-input.invalid {\r\n            outline: 1px solid #ff7070;\r\n            outline-offset: -1px;\r\n        }\r\n        .input-wrapper {\r\n            display: flex;\r\n            flex-direction: column;\r\n            gap: 4px;\r\n            align-items: flex-start;\r\n        }\r\n        .inline-error-message {\r\n            color: #ff7070;\r\n            font-size: 11px;\r\n            font-weight: 400;\r\n            letter-spacing: 0.11px;\r\n            word-wrap: break-word;\r\n            width: 240px;\r\n        }\r\n    `;\r\n\r\n\r\n    constructor() {\r\n        super();\r\n        this.isLoading = false;\r\n        this.errorMessage = '';\r\n        this.successMessage = '';\r\n        this.messageTimestamp = 0;\r\n        //////// after_modelStateService ////////\r\n        this.llmApiKey = '';\r\n        this.sttApiKey = '';\r\n        this.llmProvider = 'openai';\r\n        this.sttProvider = 'openai';\r\n        this.providers = { llm: [], stt: [] }; // 초기화\r\n        // Ollama related\r\n        this.modelSuggestions = [];\r\n        this.userModelHistory = [];\r\n        this.selectedLlmModel = '';\r\n        this.selectedSttModel = '';\r\n        this.ollamaStatus = { installed: false, running: false };\r\n        this.installingModel = null;\r\n        this.installProgress = 0;\r\n        this.whisperInstallingModels = {};\r\n        this.backCallback = () => {};\r\n        this.llmError = '';\r\n        this.sttError = '';\r\n\r\n        // Professional operation management system\r\n        this.activeOperations = new Map();\r\n        this.operationTimeouts = new Map();\r\n        this.connectionState = 'idle'; // idle, connecting, connected, failed, disconnected\r\n        this.lastStateChange = Date.now();\r\n        this.retryCount = 0;\r\n        this.maxRetries = 3;\r\n        this.baseRetryDelay = 1000;\r\n\r\n        // Backpressure and resource management\r\n        this.operationQueue = [];\r\n        this.maxConcurrentOperations = 2;\r\n        this.maxQueueSize = 5;\r\n        this.operationMetrics = {\r\n            totalOperations: 0,\r\n            successfulOperations: 0,\r\n            failedOperations: 0,\r\n            timeouts: 0,\r\n            averageResponseTime: 0,\r\n        };\r\n\r\n        // Configuration\r\n        this.ipcTimeout = 10000; // 10s for IPC calls\r\n        this.operationTimeout = 15000; // 15s for complex operations\r\n\r\n        // Health monitoring system\r\n        this.healthCheck = {\r\n            enabled: false,\r\n            intervalId: null,\r\n            intervalMs: 30000, // 30s\r\n            lastCheck: 0,\r\n            consecutiveFailures: 0,\r\n            maxFailures: 3,\r\n        };\r\n\r\n        // Load user model history from localStorage\r\n        this.loadUserModelHistory();\r\n        this.loadProviderConfig();\r\n        //////// after_modelStateService ////////\r\n\r\n        this.handleKeyPress = this.handleKeyPress.bind(this);\r\n        this.handleSubmit = this.handleSubmit.bind(this);\r\n        this.handleInput = this.handleInput.bind(this);\r\n        this.handleAnimationEnd = this.handleAnimationEnd.bind(this);\r\n        this.handleProviderChange = this.handleProviderChange.bind(this);\r\n        this.handleLlmProviderChange = this.handleLlmProviderChange.bind(this);\r\n        this.handleSttProviderChange = this.handleSttProviderChange.bind(this);\r\n        this.handleMessageFadeEnd = this.handleMessageFadeEnd.bind(this);\r\n        this.handleModelKeyPress = this.handleModelKeyPress.bind(this);\r\n        this.handleSttModelChange = this.handleSttModelChange.bind(this);\r\n        this.handleBack = this.handleBack.bind(this);\r\n        this.handleClose = this.handleClose.bind(this);\r\n    }\r\n\r\n    updated(changedProperties) {\r\n        super.updated(changedProperties);\r\n        this.dispatchEvent(new CustomEvent('content-changed', { bubbles: true, composed: true }));\r\n    }\r\n\r\n    reset() {\r\n        this.apiKey = '';\r\n        this.isLoading = false;\r\n        this.errorMessage = '';\r\n        this.validatedApiKey = null;\r\n        this.selectedProvider = 'openai';\r\n        this.requestUpdate();\r\n    }\r\n\r\n    handleBack() {\r\n        if (this.backCallback) {\r\n            this.backCallback();\r\n        }\r\n    }\r\n\r\n    async loadProviderConfig() {\r\n        if (!window.api?.apiKeyHeader) return;\r\n\r\n        try {\r\n            const [config, ollamaStatus] = await Promise.all([\r\n                window.api.apiKeyHeader.getProviderConfig(),\r\n                window.api.apiKeyHeader.getOllamaStatus(),\r\n            ]);\r\n\r\n            const llmProviders = [];\r\n            const sttProviders = [];\r\n\r\n            for (const id in config) {\r\n                // 'openai-glass' 같은 가상 Provider는 UI에 표시하지 않음\r\n                if (id.includes('-glass')) continue;\r\n                const hasLlmModels = config[id].llmModels.length > 0 || id === 'ollama';\r\n                const hasSttModels = config[id].sttModels.length > 0 || id === 'whisper';\r\n\r\n                if (hasLlmModels) {\r\n                    llmProviders.push({ id, name: config[id].name });\r\n                }\r\n                if (hasSttModels) {\r\n                    sttProviders.push({ id, name: config[id].name });\r\n                }\r\n            }\r\n\r\n            this.providers = { llm: llmProviders, stt: sttProviders };\r\n\r\n            // 기본 선택 값 설정\r\n            if (llmProviders.length > 0) this.llmProvider = llmProviders[0].id;\r\n            if (sttProviders.length > 0) this.sttProvider = sttProviders[0].id;\r\n\r\n            // Ollama 상태 및 모델 제안 로드\r\n            if (ollamaStatus?.success) {\r\n                this.ollamaStatus = {\r\n                    installed: ollamaStatus.installed,\r\n                    running: ollamaStatus.running,\r\n                };\r\n\r\n                // Load model suggestions if Ollama is running\r\n                if (ollamaStatus.running) {\r\n                    await this.loadModelSuggestions();\r\n                }\r\n            }\r\n\r\n            this.requestUpdate();\r\n        } catch (error) {\r\n            console.error('[ApiKeyHeader] Failed to load provider config:', error);\r\n        }\r\n    }\r\n\r\n    async handleMouseDown(e) {\r\n        if (e.target.tagName === 'INPUT' || e.target.tagName === 'BUTTON' || e.target.tagName === 'SELECT') {\r\n            return;\r\n        }\r\n\r\n        e.preventDefault();\r\n\r\n        if (!window.api?.apiKeyHeader) return;\r\n        const initialPosition = await window.api.apiKeyHeader.getHeaderPosition();\r\n\r\n        this.dragState = {\r\n            initialMouseX: e.screenX,\r\n            initialMouseY: e.screenY,\r\n            initialWindowX: initialPosition.x,\r\n            initialWindowY: initialPosition.y,\r\n            moved: false,\r\n        };\r\n\r\n        window.addEventListener('mousemove', this.handleMouseMove);\r\n        window.addEventListener('mouseup', this.handleMouseUp, { once: true });\r\n    }\r\n\r\n    handleMouseMove(e) {\r\n        if (!this.dragState) return;\r\n\r\n        const deltaX = Math.abs(e.screenX - this.dragState.initialMouseX);\r\n        const deltaY = Math.abs(e.screenY - this.dragState.initialMouseY);\r\n\r\n        if (deltaX > 3 || deltaY > 3) {\r\n            this.dragState.moved = true;\r\n        }\r\n\r\n        const newWindowX = this.dragState.initialWindowX + (e.screenX - this.dragState.initialMouseX);\r\n        const newWindowY = this.dragState.initialWindowY + (e.screenY - this.dragState.initialMouseY);\r\n\r\n        if (window.api?.apiKeyHeader) {\r\n            window.api.apiKeyHeader.moveHeaderTo(newWindowX, newWindowY);\r\n        }\r\n    }\r\n\r\n    handleMouseUp(e) {\r\n        if (!this.dragState) return;\r\n\r\n        const wasDragged = this.dragState.moved;\r\n\r\n        window.removeEventListener('mousemove', this.handleMouseMove);\r\n        this.dragState = null;\r\n\r\n        if (wasDragged) {\r\n            this.wasJustDragged = true;\r\n            setTimeout(() => {\r\n                this.wasJustDragged = false;\r\n            }, 200);\r\n        }\r\n    }\r\n\r\n    handleInput(e) {\r\n        this.apiKey = e.target.value;\r\n        this.clearMessages();\r\n        console.log('Input changed:', this.apiKey?.length || 0, 'chars');\r\n\r\n        this.requestUpdate();\r\n        this.updateComplete.then(() => {\r\n            const inputField = this.shadowRoot?.querySelector('.apikey-input');\r\n            if (inputField && this.isInputFocused) {\r\n                inputField.focus();\r\n            }\r\n        });\r\n    }\r\n\r\n    clearMessages() {\r\n        this.errorMessage = '';\r\n        this.successMessage = '';\r\n        this.messageTimestamp = 0;\r\n        this.llmError = '';\r\n        this.sttError = '';\r\n    }\r\n\r\n    handleProviderChange(e) {\r\n        this.selectedProvider = e.target.value;\r\n        this.clearMessages();\r\n        console.log('Provider changed to:', this.selectedProvider);\r\n        this.requestUpdate();\r\n    }\r\n\r\n    async handleLlmProviderChange(e, providerId) {\r\n        const newProvider = providerId || e.target.value;\r\n        if (newProvider === this.llmProvider) return;\r\n\r\n        // Cancel any active operations first\r\n        this._cancelAllActiveOperations();\r\n\r\n        this.llmProvider = newProvider;\r\n        this.errorMessage = '';\r\n        this.successMessage = '';\r\n\r\n        if (['openai', 'gemini'].includes(this.llmProvider)) {\r\n            this.sttProvider = this.llmProvider;\r\n        }\r\n\r\n        // Reset retry state\r\n        this.retryCount = 0;\r\n\r\n        if (this.llmProvider === 'ollama') {\r\n            console.log('[ApiKeyHeader] Ollama selected, initiating connection...');\r\n            await this._initializeOllamaConnection();\r\n            // Start health monitoring for Ollama\r\n            this._startHealthMonitoring();\r\n        } else {\r\n            this._updateConnectionState('idle', 'Non-Ollama provider selected');\r\n            // Stop health monitoring for non-Ollama providers\r\n            this._stopHealthMonitoring();\r\n        }\r\n\r\n        this.requestUpdate();\r\n    }\r\n\r\n    async _initializeOllamaConnection() {\r\n        try {\r\n            // Progressive connection attempt with exponential backoff\r\n            await this._attemptOllamaConnection();\r\n        } catch (error) {\r\n            console.error('[ApiKeyHeader] Initial Ollama connection failed:', error.message);\r\n\r\n            if (this.retryCount < this.maxRetries) {\r\n                const delay = this.baseRetryDelay * Math.pow(2, this.retryCount);\r\n                console.log(`[ApiKeyHeader] Retrying Ollama connection in ${delay}ms (attempt ${this.retryCount + 1}/${this.maxRetries})`);\r\n\r\n                this.retryCount++;\r\n\r\n                // Use proper Promise-based delay instead of setTimeout\r\n                await new Promise(resolve => {\r\n                    const retryTimeoutId = setTimeout(() => {\r\n                        this._initializeOllamaConnection();\r\n                        resolve();\r\n                    }, delay);\r\n\r\n                    // Store timeout for cleanup\r\n                    this.operationTimeouts.set(`retry_${this.retryCount}`, retryTimeoutId);\r\n                });\r\n            } else {\r\n                this._updateConnectionState('failed', `Connection failed after ${this.maxRetries} attempts`);\r\n            }\r\n        }\r\n    }\r\n\r\n    async _attemptOllamaConnection() {\r\n        await this.refreshOllamaStatus();\r\n    }\r\n\r\n    _cancelAllActiveOperations() {\r\n        console.log(`[ApiKeyHeader] Cancelling ${this.activeOperations.size} active operations and ${this.operationQueue.length} queued operations`);\r\n\r\n        // Cancel active operations\r\n        for (const [operationType, operation] of this.activeOperations) {\r\n            this._cancelOperation(operationType);\r\n        }\r\n\r\n        // Cancel queued operations\r\n        for (const queuedOp of this.operationQueue) {\r\n            queuedOp.reject(new Error(`Operation ${queuedOp.type} cancelled during cleanup`));\r\n        }\r\n        this.operationQueue.length = 0;\r\n\r\n        // Clean up all timeouts\r\n        for (const [timeoutId, timeout] of this.operationTimeouts) {\r\n            clearTimeout(timeout);\r\n        }\r\n        this.operationTimeouts.clear();\r\n    }\r\n\r\n    /**\r\n     * Get operation metrics for monitoring\r\n     */\r\n    getOperationMetrics() {\r\n        return {\r\n            ...this.operationMetrics,\r\n            activeOperations: this.activeOperations.size,\r\n            queuedOperations: this.operationQueue.length,\r\n            successRate:\r\n                this.operationMetrics.totalOperations > 0\r\n                    ? (this.operationMetrics.successfulOperations / this.operationMetrics.totalOperations) * 100\r\n                    : 0,\r\n        };\r\n    }\r\n\r\n    /**\r\n     * Adaptive backpressure based on system performance\r\n     */\r\n    _adjustBackpressureThresholds() {\r\n        const metrics = this.getOperationMetrics();\r\n\r\n        // Reduce concurrent operations if success rate is low\r\n        if (metrics.successRate < 70 && this.maxConcurrentOperations > 1) {\r\n            this.maxConcurrentOperations = Math.max(1, this.maxConcurrentOperations - 1);\r\n            console.log(\r\n                `[ApiKeyHeader] Reduced max concurrent operations to ${this.maxConcurrentOperations} (success rate: ${metrics.successRate.toFixed(1)}%)`\r\n            );\r\n        }\r\n\r\n        // Increase if performance is good\r\n        if (metrics.successRate > 90 && metrics.averageResponseTime < 3000 && this.maxConcurrentOperations < 3) {\r\n            this.maxConcurrentOperations++;\r\n            console.log(`[ApiKeyHeader] Increased max concurrent operations to ${this.maxConcurrentOperations}`);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Professional health monitoring system\r\n     */\r\n    _startHealthMonitoring() {\r\n        if (this.healthCheck.enabled) return;\r\n\r\n        this.healthCheck.enabled = true;\r\n        this.healthCheck.intervalId = setInterval(() => {\r\n            this._performHealthCheck();\r\n        }, this.healthCheck.intervalMs);\r\n\r\n        console.log(`[ApiKeyHeader] Health monitoring started (interval: ${this.healthCheck.intervalMs}ms)`);\r\n    }\r\n\r\n    _stopHealthMonitoring() {\r\n        if (!this.healthCheck.enabled) return;\r\n\r\n        this.healthCheck.enabled = false;\r\n        if (this.healthCheck.intervalId) {\r\n            clearInterval(this.healthCheck.intervalId);\r\n            this.healthCheck.intervalId = null;\r\n        }\r\n\r\n        console.log('[ApiKeyHeader] Health monitoring stopped');\r\n    }\r\n\r\n    async _performHealthCheck() {\r\n        // Only perform health check if Ollama is selected and we're in a stable state\r\n        if (this.llmProvider !== 'ollama' || this.connectionState === 'connecting') {\r\n            return;\r\n        }\r\n\r\n        const now = Date.now();\r\n        this.healthCheck.lastCheck = now;\r\n\r\n        try {\r\n            // Lightweight health check - just ping the service\r\n            const isHealthy = await this._executeOperation(\r\n                'health_check',\r\n                async () => {\r\n                    if (!window.api?.apiKeyHeader) return false;\r\n                    const result = await window.api.apiKeyHeader.getOllamaStatus();\r\n                    return result?.success && result?.running;\r\n                },\r\n                { timeout: 5000, priority: 'low' }\r\n            );\r\n\r\n            if (isHealthy) {\r\n                this.healthCheck.consecutiveFailures = 0;\r\n\r\n                // Update state if we were previously failed\r\n                if (this.connectionState === 'failed') {\r\n                    this._updateConnectionState('connected', 'Health check recovered');\r\n                }\r\n            } else {\r\n                this._handleHealthCheckFailure();\r\n            }\r\n\r\n            // Adjust thresholds based on performance\r\n            this._adjustBackpressureThresholds();\r\n        } catch (error) {\r\n            console.warn('[ApiKeyHeader] Health check failed:', error.message);\r\n            this._handleHealthCheckFailure();\r\n        }\r\n    }\r\n\r\n    _handleHealthCheckFailure() {\r\n        this.healthCheck.consecutiveFailures++;\r\n\r\n        if (this.healthCheck.consecutiveFailures >= this.healthCheck.maxFailures) {\r\n            console.warn(`[ApiKeyHeader] Health check failed ${this.healthCheck.consecutiveFailures} times, marking as disconnected`);\r\n            this._updateConnectionState('failed', 'Service health check failed');\r\n\r\n            // Increase health check frequency when having issues\r\n            this.healthCheck.intervalMs = Math.max(10000, this.healthCheck.intervalMs / 2);\r\n            this._restartHealthMonitoring();\r\n        }\r\n    }\r\n\r\n    _restartHealthMonitoring() {\r\n        this._stopHealthMonitoring();\r\n        this._startHealthMonitoring();\r\n    }\r\n\r\n    /**\r\n     * Get comprehensive health status\r\n     */\r\n    getHealthStatus() {\r\n        return {\r\n            connection: {\r\n                state: this.connectionState,\r\n                lastStateChange: this.lastStateChange,\r\n                timeSinceLastChange: Date.now() - this.lastStateChange,\r\n            },\r\n            operations: this.getOperationMetrics(),\r\n            health: {\r\n                enabled: this.healthCheck.enabled,\r\n                lastCheck: this.healthCheck.lastCheck,\r\n                timeSinceLastCheck: this.healthCheck.lastCheck > 0 ? Date.now() - this.healthCheck.lastCheck : null,\r\n                consecutiveFailures: this.healthCheck.consecutiveFailures,\r\n                intervalMs: this.healthCheck.intervalMs,\r\n            },\r\n            ollama: {\r\n                provider: this.llmProvider,\r\n                status: this.ollamaStatus,\r\n                selectedModel: this.selectedLlmModel,\r\n            },\r\n        };\r\n    }\r\n\r\n    async handleSttProviderChange(e, providerId) {\r\n        const newProvider = providerId || e.target.value;\r\n        if (newProvider === this.sttProvider) return;\r\n\r\n        this.sttProvider = newProvider;\r\n        this.errorMessage = '';\r\n        this.successMessage = '';\r\n\r\n        if (this.sttProvider === 'ollama') {\r\n            console.warn('[ApiKeyHeader] Ollama does not support STT yet. Please select Whisper or another provider.');\r\n            this.sttError = '*Ollama does not support STT yet. Please select Whisper or another STT provider.';\r\n            this.messageTimestamp = Date.now();\r\n\r\n            // Auto-select Whisper if available\r\n            const whisperProvider = this.providers.stt.find(p => p.id === 'whisper');\r\n            if (whisperProvider) {\r\n                this.sttProvider = 'whisper';\r\n                console.log('[ApiKeyHeader] Auto-selected Whisper for STT');\r\n            }\r\n        }\r\n\r\n        this.requestUpdate();\r\n    }\r\n\r\n    /**\r\n     * Professional operation management with backpressure control\r\n     */\r\n    async _executeOperation(operationType, operation, options = {}) {\r\n        const operationId = `${operationType}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;\r\n        const timeout = options.timeout || this.ipcTimeout;\r\n        const priority = options.priority || 'normal'; // high, normal, low\r\n\r\n        // Backpressure control\r\n        if (this.activeOperations.size >= this.maxConcurrentOperations) {\r\n            if (this.operationQueue.length >= this.maxQueueSize) {\r\n                throw new Error(`Operation queue full (${this.maxQueueSize}), rejecting ${operationType}`);\r\n            }\r\n\r\n            console.log(`[ApiKeyHeader] Queuing operation ${operationType} (${this.activeOperations.size} active)`);\r\n            return this._queueOperation(operationId, operationType, operation, options);\r\n        }\r\n\r\n        return this._executeImmediately(operationId, operationType, operation, timeout);\r\n    }\r\n\r\n    async _queueOperation(operationId, operationType, operation, options) {\r\n        return new Promise((resolve, reject) => {\r\n            const queuedOperation = {\r\n                id: operationId,\r\n                type: operationType,\r\n                operation,\r\n                options,\r\n                resolve,\r\n                reject,\r\n                queuedAt: Date.now(),\r\n                priority: options.priority || 'normal',\r\n            };\r\n\r\n            // Insert based on priority (high priority first)\r\n            if (options.priority === 'high') {\r\n                this.operationQueue.unshift(queuedOperation);\r\n            } else {\r\n                this.operationQueue.push(queuedOperation);\r\n            }\r\n\r\n            console.log(`[ApiKeyHeader] Queued ${operationType} (queue size: ${this.operationQueue.length})`);\r\n        });\r\n    }\r\n\r\n    async _executeImmediately(operationId, operationType, operation, timeout) {\r\n        const startTime = Date.now();\r\n        this.operationMetrics.totalOperations++;\r\n\r\n        // Check if similar operation is already running\r\n        if (this.activeOperations.has(operationType)) {\r\n            console.log(`[ApiKeyHeader] Operation ${operationType} already in progress, cancelling previous`);\r\n            this._cancelOperation(operationType);\r\n        }\r\n\r\n        // Create cancellation mechanism\r\n        const cancellationPromise = new Promise((_, reject) => {\r\n            const timeoutId = setTimeout(() => {\r\n                this.operationMetrics.timeouts++;\r\n                reject(new Error(`Operation ${operationType} timeout after ${timeout}ms`));\r\n            }, timeout);\r\n\r\n            this.operationTimeouts.set(operationId, timeoutId);\r\n        });\r\n\r\n        const operationPromise = Promise.race([operation(), cancellationPromise]);\r\n\r\n        this.activeOperations.set(operationType, {\r\n            id: operationId,\r\n            promise: operationPromise,\r\n            startTime,\r\n        });\r\n\r\n        try {\r\n            const result = await operationPromise;\r\n            this._recordOperationSuccess(startTime);\r\n            return result;\r\n        } catch (error) {\r\n            this._recordOperationFailure(error, operationType);\r\n            throw error;\r\n        } finally {\r\n            this._cleanupOperation(operationId, operationType);\r\n            this._processQueue();\r\n        }\r\n    }\r\n\r\n    _recordOperationSuccess(startTime) {\r\n        this.operationMetrics.successfulOperations++;\r\n        const responseTime = Date.now() - startTime;\r\n        this._updateAverageResponseTime(responseTime);\r\n    }\r\n\r\n    _recordOperationFailure(error, operationType) {\r\n        this.operationMetrics.failedOperations++;\r\n\r\n        if (error.message.includes('timeout')) {\r\n            console.error(`[ApiKeyHeader] Operation ${operationType} timed out`);\r\n            this._updateConnectionState('failed', `Timeout: ${error.message}`);\r\n        }\r\n    }\r\n\r\n    _updateAverageResponseTime(responseTime) {\r\n        const totalOps = this.operationMetrics.successfulOperations;\r\n        this.operationMetrics.averageResponseTime = (this.operationMetrics.averageResponseTime * (totalOps - 1) + responseTime) / totalOps;\r\n    }\r\n\r\n    async _processQueue() {\r\n        if (this.operationQueue.length === 0 || this.activeOperations.size >= this.maxConcurrentOperations) {\r\n            return;\r\n        }\r\n\r\n        const queuedOp = this.operationQueue.shift();\r\n        if (!queuedOp) return;\r\n\r\n        const queueTime = Date.now() - queuedOp.queuedAt;\r\n        console.log(`[ApiKeyHeader] Processing queued operation ${queuedOp.type} (waited ${queueTime}ms)`);\r\n\r\n        try {\r\n            const result = await this._executeImmediately(\r\n                queuedOp.id,\r\n                queuedOp.type,\r\n                queuedOp.operation,\r\n                queuedOp.options.timeout || this.ipcTimeout\r\n            );\r\n            queuedOp.resolve(result);\r\n        } catch (error) {\r\n            queuedOp.reject(error);\r\n        }\r\n    }\r\n\r\n    _cancelOperation(operationType) {\r\n        const operation = this.activeOperations.get(operationType);\r\n        if (operation) {\r\n            this._cleanupOperation(operation.id, operationType);\r\n            console.log(`[ApiKeyHeader] Cancelled operation: ${operationType}`);\r\n        }\r\n    }\r\n\r\n    _cleanupOperation(operationId, operationType) {\r\n        if (this.operationTimeouts.has(operationId)) {\r\n            clearTimeout(this.operationTimeouts.get(operationId));\r\n            this.operationTimeouts.delete(operationId);\r\n        }\r\n        this.activeOperations.delete(operationType);\r\n    }\r\n\r\n    _updateConnectionState(newState, reason = '') {\r\n        if (this.connectionState !== newState) {\r\n            console.log(`[ApiKeyHeader] Connection state: ${this.connectionState} -> ${newState} (${reason})`);\r\n            this.connectionState = newState;\r\n            this.lastStateChange = Date.now();\r\n\r\n            // Update UI based on state\r\n            this._handleStateChange(newState, reason);\r\n        }\r\n    }\r\n\r\n    _handleStateChange(state, reason) {\r\n        switch (state) {\r\n            case 'connecting':\r\n                this.installingModel = 'Connecting to Ollama...';\r\n                this.installProgress = 10;\r\n                break;\r\n            case 'failed':\r\n                this.errorMessage = reason || 'Connection failed';\r\n                this.installingModel = null;\r\n                this.installProgress = 0;\r\n                this.messageTimestamp = Date.now();\r\n                break;\r\n            case 'connected':\r\n                this.installingModel = null;\r\n                this.installProgress = 0;\r\n                break;\r\n            case 'disconnected':\r\n                this.ollamaStatus = { installed: false, running: false };\r\n                break;\r\n        }\r\n        this.requestUpdate();\r\n    }\r\n\r\n    async refreshOllamaStatus() {\r\n        if (!window.api?.apiKeyHeader) return;\r\n\r\n        try {\r\n            this._updateConnectionState('connecting', 'Checking Ollama status');\r\n\r\n            const result = await this._executeOperation('ollama_status', async () => {\r\n                return await window.api.apiKeyHeader.getOllamaStatus();\r\n            });\r\n\r\n            if (result?.success) {\r\n                this.ollamaStatus = {\r\n                    installed: result.installed,\r\n                    running: result.running,\r\n                };\r\n\r\n                this._updateConnectionState('connected', 'Status updated successfully');\r\n\r\n                // Load model suggestions if Ollama is running\r\n                if (result.running) {\r\n                    await this.loadModelSuggestions();\r\n                }\r\n            } else {\r\n                this._updateConnectionState('failed', result?.error || 'Status check failed');\r\n            }\r\n        } catch (error) {\r\n            console.error('[ApiKeyHeader] Failed to refresh Ollama status:', error.message);\r\n            this._updateConnectionState('failed', error.message);\r\n        }\r\n    }\r\n\r\n    async loadModelSuggestions() {\r\n        if (!window.api?.apiKeyHeader) return;\r\n\r\n        try {\r\n            const result = await this._executeOperation('model_suggestions', async () => {\r\n                return await window.api.apiKeyHeader.getModelSuggestions();\r\n            });\r\n\r\n            if (result?.success) {\r\n                this.modelSuggestions = result.suggestions || [];\r\n\r\n                // 기본 모델 선택 (설치된 모델 중 첫 번째)\r\n                if (!this.selectedLlmModel && this.modelSuggestions.length > 0) {\r\n                    const installedModel = this.modelSuggestions.find(m => m.status === 'installed');\r\n                    if (installedModel) {\r\n                        this.selectedLlmModel = installedModel.name;\r\n                    }\r\n                }\r\n                this.requestUpdate();\r\n            } else {\r\n                console.warn('[ApiKeyHeader] Model suggestions request unsuccessful:', result?.error);\r\n            }\r\n        } catch (error) {\r\n            console.error('[ApiKeyHeader] Failed to load model suggestions:', error.message);\r\n        }\r\n    }\r\n\r\n    async ensureOllamaReady() {\r\n        if (!window.api?.apiKeyHeader) return false;\r\n\r\n        try {\r\n            this._updateConnectionState('connecting', 'Ensuring Ollama is ready');\r\n\r\n            const result = await this._executeOperation(\r\n                'ollama_ensure_ready',\r\n                async () => {\r\n                    return await window.api.apiKeyHeader.ensureOllamaReady();\r\n                },\r\n                { timeout: this.operationTimeout }\r\n            );\r\n\r\n            if (result?.success) {\r\n                await this.refreshOllamaStatus();\r\n                this._updateConnectionState('connected', 'Ollama ready');\r\n                return true;\r\n            } else {\r\n                const errorMsg = `Failed to setup Ollama: ${result?.error || 'Unknown error'}`;\r\n                this._updateConnectionState('failed', errorMsg);\r\n                return false;\r\n            }\r\n        } catch (error) {\r\n            console.error('[ApiKeyHeader] Failed to ensure Ollama ready:', error.message);\r\n            this._updateConnectionState('failed', `Error setting up Ollama: ${error.message}`);\r\n            return false;\r\n        }\r\n    }\r\n\r\n    async ensureOllamaReadyWithUI() {\r\n        if (!window.api?.apiKeyHeader) return false;\r\n\r\n        this.installingModel = 'Setting up Ollama';\r\n        this.installProgress = 0;\r\n        this.clearMessages();\r\n        this.requestUpdate();\r\n\r\n        const progressHandler = (event, data) => {\r\n            // 통합 LocalAI 이벤트에서 Ollama 진행률만 처리\r\n            if (data.service !== 'ollama') return;\r\n            \r\n            let baseProgress = 0;\r\n            let stageTotal = 0;\r\n\r\n            switch (data.stage) {\r\n                case 'downloading':\r\n                    baseProgress = 0;\r\n                    stageTotal = 70;\r\n                    break;\r\n                case 'mounting':\r\n                    baseProgress = 70;\r\n                    stageTotal = 10;\r\n                    break;\r\n                case 'installing':\r\n                    baseProgress = 80;\r\n                    stageTotal = 10;\r\n                    break;\r\n                case 'linking':\r\n                    baseProgress = 90;\r\n                    stageTotal = 5;\r\n                    break;\r\n                case 'cleanup':\r\n                    baseProgress = 95;\r\n                    stageTotal = 3;\r\n                    break;\r\n                case 'starting':\r\n                    baseProgress = 98;\r\n                    stageTotal = 2;\r\n                    break;\r\n            }\r\n\r\n            const overallProgress = baseProgress + (data.progress / 100) * stageTotal;\r\n\r\n            this.installingModel = data.message;\r\n            this.installProgress = Math.round(overallProgress);\r\n            this.requestUpdate();\r\n        };\r\n\r\n        let operationCompleted = false;\r\n        const completionTimeout = setTimeout(async () => {\r\n            if (!operationCompleted) {\r\n                console.log('[ApiKeyHeader] Operation timeout, checking status manually...');\r\n                await this._handleOllamaSetupCompletion(true);\r\n            }\r\n        }, 15000); // 15 second timeout\r\n\r\n        const completionHandler = async (event, data) => {\r\n            // 통합 LocalAI 이벤트에서 Ollama 완료만 처리\r\n            if (data.service !== 'ollama') return;\r\n            if (operationCompleted) return;\r\n            operationCompleted = true;\r\n            clearTimeout(completionTimeout);\r\n\r\n            window.api.apiKeyHeader.removeOnLocalAIProgress(progressHandler);\r\n            // installation-complete 이벤트는 성공을 의미\r\n            await this._handleOllamaSetupCompletion(true);\r\n        };\r\n\r\n        // 통합 LocalAI 이벤트 사용\r\n        window.api.apiKeyHeader.onLocalAIComplete(completionHandler);\r\n        window.api.apiKeyHeader.onLocalAIProgress(progressHandler);\r\n\r\n        try {\r\n            let result;\r\n            if (!this.ollamaStatus.installed) {\r\n                console.log('[ApiKeyHeader] Ollama not installed. Starting installation.');\r\n                result = await window.api.apiKeyHeader.installOllama();\r\n            } else {\r\n                console.log('[ApiKeyHeader] Ollama installed. Starting service.');\r\n                result = await window.api.apiKeyHeader.startOllamaService();\r\n            }\r\n\r\n            // If IPC call succeeds but no event received, handle completion manually\r\n            if (result?.success && !operationCompleted) {\r\n                setTimeout(async () => {\r\n                    if (!operationCompleted) {\r\n                        operationCompleted = true;\r\n                        clearTimeout(completionTimeout);\r\n                        await this._handleOllamaSetupCompletion(true);\r\n                    }\r\n                }, 2000);\r\n            }\r\n        } catch (error) {\r\n            operationCompleted = true;\r\n            clearTimeout(completionTimeout);\r\n            console.error('[ApiKeyHeader] Ollama setup failed:', error);\r\n            window.api.apiKeyHeader.removeOnLocalAIProgress(progressHandler);\r\n            window.api.apiKeyHeader.removeOnLocalAIComplete(completionHandler);\r\n            await this._handleOllamaSetupCompletion(false, error.message);\r\n        }\r\n    }\r\n\r\n    async _handleOllamaSetupCompletion(success, errorMessage = null) {\r\n        this.installingModel = null;\r\n        this.installProgress = 0;\r\n\r\n        if (success) {\r\n            await this.refreshOllamaStatus();\r\n            this.successMessage = '✓ Ollama is ready!';\r\n        } else {\r\n            this.llmError = `*Setup failed: ${errorMessage || 'Unknown error'}`;\r\n        }\r\n        this.messageTimestamp = Date.now();\r\n        this.requestUpdate();\r\n    }\r\n\r\n    async handleModelInput(e) {\r\n        const modelName = e.target.value.trim();\r\n        this.selectedLlmModel = modelName;\r\n        this.clearMessages();\r\n\r\n        // Save to user history if it's a valid model name\r\n        if (modelName && modelName.length > 2) {\r\n            this.saveToUserHistory(modelName);\r\n        }\r\n\r\n        this.requestUpdate();\r\n    }\r\n\r\n    async handleModelKeyPress(e) {\r\n        if (e.key === 'Enter' && this.selectedLlmModel?.trim()) {\r\n            e.preventDefault();\r\n            console.log(`[ApiKeyHeader] Enter pressed, installing model: ${this.selectedLlmModel}`);\r\n\r\n            // Check if Ollama is ready first\r\n            const ollamaReady = await this.ensureOllamaReady();\r\n            if (!ollamaReady) {\r\n                this.llmError = '*Failed to setup Ollama';\r\n                this.messageTimestamp = Date.now();\r\n                this.requestUpdate();\r\n                return;\r\n            }\r\n\r\n            // Install the model\r\n            await this.installModel(this.selectedLlmModel);\r\n        }\r\n    }\r\n\r\n    loadUserModelHistory() {\r\n        try {\r\n            const saved = localStorage.getItem('ollama-model-history');\r\n            if (saved) {\r\n                this.userModelHistory = JSON.parse(saved);\r\n            }\r\n        } catch (error) {\r\n            console.error('[ApiKeyHeader] Failed to load model history:', error);\r\n            this.userModelHistory = [];\r\n        }\r\n    }\r\n\r\n    saveToUserHistory(modelName) {\r\n        if (!modelName || !modelName.trim()) return;\r\n\r\n        // Remove if already exists (to move to front)\r\n        this.userModelHistory = this.userModelHistory.filter(m => m !== modelName);\r\n\r\n        // Add to front\r\n        this.userModelHistory.unshift(modelName);\r\n\r\n        // Keep only last 20 entries\r\n        this.userModelHistory = this.userModelHistory.slice(0, 20);\r\n\r\n        // Save to localStorage\r\n        try {\r\n            localStorage.setItem('ollama-model-history', JSON.stringify(this.userModelHistory));\r\n        } catch (error) {\r\n            console.error('[ApiKeyHeader] Failed to save model history:', error);\r\n        }\r\n    }\r\n\r\n    getCombinedModelSuggestions() {\r\n        const combined = [];\r\n\r\n        // Add installed models first (from Ollama CLI)\r\n        for (const model of this.modelSuggestions) {\r\n            combined.push({\r\n                name: model.name,\r\n                status: 'installed',\r\n                size: model.size || 'Unknown',\r\n                source: 'installed',\r\n            });\r\n        }\r\n\r\n        // Add user history models that aren't already installed\r\n        const installedNames = this.modelSuggestions.map(m => m.name);\r\n        for (const modelName of this.userModelHistory) {\r\n            if (!installedNames.includes(modelName)) {\r\n                combined.push({\r\n                    name: modelName,\r\n                    status: 'history',\r\n                    size: 'Unknown',\r\n                    source: 'history',\r\n                });\r\n            }\r\n        }\r\n\r\n        return combined;\r\n    }\r\n\r\n    async installModel(modelName) {\r\n        if (!modelName?.trim()) {\r\n            throw new Error('Invalid model name');\r\n        }\r\n\r\n        this.installingModel = modelName;\r\n        this.installProgress = 0;\r\n        this.clearMessages();\r\n        this.requestUpdate();\r\n\r\n        if (!window.api?.apiKeyHeader) return;\r\n        let progressHandler = null;\r\n\r\n        try {\r\n            console.log(`[ApiKeyHeader] Installing model via Ollama REST API: ${modelName}`);\r\n\r\n            // Create robust progress handler with timeout protection\r\n            progressHandler = (event, data) => {\r\n                if (data.service === 'ollama' && data.model === modelName && !this._isOperationCancelled(modelName)) {\r\n                    const progress = Math.round(Math.max(0, Math.min(100, data.progress || 0)));\r\n\r\n                    if (progress !== this.installProgress) {\r\n                        this.installProgress = progress;\r\n                        console.log(`[ApiKeyHeader] API Progress: ${progress}% for ${modelName} (${data.status || 'downloading'})`);\r\n                        this.requestUpdate();\r\n                    }\r\n                }\r\n            };\r\n\r\n            // Set up progress tracking - 통합 LocalAI 이벤트 사용\r\n            window.api.apiKeyHeader.onLocalAIProgress(progressHandler);\r\n\r\n            // Execute the model pull with timeout\r\n            const installPromise = window.api.apiKeyHeader.pullOllamaModel(modelName);\r\n            const timeoutPromise = new Promise((_, reject) => setTimeout(() => reject(new Error('Installation timeout after 10 minutes')), 600000));\r\n\r\n            const result = await Promise.race([installPromise, timeoutPromise]);\r\n\r\n            if (result.success) {\r\n                console.log(`[ApiKeyHeader] Model ${modelName} installed successfully via API`);\r\n                this.installProgress = 100;\r\n                this.requestUpdate();\r\n\r\n                // Brief pause to show completion\r\n                await new Promise(resolve => setTimeout(resolve, 300));\r\n\r\n                // Refresh status and show success\r\n                await this.refreshOllamaStatus();\r\n                this.successMessage = `✓ ${modelName} ready`;\r\n                this.messageTimestamp = Date.now();\r\n            } else {\r\n                throw new Error(result.error || 'Installation failed');\r\n            }\r\n        } catch (error) {\r\n            console.error(`[ApiKeyHeader] Model installation failed:`, error);\r\n            this.llmError = `*Failed: ${error.message}`;\r\n            this.messageTimestamp = Date.now();\r\n        } finally {\r\n            // Comprehensive cleanup\r\n            if (progressHandler) {\r\n                window.api.apiKeyHeader.removeOnLocalAIProgress(progressHandler);\r\n            }\r\n\r\n            this.installingModel = null;\r\n            this.installProgress = 0;\r\n            this.requestUpdate();\r\n        }\r\n    }\r\n\r\n    _isOperationCancelled(modelName) {\r\n        return !this.installingModel || this.installingModel !== modelName;\r\n    }\r\n\r\n    async downloadWhisperModel(modelId) {\r\n        if (!modelId?.trim()) {\r\n            console.warn('[ApiKeyHeader] Invalid Whisper model ID');\r\n            return;\r\n        }\r\n\r\n        console.log(`[ApiKeyHeader] Starting Whisper model download: ${modelId}`);\r\n\r\n        // Mark as installing\r\n        this.whisperInstallingModels = { ...this.whisperInstallingModels, [modelId]: 0 };\r\n        this.clearMessages();\r\n        this.requestUpdate();\r\n\r\n        if (!window.api?.apiKeyHeader) return;\r\n        let progressHandler = null;\r\n\r\n        try {\r\n            // Set up robust progress listener - 통합 LocalAI 이벤트 사용\r\n            progressHandler = (event, data) => {\r\n                if (data.service === 'whisper' && data.model === modelId) {\r\n                    const cleanProgress = Math.round(Math.max(0, Math.min(100, data.progress || 0)));\r\n                    this.whisperInstallingModels = { ...this.whisperInstallingModels, [modelId]: cleanProgress };\r\n                    console.log(`[ApiKeyHeader] Whisper download progress: ${cleanProgress}% for ${modelId}`);\r\n                    this.requestUpdate();\r\n                }\r\n            };\r\n\r\n            window.api.apiKeyHeader.onLocalAIProgress(progressHandler);\r\n\r\n            // Start download with timeout protection\r\n            const downloadPromise = window.api.apiKeyHeader.downloadWhisperModel(modelId);\r\n            const timeoutPromise = new Promise((_, reject) => setTimeout(() => reject(new Error('Download timeout after 10 minutes')), 600000));\r\n\r\n            const result = await Promise.race([downloadPromise, timeoutPromise]);\r\n\r\n            if (result?.success) {\r\n                this.successMessage = `✓ ${modelId} downloaded successfully`;\r\n                this.messageTimestamp = Date.now();\r\n                console.log(`[ApiKeyHeader] Whisper model ${modelId} downloaded successfully`);\r\n\r\n                // Auto-select the downloaded model\r\n                this.selectedSttModel = modelId;\r\n            } else {\r\n                this.sttError = `*Failed to download ${modelId}: ${result?.error || 'Unknown error'}`;\r\n                this.messageTimestamp = Date.now();\r\n                console.error(`[ApiKeyHeader] Whisper download failed:`, result?.error);\r\n            }\r\n        } catch (error) {\r\n            console.error(`[ApiKeyHeader] Error downloading Whisper model ${modelId}:`, error);\r\n            this.sttError = `*Error downloading ${modelId}: ${error.message}`;\r\n            this.messageTimestamp = Date.now();\r\n        } finally {\r\n            // Cleanup\r\n            if (progressHandler) {\r\n                window.api.apiKeyHeader.removeOnLocalAIProgress(progressHandler);\r\n            }\r\n            delete this.whisperInstallingModels[modelId];\r\n            this.requestUpdate();\r\n        }\r\n    }\r\n\r\n    handlePaste(e) {\r\n        e.preventDefault();\r\n        this.clearMessages();\r\n        const clipboardText = (e.clipboardData || window.clipboardData).getData('text');\r\n        console.log('Paste event detected:', clipboardText?.substring(0, 10) + '...');\r\n\r\n        if (clipboardText) {\r\n            this.apiKey = clipboardText.trim();\r\n\r\n            const inputElement = e.target;\r\n            inputElement.value = this.apiKey;\r\n        }\r\n\r\n        this.requestUpdate();\r\n        this.updateComplete.then(() => {\r\n            const inputField = this.shadowRoot?.querySelector('.apikey-input');\r\n            if (inputField) {\r\n                inputField.focus();\r\n                inputField.setSelectionRange(inputField.value.length, inputField.value.length);\r\n            }\r\n        });\r\n    }\r\n\r\n    handleKeyPress(e) {\r\n        if (e.key === 'Enter') {\r\n            e.preventDefault();\r\n            this.handleSubmit();\r\n        }\r\n    }\r\n\r\n    //////// after_modelStateService ////////\r\n    async handleSttModelChange(e) {\r\n        const modelId = e.target.value;\r\n        this.selectedSttModel = modelId;\r\n\r\n        if (modelId && this.sttProvider === 'whisper') {\r\n            // Check if model needs to be downloaded\r\n            const isInstalling = this.whisperInstallingModels[modelId] !== undefined;\r\n            if (!isInstalling) {\r\n                console.log(`[ApiKeyHeader] Auto-installing Whisper model: ${modelId}`);\r\n                await this.downloadWhisperModel(modelId);\r\n            }\r\n        }\r\n\r\n        this.requestUpdate();\r\n    }\r\n\r\n    async handleSubmit() {\r\n        console.log('[ApiKeyHeader] handleSubmit: Submitting...');\r\n\r\n        this.isLoading = true;\r\n        this.clearMessages();\r\n        this.requestUpdate();\r\n\r\n        if (!window.api?.apiKeyHeader) {\r\n            this.isLoading = false;\r\n            this.llmError = '*API bridge not available';\r\n            this.requestUpdate();\r\n            return;\r\n        }\r\n\r\n        try {\r\n            // Handle LLM provider\r\n            let llmResult;\r\n            if (this.llmProvider === 'ollama') {\r\n                // For Ollama ensure it's ready and validate model selection\r\n                if (!this.selectedLlmModel?.trim()) {\r\n                    throw new Error('Please enter an Ollama model name');\r\n                }\r\n\r\n                const ollamaReady = await this.ensureOllamaReady();\r\n                if (!ollamaReady) {\r\n                    throw new Error('Failed to setup Ollama');\r\n                }\r\n\r\n                // Check if model is installed, if not install it\r\n                const selectedModel = this.getCombinedModelSuggestions().find(m => m.name === this.selectedLlmModel);\r\n                if (!selectedModel || selectedModel.status !== 'installed') {\r\n                    console.log(`[ApiKeyHeader] Installing model ${this.selectedLlmModel}...`);\r\n                    await this.installModel(this.selectedLlmModel);\r\n                }\r\n\r\n                // Validate Ollama is working\r\n                llmResult = await window.api.apiKeyHeader.validateKey({\r\n                    provider: 'ollama',\r\n                    key: 'local',\r\n                });\r\n\r\n                if (llmResult.success) {\r\n                    // Set the selected model\r\n                    await window.api.apiKeyHeader.setSelectedModel({\r\n                        type: 'llm',\r\n                        modelId: this.selectedLlmModel,\r\n                    });\r\n                }\r\n            } else {\r\n                // For other providers, validate API key\r\n                if (!this.llmApiKey.trim()) {\r\n                    throw new Error('Please enter LLM API key');\r\n                }\r\n\r\n                llmResult = await window.api.apiKeyHeader.validateKey({\r\n                    provider: this.llmProvider,\r\n                    key: this.llmApiKey.trim(),\r\n                });\r\n\r\n                if (llmResult.success) {\r\n                    const config = await window.api.apiKeyHeader.getProviderConfig();\r\n                    const providerConfig = config[this.llmProvider];\r\n                    if (providerConfig && providerConfig.llmModels.length > 0) {\r\n                        await window.api.apiKeyHeader.setSelectedModel({\r\n                            type: 'llm',\r\n                            modelId: providerConfig.llmModels[0].id,\r\n                        });\r\n                    }\r\n                }\r\n            }\r\n\r\n            // Handle STT provider\r\n            let sttResult;\r\n            if (this.sttProvider === 'ollama') {\r\n                // Ollama doesn't support STT yet, so skip or use same as LLM validation\r\n                sttResult = { success: true };\r\n            } else if (this.sttProvider === 'whisper') {\r\n                // For Whisper, just validate it's enabled (model download already handled in handleSttModelChange)\r\n                sttResult = await window.api.apiKeyHeader.validateKey({\r\n                    provider: 'whisper',\r\n                    key: 'local',\r\n                });\r\n\r\n                if (sttResult.success && this.selectedSttModel) {\r\n                    // Set the selected model\r\n                    await window.api.apiKeyHeader.setSelectedModel({\r\n                        type: 'stt',\r\n                        modelId: this.selectedSttModel,\r\n                    });\r\n                }\r\n            } else {\r\n                // For other providers, validate API key\r\n                if (!this.sttApiKey.trim()) {\r\n                    throw new Error('Please enter STT API key');\r\n                }\r\n\r\n                sttResult = await window.api.apiKeyHeader.validateKey({\r\n                    provider: this.sttProvider,\r\n                    key: this.sttApiKey.trim(),\r\n                });\r\n\r\n                if (sttResult.success) {\r\n                    const config = await window.api.apiKeyHeader.getProviderConfig();\r\n                    const providerConfig = config[this.sttProvider];\r\n                    if (providerConfig && providerConfig.sttModels.length > 0) {\r\n                        await window.api.apiKeyHeader.setSelectedModel({\r\n                            type: 'stt',\r\n                            modelId: providerConfig.sttModels[0].id,\r\n                        });\r\n                    }\r\n                }\r\n            }\r\n\r\n            if (llmResult.success && sttResult.success) {\r\n                console.log('[ApiKeyHeader] handleSubmit: Validation successful.');\r\n                \r\n                // Force refresh the model state to ensure areProvidersConfigured returns true\r\n                setTimeout(async () => {\r\n                    const isConfigured = await window.api.apiKeyHeader.areProvidersConfigured();\r\n                    console.log('[ApiKeyHeader] Post-validation providers configured check:', isConfigured);\r\n                    \r\n                    if (isConfigured) {\r\n                        this.startSlideOutAnimation();\r\n                    } else {\r\n                        console.error('[ApiKeyHeader] Providers still not configured after successful validation');\r\n                        this.llmError = '*Configuration error. Please try again.';\r\n                        this.isLoading = false;\r\n                        this.requestUpdate();\r\n                    }\r\n                }, 100);\r\n            } else {\r\n                this.llmError = !llmResult.success ? `*${llmResult.error || 'Invalid API Key'}` : '';\r\n                this.sttError = !sttResult.success ? `*${sttResult.error || 'Invalid'}` : '';\r\n                this.errorMessage = ''; // Do not use the general error message for this\r\n                this.messageTimestamp = Date.now();\r\n            }\r\n        } catch (error) {\r\n            console.error('[ApiKeyHeader] handleSubmit: Error:', error);\r\n            this.llmError = `*${error.message}`;\r\n            this.messageTimestamp = Date.now();\r\n        }\r\n\r\n        this.isLoading = false;\r\n        this.requestUpdate();\r\n    }\r\n    //////// after_modelStateService ////////\r\n\r\n\r\n    ////TODO: 뭔가 넘어가는 애니메이션 로직에 문제가 있음\r\n    startSlideOutAnimation() {\r\n        console.log('[ApiKeyHeader] startSlideOutAnimation: Starting slide out animation.');\r\n        this.classList.add('sliding-out');\r\n        \r\n        // Fallback: if animation doesn't trigger animationend event, force transition\r\n        setTimeout(() => {\r\n            if (this.classList.contains('sliding-out')) {\r\n                console.log('[ApiKeyHeader] Animation fallback triggered - forcing transition');\r\n                this.handleAnimationEnd({ target: this, animationName: 'slideOut' });\r\n            }\r\n        }, 1); // Wait a bit longer than animation duration\r\n    }\r\n\r\n    handleClose() {\r\n        if (window.api?.common) {\r\n            window.api.common.quitApplication();\r\n        }\r\n    }\r\n\r\n    //////// after_modelStateService ////////\r\n    handleAnimationEnd(e) {\r\n        if (e.target !== this || !this.classList.contains('sliding-out')) return;\r\n        this.classList.remove('sliding-out');\r\n        this.classList.add('hidden');\r\n\r\n        console.log('[ApiKeyHeader] handleAnimationEnd: Animation completed, transitioning to next state...');\r\n\r\n        if (!window.api?.common) {\r\n            console.error('[ApiKeyHeader] handleAnimationEnd: window.api.common not available');\r\n            return;\r\n        }\r\n\r\n        if (!this.stateUpdateCallback) {\r\n            console.error('[ApiKeyHeader] handleAnimationEnd: stateUpdateCallback not set! This will prevent transition to main window.');\r\n            return;\r\n        }\r\n\r\n        window.api.common\r\n            .getCurrentUser()\r\n            .then(userState => {\r\n                console.log('[ApiKeyHeader] handleAnimationEnd: User state retrieved:', userState);\r\n\r\n                // Additional validation for local providers\r\n                return window.api.apiKeyHeader.areProvidersConfigured().then(isConfigured => {\r\n                    console.log('[ApiKeyHeader] handleAnimationEnd: Providers configured check:', isConfigured);\r\n\r\n                    if (!isConfigured) {\r\n                        console.warn('[ApiKeyHeader] handleAnimationEnd: Providers still not configured, may return to ApiKey screen');\r\n                    }\r\n\r\n                    // Call the state update callback\r\n                    this.stateUpdateCallback(userState);\r\n                });\r\n            })\r\n            .catch(error => {\r\n                console.error('[ApiKeyHeader] handleAnimationEnd: Error during state transition:', error);\r\n\r\n                // Fallback: try to call callback with minimal state\r\n                if (this.stateUpdateCallback) {\r\n                    console.log('[ApiKeyHeader] handleAnimationEnd: Attempting fallback state transition...');\r\n                    this.stateUpdateCallback({ isLoggedIn: false });\r\n                }\r\n            });\r\n    }\r\n    //////// after_modelStateService ////////\r\n\r\n    connectedCallback() {\r\n        super.connectedCallback();\r\n        this.addEventListener('animationend', this.handleAnimationEnd);\r\n    }\r\n\r\n    handleMessageFadeEnd(e) {\r\n        if (e.animationName === 'fadeOut') {\r\n            // Clear the message that finished fading\r\n            if (e.target.classList.contains('error-message')) {\r\n                this.errorMessage = '';\r\n            } else if (e.target.classList.contains('success-message')) {\r\n                this.successMessage = '';\r\n            }\r\n            this.messageTimestamp = 0;\r\n            this.requestUpdate();\r\n        }\r\n    }\r\n\r\n    disconnectedCallback() {\r\n        super.disconnectedCallback();\r\n        this.removeEventListener('animationend', this.handleAnimationEnd);\r\n\r\n        // Professional cleanup of all resources\r\n        this._performCompleteCleanup();\r\n    }\r\n\r\n    _performCompleteCleanup() {\r\n        console.log('[ApiKeyHeader] Performing complete cleanup');\r\n\r\n        // Stop health monitoring\r\n        this._stopHealthMonitoring();\r\n\r\n        // Cancel all active operations\r\n        this._cancelAllActiveOperations();\r\n\r\n        // Cancel any ongoing installations when component is destroyed\r\n        if (this.installingModel) {\r\n            this.progressTracker.cancelInstallation(this.installingModel);\r\n        }\r\n\r\n        // Cleanup event listeners\r\n        if (window.api?.apiKeyHeader) {\r\n            window.api.apiKeyHeader.removeAllListeners();\r\n        }\r\n\r\n        // Cancel any ongoing downloads\r\n        const downloadingModels = Object.keys(this.whisperInstallingModels);\r\n        if (downloadingModels.length > 0) {\r\n            console.log(`[ApiKeyHeader] Cancelling ${downloadingModels.length} ongoing Whisper downloads`);\r\n            downloadingModels.forEach(modelId => {\r\n                delete this.whisperInstallingModels[modelId];\r\n            });\r\n        }\r\n\r\n        // Reset state\r\n        this.connectionState = 'disconnected';\r\n        this.retryCount = 0;\r\n\r\n        console.log('[ApiKeyHeader] Cleanup completed');\r\n    }\r\n\r\n    /**\r\n     * State machine-based Ollama UI rendering\r\n     */\r\n    _renderOllamaStateUI() {\r\n        const state = this._getOllamaUIState();\r\n\r\n        switch (state.type) {\r\n            case 'connecting':\r\n                return this._renderConnectingState(state);\r\n            case 'install_required':\r\n                return this._renderInstallRequiredState();\r\n            case 'start_required':\r\n                return this._renderStartRequiredState();\r\n            case 'ready':\r\n                return this._renderReadyState();\r\n            case 'failed':\r\n                return this._renderFailedState(state);\r\n            case 'installing':\r\n                return this._renderInstallingState(state);\r\n            default:\r\n                return this._renderUnknownState();\r\n        }\r\n    }\r\n\r\n    _getOllamaUIState() {\r\n        // State determination logic\r\n        if (this.connectionState === 'connecting') {\r\n            return { type: 'connecting', message: this.installingModel || 'Connecting to Ollama...' };\r\n        }\r\n\r\n        if (this.connectionState === 'failed') {\r\n            return { type: 'failed', message: this.errorMessage };\r\n        }\r\n\r\n        if (this.installingModel && this.installingModel.includes('Ollama')) {\r\n            return { type: 'installing', progress: this.installProgress };\r\n        }\r\n\r\n        if (!this.ollamaStatus.installed) {\r\n            return { type: 'install_required' };\r\n        }\r\n\r\n        if (!this.ollamaStatus.running) {\r\n            return { type: 'start_required' };\r\n        }\r\n\r\n        return { type: 'ready' };\r\n    }\r\n\r\n    _renderConnectingState(state) {\r\n        return html`\r\n            <div style=\"margin-top: 3px; display: flex; align-items: center; gap: 6px;\">\r\n                <div style=\"height: 1px; background: rgba(255,255,255,0.3); border-radius: 0.5px; overflow: hidden; flex: 1;\">\r\n                    <div style=\"height: 100%; background: rgba(0,122,255,1); width: ${this.installProgress}%; transition: width 0.1s ease;\"></div>\r\n                </div>\r\n                <div style=\"font-size: 8px; color: rgba(255,255,255,0.8); font-weight: 600; min-width: 24px; text-align: right;\">\r\n                    ${this.installProgress}%\r\n                </div>\r\n            </div>\r\n        `;\r\n    }\r\n\r\n    _renderInstallRequiredState() {\r\n        return html` <button class=\"ollama-action-button install\" @click=${this.ensureOllamaReadyWithUI}>Install Ollama</button> `;\r\n    }\r\n\r\n    _renderStartRequiredState() {\r\n        return html` <button class=\"ollama-action-button start\" @click=${this.ensureOllamaReadyWithUI}>Start Ollama Service</button> `;\r\n    }\r\n\r\n    _renderReadyState() {\r\n        return html`\r\n            <!-- Model Input with Autocomplete -->\r\n            <input\r\n                type=\"text\"\r\n                class=\"api-input\"\r\n                placeholder=\"Model name (press Enter to install)\"\r\n                .value=${this.selectedLlmModel}\r\n                @input=${this.handleModelInput}\r\n                @keypress=${this.handleModelKeyPress}\r\n                list=\"model-suggestions\"\r\n                ?disabled=${this.isLoading || this.installingModel}\r\n                style=\"text-align: left; padding-left: 12px;\"\r\n            />\r\n            <datalist id=\"model-suggestions\">\r\n                ${this.getCombinedModelSuggestions().map(\r\n                    model => html`\r\n                        <option value=${model.name}>\r\n                            ${model.name} ${model.status === 'installed' ? '✓ Installed' : model.status === 'history' ? '📝 Recent' : '- Available'}\r\n                        </option>\r\n                    `\r\n                )}\r\n            </datalist>\r\n\r\n            <!-- Show model status -->\r\n            ${this.renderModelStatus()}\r\n            ${this.installingModel && !this.installingModel.includes('Ollama')\r\n                ? html`\r\n                      <div style=\"margin-top: 3px; display: flex; align-items: center; gap: 6px;\">\r\n                          <div style=\"height: 1px; background: rgba(255,255,255,0.3); border-radius: 0.5px; overflow: hidden; flex: 1;\">\r\n                              <div\r\n                                  style=\"height: 100%; background: rgba(0,122,255,1); width: ${this.installProgress}%; transition: width 0.1s ease;\"\r\n                              ></div>\r\n                          </div>\r\n                          <div style=\"font-size: 8px; color: rgba(255,255,255,0.8); font-weight: 600; min-width: 24px; text-align: right;\">\r\n                              ${this.installProgress}%\r\n                          </div>\r\n                      </div>\r\n                  `\r\n                : ''}\r\n        `;\r\n    }\r\n\r\n    _renderFailedState(state) {\r\n        return html`\r\n            <div style=\"margin-top: 6px; padding: 8px; background: rgba(239,68,68,0.1); border-radius: 8px;\">\r\n                <div style=\"font-size: 11px; color: rgba(239,68,68,0.8); margin-bottom: 4px; text-align: center;\">Connection failed</div>\r\n                <div style=\"font-size: 10px; color: rgba(239,68,68,0.6); text-align: center; margin-bottom: 6px;\">\r\n                    ${state.message || 'Unknown error'}\r\n                </div>\r\n                <button\r\n                    class=\"action-button\"\r\n                    style=\"width: 100%; height: 28px; font-size: 10px; background: rgba(239,68,68,0.2);\"\r\n                    @click=${() => this._initializeOllamaConnection()}\r\n                >\r\n                    Retry Connection\r\n                </button>\r\n            </div>\r\n        `;\r\n    }\r\n\r\n    _renderInstallingState(state) {\r\n        return html`\r\n            <div style=\"margin-top: 3px; display: flex; align-items: center; gap: 6px;\">\r\n                <div style=\"height: 1px; background: rgba(255,255,255,0.3); border-radius: 0.5px; overflow: hidden; flex: 1;\">\r\n                    <div style=\"height: 100%; background: rgba(0,122,255,1); width: ${state.progress}%; transition: width 0.1s ease;\"></div>\r\n                </div>\r\n                <div style=\"font-size: 8px; color: rgba(255,255,255,0.8); font-weight: 600; min-width: 24px; text-align: right;\">\r\n                    ${state.progress}%\r\n                </div>\r\n            </div>\r\n        `;\r\n    }\r\n\r\n    _renderUnknownState() {\r\n        return html`\r\n            <div style=\"margin-top: 6px; padding: 8px; background: rgba(255,200,0,0.1); border-radius: 8px;\">\r\n                <div style=\"font-size: 11px; color: rgba(255,200,0,0.8); text-align: center;\">Unknown state - Please refresh</div>\r\n            </div>\r\n        `;\r\n    }\r\n\r\n    renderModelStatus() {\r\n        return '';\r\n    }\r\n\r\n    shouldFadeMessage(type) {\r\n        const hasMessage = type === 'error' ? this.errorMessage : this.successMessage;\r\n        return hasMessage && this.messageTimestamp > 0 && Date.now() - this.messageTimestamp > 100;\r\n    }\r\n\r\n    openPrivacyPolicy() {\r\n        console.log('🔊 openPrivacyPolicy ApiKeyHeader');\r\n        if (window.api?.common) {\r\n            window.api.common.openExternal('https://pickle.com/privacy-policy');\r\n        }\r\n    }\r\n\r\n    render() {\r\n        const llmNeedsApiKey = this.llmProvider !== 'ollama' && this.llmProvider !== 'whisper';\r\n        const sttNeedsApiKey = this.sttProvider !== 'ollama' && this.sttProvider !== 'whisper';\r\n        const llmNeedsModel = this.llmProvider === 'ollama';\r\n        const sttNeedsModel = this.sttProvider === 'whisper';\r\n\r\n        const isButtonDisabled =\r\n            this.isLoading ||\r\n            this.installingModel ||\r\n            Object.keys(this.whisperInstallingModels).length > 0 ||\r\n            (llmNeedsApiKey && !this.llmApiKey.trim()) ||\r\n            (sttNeedsApiKey && !this.sttApiKey.trim()) ||\r\n            (llmNeedsModel && !this.selectedLlmModel?.trim()) ||\r\n            (sttNeedsModel && !this.selectedSttModel);\r\n\r\n        const llmProviderName = this.providers.llm.find(p => p.id === this.llmProvider)?.name || this.llmProvider;\r\n\r\n        return html`\r\n            <div class=\"container\">\r\n                <button class=\"close-button\" @click=${this.handleClose}>×</button>\r\n                <div class=\"header\">\r\n                    <div class=\"back-button\" @click=${this.handleBack}>\r\n                        <i class=\"arrow-icon-left\"></i>\r\n                        <div class=\"back-button-text\">Back</div>\r\n                    </div>\r\n                    <div class=\"title\">Use Personal API keys</div>\r\n                </div>\r\n\r\n                <!-- LLM Section -->\r\n                <div class=\"section\">\r\n                    <div class=\"row\">\r\n                        <div class=\"label\">1. Select LLM Provider</div>\r\n                        <div class=\"provider-selector\">\r\n                            ${this.providers.llm.map(\r\n                                p => html`\r\n                                    <button\r\n                                        class=\"provider-button\"\r\n                                        data-status=${this.llmProvider === p.id ? 'active' : 'default'}\r\n                                        @click=${e => this.handleLlmProviderChange(e, p.id)}\r\n                                    >\r\n                                        ${p.name}\r\n                                    </button>\r\n                                `\r\n                            )}\r\n                        </div>\r\n                    </div>\r\n                    <div class=\"row\">\r\n                        <div class=\"label\">2. Enter API Key</div>\r\n                        ${this.llmProvider === 'ollama'\r\n                            ? this._renderOllamaStateUI()\r\n                            : html`\r\n                                  <div class=\"input-wrapper\">\r\n                                      <input\r\n                                          type=\"password\"\r\n                                          class=\"api-input ${this.llmError ? 'invalid' : ''}\"\r\n                                          placeholder=\"Enter your ${llmProviderName} API key\"\r\n                                          .value=${this.llmApiKey}\r\n                                          @input=${e => {\r\n                                              this.llmApiKey = e.target.value;\r\n                                              this.llmError = '';\r\n                                          }}\r\n                                          ?disabled=${this.isLoading}\r\n                                      />\r\n                                      ${this.llmError ? html`<div class=\"inline-error-message\">${this.llmError}</div>` : ''}\r\n                                  </div>\r\n                              `}\r\n                    </div>\r\n                </div>\r\n\r\n                <!-- STT Section -->\r\n                <div class=\"section\">\r\n                    <div class=\"row\">\r\n                        <div class=\"label\">3. Select STT Provider</div>\r\n                        <div class=\"provider-selector\">\r\n                            ${this.providers.stt.map(\r\n                                p => html`\r\n                                    <button\r\n                                        class=\"provider-button\"\r\n                                        data-status=${this.sttProvider === p.id ? 'active' : 'default'}\r\n                                        @click=${e => this.handleSttProviderChange(e, p.id)}\r\n                                    >\r\n                                        ${p.name}\r\n                                    </button>\r\n                                `\r\n                            )}\r\n                        </div>\r\n                    </div>\r\n                    <div class=\"row\">\r\n                        <div class=\"label\">4. Enter STT API Key</div>\r\n                        ${this.sttProvider === 'ollama'\r\n                            ? html`\r\n                                  <div class=\"api-input\" style=\"background: transparent; border: none; text-align: right; color: #a0a0a0;\">\r\n                                      STT not supported by Ollama\r\n                                  </div>\r\n                              `\r\n                            : this.sttProvider === 'whisper'\r\n                              ? html`\r\n                                    <div class=\"input-wrapper\">\r\n                                        <select\r\n                                            class=\"api-input ${this.sttError ? 'invalid' : ''}\"\r\n                                            .value=${this.selectedSttModel || ''}\r\n                                            @change=${e => {\r\n                                                this.handleSttModelChange(e);\r\n                                                this.sttError = '';\r\n                                            }}\r\n                                            ?disabled=${this.isLoading}\r\n                                        >\r\n                                            <option value=\"\">Select a model...</option>\r\n                                            ${[\r\n                                                { id: 'whisper-tiny', name: 'Whisper Tiny (39M)' },\r\n                                                { id: 'whisper-base', name: 'Whisper Base (74M)' },\r\n                                                { id: 'whisper-small', name: 'Whisper Small (244M)' },\r\n                                                { id: 'whisper-medium', name: 'Whisper Medium (769M)' },\r\n                                            ].map(model => html` <option value=\"${model.id}\">${model.name}</option> `)}\r\n                                        </select>\r\n                                        ${this.sttError ? html`<div class=\"inline-error-message\">${this.sttError}</div>` : ''}\r\n                                    </div>\r\n                                `\r\n                              : html`\r\n                                    <div class=\"input-wrapper\">\r\n                                        <input\r\n                                            type=\"password\"\r\n                                            class=\"api-input ${this.sttError ? 'invalid' : ''}\"\r\n                                            placeholder=\"Enter your STT API key\"\r\n                                            .value=${this.sttApiKey}\r\n                                            @input=${e => {\r\n                                                this.sttApiKey = e.target.value;\r\n                                                this.sttError = '';\r\n                                            }}\r\n                                            ?disabled=${this.isLoading}\r\n                                        />\r\n                                        ${this.sttError ? html`<div class=\"inline-error-message\">${this.sttError}</div>` : ''}\r\n                                    </div>\r\n                                `}\r\n                    </div>\r\n                </div>\r\n                <div class=\"confirm-button-container\">\r\n                    <button class=\"confirm-button\" @click=${this.handleSubmit} ?disabled=${isButtonDisabled}>\r\n                        ${this.isLoading\r\n                            ? 'Setting up...'\r\n                            : this.installingModel\r\n                              ? `Installing ${this.installingModel}...`\r\n                              : Object.keys(this.whisperInstallingModels).length > 0\r\n                                ? `Downloading...`\r\n                                : 'Confirm'}\r\n                    </button>\r\n                </div>\r\n\r\n                <div class=\"footer\">\r\n                    Get your API key from: OpenAI | Google | Anthropic\r\n                    <br />\r\n                    Glass does not collect your personal data —\r\n                    <span class=\"footer-link\" @click=${this.openPrivacyPolicy}>See details</span>\r\n                </div>\r\n\r\n                <div class=\"error-message ${this.shouldFadeMessage('error') ? 'message-fade-out' : ''}\" @animationend=${this.handleMessageFadeEnd}>\r\n                    ${this.errorMessage}\r\n                </div>\r\n                <div\r\n                    class=\"success-message ${this.shouldFadeMessage('success') ? 'message-fade-out' : ''}\"\r\n                    @animationend=${this.handleMessageFadeEnd}\r\n                >\r\n                    ${this.successMessage}\r\n                </div>\r\n            </div>\r\n        `;\r\n    }\r\n}\r\n\r\ncustomElements.define('apikey-header', ApiKeyHeader);\r\n", "import { LitElement, html, css } from '../assets/lit-core-2.7.4.min.js';\r\n\r\nexport class PermissionHeader extends LitElement {\r\n    static styles = css`\r\n        :host {\r\n            display: block;\r\n            transition: opacity 0.3s ease-in, transform 0.3s ease-in;\r\n            will-change: opacity, transform;\r\n        }\r\n\r\n        :host(.sliding-out) {\r\n            opacity: 0;\r\n            transform: translateY(-20px);\r\n        }\r\n\r\n        :host(.hidden) {\r\n            opacity: 0;\r\n            pointer-events: none;\r\n        }\r\n\r\n        * {\r\n            font-family: 'Helvetica Neue', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;\r\n            cursor: default;\r\n            user-select: none;\r\n            box-sizing: border-box;\r\n        }\r\n\r\n        .container {\r\n            -webkit-app-region: drag;\r\n            width: 285px;\r\n            /* height is now set dynamically */\r\n            padding: 18px 20px;\r\n            background: rgba(0, 0, 0, 0.3);\r\n            border-radius: 16px;\r\n            overflow: hidden;\r\n            position: relative;\r\n            display: flex;\r\n            flex-direction: column;\r\n            align-items: center;\r\n        }\r\n\r\n        .container::after {\r\n            content: '';\r\n            position: absolute;\r\n            top: 0;\r\n            left: 0;\r\n            right: 0;\r\n            bottom: 0;\r\n            border-radius: 16px;\r\n            padding: 1px;\r\n            background: linear-gradient(169deg, rgba(255, 255, 255, 0.5) 0%, rgba(255, 255, 255, 0) 50%, rgba(255, 255, 255, 0.5) 100%);\r\n            -webkit-mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);\r\n            -webkit-mask-composite: destination-out;\r\n            mask-composite: exclude;\r\n            pointer-events: none;\r\n        }\r\n\r\n        .close-button {\r\n            -webkit-app-region: no-drag;\r\n            position: absolute;\r\n            top: 10px;\r\n            right: 10px;\r\n            width: 14px;\r\n            height: 14px;\r\n            background: rgba(255, 255, 255, 0.1);\r\n            border: none;\r\n            border-radius: 3px;\r\n            color: rgba(255, 255, 255, 0.7);\r\n            cursor: pointer;\r\n            display: flex;\r\n            align-items: center;\r\n            justify-content: center;\r\n            transition: all 0.15s ease;\r\n            z-index: 10;\r\n            font-size: 14px;\r\n            line-height: 1;\r\n            padding: 0;\r\n        }\r\n\r\n        .close-button:hover {\r\n            background: rgba(255, 255, 255, 0.2);\r\n            color: rgba(255, 255, 255, 0.9);\r\n        }\r\n\r\n        .close-button:active {\r\n            transform: scale(0.95);\r\n        }\r\n\r\n        .title {\r\n            color: white;\r\n            font-size: 16px;\r\n            font-weight: 500;\r\n            margin: 0;\r\n            text-align: center;\r\n            flex-shrink: 0;\r\n        }\r\n\r\n        .form-content {\r\n            display: flex;\r\n            flex-direction: column;\r\n            align-items: center;\r\n            width: 100%;\r\n            margin-top: auto;\r\n        }\r\n\r\n        .form-content.all-granted {\r\n            flex-grow: 1;\r\n            justify-content: center;\r\n            margin-top: 0;\r\n        }\r\n\r\n        .subtitle {\r\n            color: rgba(255, 255, 255, 0.7);\r\n            font-size: 11px;\r\n            font-weight: 400;\r\n            text-align: center;\r\n            margin-bottom: 12px;\r\n            line-height: 1.3;\r\n        }\r\n\r\n        .permission-status {\r\n            display: flex;\r\n            align-items: center;\r\n            justify-content: center;\r\n            gap: 8px;\r\n            margin-bottom: 12px;\r\n            min-height: 20px;\r\n        }\r\n\r\n        .permission-item {\r\n            display: flex;\r\n            align-items: center;\r\n            gap: 6px;\r\n            color: rgba(255, 255, 255, 0.8);\r\n            font-size: 11px;\r\n            font-weight: 400;\r\n        }\r\n\r\n        .permission-item.granted {\r\n            color: rgba(34, 197, 94, 0.9);\r\n        }\r\n\r\n        .permission-icon {\r\n            width: 12px;\r\n            height: 12px;\r\n            opacity: 0.8;\r\n        }\r\n\r\n        .check-icon {\r\n            width: 12px;\r\n            height: 12px;\r\n            color: rgba(34, 197, 94, 0.9);\r\n        }\r\n\r\n        .action-button {\r\n            -webkit-app-region: no-drag;\r\n            width: 100%;\r\n            height: 34px;\r\n            background: rgba(255, 255, 255, 0.2);\r\n            border: none;\r\n            border-radius: 10px;\r\n            color: white;\r\n            font-size: 12px;\r\n            font-weight: 500;\r\n            cursor: pointer;\r\n            transition: background 0.15s ease;\r\n            position: relative;\r\n            overflow: hidden;\r\n            margin-bottom: 6px;\r\n        }\r\n\r\n        .action-button::after {\r\n            content: '';\r\n            position: absolute;\r\n            top: 0;\r\n            left: 0;\r\n            right: 0;\r\n            bottom: 0;\r\n            border-radius: 10px;\r\n            padding: 1px;\r\n            background: linear-gradient(169deg, rgba(255, 255, 255, 0.5) 0%, rgba(255, 255, 255, 0) 50%, rgba(255, 255, 255, 0.5) 100%);\r\n            -webkit-mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);\r\n            -webkit-mask-composite: destination-out;\r\n            mask-composite: exclude;\r\n            pointer-events: none;\r\n        }\r\n\r\n        .action-button:hover:not(:disabled) {\r\n            background: rgba(255, 255, 255, 0.3);\r\n        }\r\n\r\n        .action-button:disabled {\r\n            opacity: 0.5;\r\n            cursor: not-allowed;\r\n        }\r\n\r\n        .continue-button {\r\n            -webkit-app-region: no-drag;\r\n            width: 100%;\r\n            height: 34px;\r\n            background: rgba(34, 197, 94, 0.8);\r\n            border: none;\r\n            border-radius: 10px;\r\n            color: white;\r\n            font-size: 12px;\r\n            font-weight: 500;\r\n            cursor: pointer;\r\n            transition: background 0.15s ease;\r\n            position: relative;\r\n            overflow: hidden;\r\n            margin-top: 4px;\r\n        }\r\n\r\n        .continue-button::after {\r\n            content: '';\r\n            position: absolute;\r\n            top: 0;\r\n            left: 0;\r\n            right: 0;\r\n            bottom: 0;\r\n            border-radius: 10px;\r\n            padding: 1px;\r\n            background: linear-gradient(169deg, rgba(255, 255, 255, 0.5) 0%, rgba(255, 255, 255, 0) 50%, rgba(255, 255, 255, 0.5) 100%);\r\n            -webkit-mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);\r\n            -webkit-mask-composite: destination-out;\r\n            mask-composite: exclude;\r\n            pointer-events: none;\r\n        }\r\n\r\n        .continue-button:hover:not(:disabled) {\r\n            background: rgba(34, 197, 94, 0.9);\r\n        }\r\n\r\n        .continue-button:disabled {\r\n            background: rgba(255, 255, 255, 0.2);\r\n            cursor: not-allowed;\r\n        }\r\n        \r\n        /* ────────────────[ GLASS BYPASS ]─────────────── */\r\n        :host-context(body.has-glass) .container,\r\n        :host-context(body.has-glass) .action-button,\r\n        :host-context(body.has-glass) .continue-button,\r\n        :host-context(body.has-glass) .close-button {\r\n            background: transparent !important;\r\n            border: none !important;\r\n            box-shadow: none !important;\r\n            filter: none !important;\r\n            backdrop-filter: none !important;\r\n        }\r\n\r\n        :host-context(body.has-glass) .container::after,\r\n        :host-context(body.has-glass) .action-button::after,\r\n        :host-context(body.has-glass) .continue-button::after {\r\n            display: none !important;\r\n        }\r\n\r\n        :host-context(body.has-glass) .action-button:hover,\r\n        :host-context(body.has-glass) .continue-button:hover,\r\n        :host-context(body.has-glass) .close-button:hover {\r\n            background: transparent !important;\r\n        }\r\n    `;\r\n\r\n    static properties = {\r\n        microphoneGranted: { type: String },\r\n        screenGranted: { type: String },\r\n        keychainGranted: { type: String },\r\n        isChecking: { type: String },\r\n        continueCallback: { type: Function },\r\n        userMode: { type: String }, // 'local' or 'firebase'\r\n    };\r\n\r\n    constructor() {\r\n        super();\r\n        this.microphoneGranted = 'unknown';\r\n        this.screenGranted = 'unknown';\r\n        this.keychainGranted = 'unknown';\r\n        this.isChecking = false;\r\n        this.continueCallback = null;\r\n        this.userMode = 'local'; // Default to local\r\n    }\r\n\r\n    updated(changedProperties) {\r\n        super.updated(changedProperties);\r\n        if (changedProperties.has('userMode')) {\r\n            const newHeight = this.userMode === 'firebase' ? 280 : 220;\r\n            console.log(`[PermissionHeader] User mode changed to ${this.userMode}, requesting resize to ${newHeight}px`);\r\n            this.dispatchEvent(new CustomEvent('request-resize', {\r\n                detail: { height: newHeight },\r\n                bubbles: true,\r\n                composed: true\r\n            }));\r\n        }\r\n    }\r\n\r\n    async connectedCallback() {\r\n        super.connectedCallback();\r\n\r\n        if (window.api) {\r\n            try {\r\n                const userState = await window.api.common.getCurrentUser();\r\n                this.userMode = userState.mode;\r\n            } catch (e) {\r\n                console.error('[PermissionHeader] Failed to get user state', e);\r\n                this.userMode = 'local'; // Fallback to local\r\n            }\r\n        }\r\n\r\n        await this.checkPermissions();\r\n        \r\n        // Set up periodic permission check\r\n        this.permissionCheckInterval = setInterval(async () => {\r\n            if (window.api) {\r\n                try {\r\n                    const userState = await window.api.common.getCurrentUser();\r\n                    this.userMode = userState.mode;\r\n                } catch (e) {\r\n                    this.userMode = 'local';\r\n                }\r\n            }\r\n            this.checkPermissions();\r\n        }, 1000);\r\n    }\r\n\r\n    disconnectedCallback() {\r\n        super.disconnectedCallback();\r\n        if (this.permissionCheckInterval) {\r\n            clearInterval(this.permissionCheckInterval);\r\n        }\r\n    }\r\n\r\n    async checkPermissions() {\r\n        if (!window.api || this.isChecking) return;\r\n        \r\n        this.isChecking = true;\r\n        \r\n        try {\r\n            const permissions = await window.api.permissionHeader.checkSystemPermissions();\r\n            console.log('[PermissionHeader] Permission check result:', permissions);\r\n            \r\n            const prevMic = this.microphoneGranted;\r\n            const prevScreen = this.screenGranted;\r\n            const prevKeychain = this.keychainGranted;\r\n            \r\n            this.microphoneGranted = permissions.microphone;\r\n            this.screenGranted = permissions.screen;\r\n            this.keychainGranted = permissions.keychain;\r\n            \r\n            // if permissions changed == UI update\r\n            if (prevMic !== this.microphoneGranted || prevScreen !== this.screenGranted || prevKeychain !== this.keychainGranted) {\r\n                console.log('[PermissionHeader] Permission status changed, updating UI');\r\n                this.requestUpdate();\r\n            }\r\n\r\n            const isKeychainRequired = this.userMode === 'firebase';\r\n            const keychainOk = !isKeychainRequired || this.keychainGranted === 'granted';\r\n            \r\n            // if all permissions granted == automatically continue\r\n            if (this.microphoneGranted === 'granted' && \r\n                this.screenGranted === 'granted' && \r\n                keychainOk && \r\n                this.continueCallback) {\r\n                console.log('[PermissionHeader] All permissions granted, proceeding automatically');\r\n                setTimeout(() => this.handleContinue(), 500);\r\n            }\r\n        } catch (error) {\r\n            console.error('[PermissionHeader] Error checking permissions:', error);\r\n        } finally {\r\n            this.isChecking = false;\r\n        }\r\n    }\r\n\r\n    async handleMicrophoneClick() {\r\n        if (!window.api || this.microphoneGranted === 'granted') return;\r\n        \r\n        console.log('[PermissionHeader] Requesting microphone permission...');\r\n        \r\n        try {\r\n            const result = await window.api.permissionHeader.checkSystemPermissions();\r\n            console.log('[PermissionHeader] Microphone permission result:', result);\r\n            \r\n            if (result.microphone === 'granted') {\r\n                this.microphoneGranted = 'granted';\r\n                this.requestUpdate();\r\n                return;\r\n              }\r\n            \r\n              if (result.microphone === 'not-determined' || result.microphone === 'denied' || result.microphone === 'unknown' || result.microphone === 'restricted') {\r\n                const res = await window.api.permissionHeader.requestMicrophonePermission();\r\n                if (res.status === 'granted' || res.success === true) {\r\n                    this.microphoneGranted = 'granted';\r\n                    this.requestUpdate();\r\n                    return;\r\n                }\r\n              }\r\n            \r\n            \r\n            // Check permissions again after a delay\r\n            // setTimeout(() => this.checkPermissions(), 1000);\r\n        } catch (error) {\r\n            console.error('[PermissionHeader] Error requesting microphone permission:', error);\r\n        }\r\n    }\r\n\r\n    async handleScreenClick() {\r\n        if (!window.api || this.screenGranted === 'granted') return;\r\n        \r\n        console.log('[PermissionHeader] Checking screen recording permission...');\r\n        \r\n        try {\r\n            const permissions = await window.api.permissionHeader.checkSystemPermissions();\r\n            console.log('[PermissionHeader] Screen permission check result:', permissions);\r\n            \r\n            if (permissions.screen === 'granted') {\r\n                this.screenGranted = 'granted';\r\n                this.requestUpdate();\r\n                return;\r\n            }\r\n            if (permissions.screen === 'not-determined' || permissions.screen === 'denied' || permissions.screen === 'unknown' || permissions.screen === 'restricted') {\r\n            console.log('[PermissionHeader] Opening screen recording preferences...');\r\n            await window.api.permissionHeader.openSystemPreferences('screen-recording');\r\n            }\r\n            \r\n            // Check permissions again after a delay\r\n            // (This may not execute if app restarts after permission grant)\r\n            // setTimeout(() => this.checkPermissions(), 2000);\r\n        } catch (error) {\r\n            console.error('[PermissionHeader] Error opening screen recording preferences:', error);\r\n        }\r\n    }\r\n\r\n    async handleKeychainClick() {\r\n        if (!window.api || this.keychainGranted === 'granted') return;\r\n        \r\n        console.log('[PermissionHeader] Requesting keychain permission...');\r\n        \r\n        try {\r\n            // Trigger initializeKey to prompt for keychain access\r\n            // Assuming encryptionService is accessible or via API\r\n            await window.api.permissionHeader.initializeEncryptionKey(); // New IPC handler needed\r\n            \r\n            // After success, update status\r\n            this.keychainGranted = 'granted';\r\n            this.requestUpdate();\r\n        } catch (error) {\r\n            console.error('[PermissionHeader] Error requesting keychain permission:', error);\r\n        }\r\n    }\r\n\r\n    async handleContinue() {\r\n        const isKeychainRequired = this.userMode === 'firebase';\r\n        const keychainOk = !isKeychainRequired || this.keychainGranted === 'granted';\r\n\r\n        if (this.continueCallback && \r\n            this.microphoneGranted === 'granted' && \r\n            this.screenGranted === 'granted' && \r\n            keychainOk) {\r\n            // Mark permissions as completed\r\n            if (window.api && isKeychainRequired) {\r\n                try {\r\n                    await window.api.permissionHeader.markKeychainCompleted();\r\n                    console.log('[PermissionHeader] Marked keychain as completed');\r\n                } catch (error) {\r\n                    console.error('[PermissionHeader] Error marking keychain as completed:', error);\r\n                }\r\n            }\r\n            \r\n            this.continueCallback();\r\n        }\r\n    }\r\n\r\n    handleClose() {\r\n        console.log('Close button clicked');\r\n        if (window.api) {\r\n            window.api.common.quitApplication();\r\n        }\r\n    }\r\n\r\n    render() {\r\n        const isKeychainRequired = this.userMode === 'firebase';\r\n        const containerHeight = isKeychainRequired ? 280 : 220;\r\n        const keychainOk = !isKeychainRequired || this.keychainGranted === 'granted';\r\n        const allGranted = this.microphoneGranted === 'granted' && this.screenGranted === 'granted' && keychainOk;\r\n\r\n        return html`\r\n            <div class=\"container\" style=\"height: ${containerHeight}px\">\r\n                <button class=\"close-button\" @click=${this.handleClose} title=\"Close application\">\r\n                    <svg width=\"8\" height=\"8\" viewBox=\"0 0 10 10\" fill=\"currentColor\">\r\n                        <path d=\"M1 1L9 9M9 1L1 9\" stroke=\"currentColor\" stroke-width=\"1.2\" />\r\n                    </svg>\r\n                </button>\r\n                <h1 class=\"title\">Permission Setup Required</h1>\r\n\r\n                <div class=\"form-content ${allGranted ? 'all-granted' : ''}\">\r\n                    ${!allGranted ? html`\r\n                        <div class=\"subtitle\">Grant access to microphone, screen recording${isKeychainRequired ? ' and keychain' : ''} to continue</div>\r\n                        \r\n                        <div class=\"permission-status\">\r\n                            <div class=\"permission-item ${this.microphoneGranted === 'granted' ? 'granted' : ''}\">\r\n                                ${this.microphoneGranted === 'granted' ? html`\r\n                                    <svg class=\"check-icon\" viewBox=\"0 0 20 20\" fill=\"currentColor\">\r\n                                        <path fill-rule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z\" clip-rule=\"evenodd\" />\r\n                                    </svg>\r\n                                    <span>Microphone ✓</span>\r\n                                ` : html`\r\n                                    <svg class=\"permission-icon\" viewBox=\"0 0 20 20\" fill=\"currentColor\">\r\n                                        <path fill-rule=\"evenodd\" d=\"M7 4a3 3 0 016 0v4a3 3 0 11-6 0V4zm4 10.93A7.001 7.001 0 0017 8a1 1 0 10-2 0A5 5 0 015 8a1 1 0 00-2 0 7.001 7.001 0 006 6.93V17H6a1 1 0 100 2h8a1 1 0 100-2h-3v-2.07z\" clip-rule=\"evenodd\" />\r\n                                    </svg>\r\n                                    <span>Microphone</span>\r\n                                `}\r\n                            </div>\r\n                            \r\n                            <div class=\"permission-item ${this.screenGranted === 'granted' ? 'granted' : ''}\">\r\n                                ${this.screenGranted === 'granted' ? html`\r\n                                    <svg class=\"check-icon\" viewBox=\"0 0 20 20\" fill=\"currentColor\">\r\n                                        <path fill-rule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z\" clip-rule=\"evenodd\" />\r\n                                    </svg>\r\n                                    <span>Screen ✓</span>\r\n                                ` : html`\r\n                                    <svg class=\"permission-icon\" viewBox=\"0 0 20 20\" fill=\"currentColor\">\r\n                                        <path fill-rule=\"evenodd\" d=\"M3 5a2 2 0 012-2h10a2 2 0 012 2v8a2 2 0 01-2 2h-2.22l.123.489.804.804A1 1 0 0113 18H7a1 1 0 01-.707-1.707l.804-.804L7.22 15H5a2 2 0 01-2-2V5zm5.771 7H5V5h10v7H8.771z\" clip-rule=\"evenodd\" />\r\n                                    </svg>\r\n                                    <span>Screen Recording</span>\r\n                                `}\r\n                            </div>\r\n\r\n                            ${isKeychainRequired ? html`\r\n                                <div class=\"permission-item ${this.keychainGranted === 'granted' ? 'granted' : ''}\">\r\n                                    ${this.keychainGranted === 'granted' ? html`\r\n                                        <svg class=\"check-icon\" viewBox=\"0 0 20 20\" fill=\"currentColor\">\r\n                                            <path fill-rule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z\" clip-rule=\"evenodd\" />\r\n                                        </svg>\r\n                                        <span>Data Encryption ✓</span>\r\n                                    ` : html`\r\n                                        <svg class=\"permission-icon\" viewBox=\"0 0 20 20\" fill=\"currentColor\">\r\n                                            <path fill-rule=\"evenodd\" d=\"M18 8a6 6 0 01-7.744 5.668l-1.649 1.652c-.63.63-1.706.19-1.706-.742V12.18a.75.75 0 00-1.5 0v2.696c0 .932-1.075 1.372-1.706.742l-1.649-1.652A6 6 0 112 8zm-4 0a.75.75 0 00.75-.75A3.75 3.75 0 018.25 4a.75.75 0 000 1.5 2.25 2.25 0 012.25 ********** 0 00.75.75z\" clip-rule=\"evenodd\" />\r\n                                        </svg>\r\n                                        <span>Data Encryption</span>\r\n                                    `}\r\n                                </div>\r\n                            ` : ''}\r\n                        </div>\r\n\r\n                        <button \r\n                            class=\"action-button\" \r\n                            @click=${this.handleMicrophoneClick}\r\n                            ?disabled=${this.microphoneGranted === 'granted'}\r\n                        >\r\n                            ${this.microphoneGranted === 'granted' ? 'Microphone Access Granted' : 'Grant Microphone Access'}\r\n                        </button>\r\n\r\n                        <button \r\n                            class=\"action-button\" \r\n                            @click=${this.handleScreenClick}\r\n                            ?disabled=${this.screenGranted === 'granted'}\r\n                        >\r\n                            ${this.screenGranted === 'granted' ? 'Screen Recording Granted' : 'Grant Screen Recording Access'}\r\n                        </button>\r\n\r\n                        ${isKeychainRequired ? html`\r\n                            <button \r\n                                class=\"action-button\" \r\n                                @click=${this.handleKeychainClick}\r\n                                ?disabled=${this.keychainGranted === 'granted'}\r\n                            >\r\n                                ${this.keychainGranted === 'granted' ? 'Encryption Enabled' : 'Enable Encryption'}\r\n                            </button>\r\n                            <div class=\"subtitle\" style=\"visibility: ${this.keychainGranted === 'granted' ? 'hidden' : 'visible'}\">\r\n                                Stores the key to encrypt your data. Press \"<b>Always Allow</b>\" to continue.\r\n                            </div>\r\n                        ` : ''}\r\n                    ` : html`\r\n                        <button \r\n                            class=\"continue-button\" \r\n                            @click=${this.handleContinue}\r\n                        >\r\n                            Continue to Pickle Glass\r\n                        </button>\r\n                    `}\r\n                </div>\r\n            </div>\r\n        `;\r\n    }\r\n}\r\n\r\ncustomElements.define('permission-setup', PermissionHeader); ", "import { html, css, LitElement } from '../assets/lit-core-2.7.4.min.js';\r\n\r\nexport class WelcomeHeader extends LitElement {\r\n    static styles = css`\r\n        :host {\r\n            display: block;\r\n            font-family:\r\n                'Inter',\r\n                -apple-system,\r\n                BlinkMacSystemFont,\r\n                'Segoe UI',\r\n                Roboto,\r\n                sans-serif;\r\n        }\r\n        .container {\r\n            width: 100%;\r\n            box-sizing: border-box;\r\n            height: auto;\r\n            padding: 24px 16px;\r\n            background: rgba(0, 0, 0, 0.64);\r\n            box-shadow: 0px 0px 0px 1.5px rgba(255, 255, 255, 0.64) inset;\r\n            border-radius: 16px;\r\n            flex-direction: column;\r\n            justify-content: flex-start;\r\n            align-items: flex-start;\r\n            gap: 32px;\r\n            display: inline-flex;\r\n            -webkit-app-region: drag;\r\n        }\r\n        .close-button {\r\n            -webkit-app-region: no-drag;\r\n            position: absolute;\r\n            top: 16px;\r\n            right: 16px;\r\n            width: 20px;\r\n            height: 20px;\r\n            background: rgba(255, 255, 255, 0.1);\r\n            border: none;\r\n            border-radius: 5px;\r\n            color: rgba(255, 255, 255, 0.7);\r\n            cursor: pointer;\r\n            display: flex;\r\n            align-items: center;\r\n            justify-content: center;\r\n            transition: all 0.15s ease;\r\n            z-index: 10;\r\n            font-size: 16px;\r\n            line-height: 1;\r\n            padding: 0;\r\n        }\r\n        .close-button:hover {\r\n            background: rgba(255, 255, 255, 0.2);\r\n            color: rgba(255, 255, 255, 0.9);\r\n        }\r\n        .header-section {\r\n            flex-direction: column;\r\n            justify-content: flex-start;\r\n            align-items: flex-start;\r\n            gap: 4px;\r\n            display: flex;\r\n        }\r\n        .title {\r\n            color: white;\r\n            font-size: 18px;\r\n            font-weight: 700;\r\n        }\r\n        .subtitle {\r\n            color: white;\r\n            font-size: 14px;\r\n            font-weight: 500;\r\n        }\r\n        .option-card {\r\n            width: 100%;\r\n            justify-content: flex-start;\r\n            align-items: flex-start;\r\n            gap: 8px;\r\n            display: inline-flex;\r\n        }\r\n        .divider {\r\n            width: 1px;\r\n            align-self: stretch;\r\n            position: relative;\r\n            background: #bebebe;\r\n            border-radius: 2px;\r\n        }\r\n        .option-content {\r\n            flex: 1 1 0;\r\n            flex-direction: column;\r\n            justify-content: flex-start;\r\n            align-items: flex-start;\r\n            gap: 8px;\r\n            display: inline-flex;\r\n            min-width: 0;\r\n        }\r\n        .option-title {\r\n            color: white;\r\n            font-size: 14px;\r\n            font-weight: 700;\r\n        }\r\n        .option-description {\r\n            color: #dcdcdc;\r\n            font-size: 12px;\r\n            font-weight: 400;\r\n            line-height: 18px;\r\n            letter-spacing: 0.12px;\r\n            white-space: nowrap;\r\n            overflow: hidden;\r\n            text-overflow: ellipsis;\r\n        }\r\n        .action-button {\r\n            -webkit-app-region: no-drag;\r\n            padding: 8px 10px;\r\n            background: rgba(132.6, 132.6, 132.6, 0.8);\r\n            box-shadow: 0px 2px 2px rgba(0, 0, 0, 0.16);\r\n            border-radius: 16px;\r\n            border: 1px solid rgba(255, 255, 255, 0.5);\r\n            justify-content: center;\r\n            align-items: center;\r\n            gap: 6px;\r\n            display: flex;\r\n            cursor: pointer;\r\n            transition: background-color 0.2s;\r\n        }\r\n        .action-button:hover {\r\n            background: rgba(150, 150, 150, 0.9);\r\n        }\r\n        .button-text {\r\n            color: white;\r\n            font-size: 12px;\r\n            font-weight: 600;\r\n        }\r\n        .button-icon {\r\n            width: 12px;\r\n            height: 12px;\r\n            position: relative;\r\n            display: flex;\r\n            align-items: center;\r\n            justify-content: center;\r\n        }\r\n        .arrow-icon {\r\n            border: solid white;\r\n            border-width: 0 1.2px 1.2px 0;\r\n            display: inline-block;\r\n            padding: 3px;\r\n            transform: rotate(-45deg);\r\n            -webkit-transform: rotate(-45deg);\r\n        }\r\n        .footer {\r\n            align-self: stretch;\r\n            text-align: center;\r\n            color: #dcdcdc;\r\n            font-size: 12px;\r\n            font-weight: 500;\r\n            line-height: 19.2px;\r\n        }\r\n        .footer-link {\r\n            text-decoration: underline;\r\n            cursor: pointer;\r\n            -webkit-app-region: no-drag;\r\n        }\r\n    `;\r\n\r\n    static properties = {\r\n        loginCallback: { type: Function },\r\n        apiKeyCallback: { type: Function },\r\n    };\r\n\r\n    constructor() {\r\n        super();\r\n        this.loginCallback = () => {};\r\n        this.apiKeyCallback = () => {};\r\n        this.handleClose = this.handleClose.bind(this);\r\n    }\r\n\r\n    updated(changedProperties) {\r\n        super.updated(changedProperties);\r\n        this.dispatchEvent(new CustomEvent('content-changed', { bubbles: true, composed: true }));\r\n    }\r\n\r\n    handleClose() {\r\n        if (window.api?.common) {\r\n            window.api.common.quitApplication();\r\n        }\r\n    }\r\n\r\n    render() {\r\n        return html`\r\n            <div class=\"container\">\r\n                <button class=\"close-button\" @click=${this.handleClose}>×</button>\r\n                <div class=\"header-section\">\r\n                    <div class=\"title\">Welcome to Glass</div>\r\n                    <div class=\"subtitle\">Choose how to connect your AI model</div>\r\n                </div>\r\n                <div class=\"option-card\">\r\n                    <div class=\"divider\"></div>\r\n                    <div class=\"option-content\">\r\n                        <div class=\"option-title\">Quick start with default API key</div>\r\n                        <div class=\"option-description\">\r\n                            100% free with Pickle's OpenAI key<br/>No personal data collected<br/>Sign up with Google in seconds\r\n                        </div>\r\n                    </div>\r\n                    <button class=\"action-button\" @click=${this.loginCallback}>\r\n                        <div class=\"button-text\">Open Browser to Log in</div>\r\n                        <div class=\"button-icon\"><div class=\"arrow-icon\"></div></div>\r\n                    </button>\r\n                </div>\r\n                <div class=\"option-card\">\r\n                    <div class=\"divider\"></div>\r\n                    <div class=\"option-content\">\r\n                        <div class=\"option-title\">Use Personal API keys</div>\r\n                        <div class=\"option-description\">\r\n                            Costs may apply based on your API usage<br/>No personal data collected<br/>Use your own API keys (OpenAI, Gemini, etc.)\r\n                        </div>\r\n                    </div>\r\n                    <button class=\"action-button\" @click=${this.apiKeyCallback}>\r\n                        <div class=\"button-text\">Enter Your API Key</div>\r\n                        <div class=\"button-icon\"><div class=\"arrow-icon\"></div></div>\r\n                    </button>\r\n                </div>\r\n                <div class=\"footer\">\r\n                    Glass does not collect your personal data —\r\n                    <span class=\"footer-link\" @click=${this.openPrivacyPolicy}>See details</span>\r\n                </div>\r\n            </div>\r\n        `;\r\n    }\r\n\r\n    openPrivacyPolicy() {\r\n        console.log('🔊 openPrivacyPolicy WelcomeHeader');\r\n        if (window.api?.common) {\r\n            window.api.common.openExternal('https://pickle.com/privacy-policy');\r\n        }\r\n    }\r\n}\r\n\r\ncustomElements.define('welcome-header', WelcomeHeader);", "import './MainHeader.js';\r\nimport './ApiKeyHeader.js';\r\nimport './PermissionHeader.js';\r\nimport './WelcomeHeader.js';\r\n\r\nclass HeaderTransitionManager {\r\n    constructor() {\r\n        this.headerContainer      = document.getElementById('header-container');\r\n        this.currentHeaderType    = null;   // 'welcome' | 'apikey' | 'main' | 'permission'\r\n        this.welcomeHeader        = null;\r\n        this.apiKeyHeader         = null;\r\n        this.mainHeader            = null;\r\n        this.permissionHeader      = null;\r\n\r\n        /**\r\n         * only one header window is allowed\r\n         * @param {'welcome'|'apikey'|'main'|'permission'} type\r\n         */\r\n        this.ensureHeader = (type) => {\r\n            console.log('[HeaderController] ensureHeader: Ensuring header of type:', type);\r\n            if (this.currentHeaderType === type) {\r\n                console.log('[HeaderController] ensureHeader: Header of type:', type, 'already exists.');\r\n                return;\r\n            }\r\n\r\n            this.headerContainer.innerHTML = '';\r\n            \r\n            this.welcomeHeader = null;\r\n            this.apiKeyHeader = null;\r\n            this.mainHeader = null;\r\n            this.permissionHeader = null;\r\n\r\n            // Create new header element\r\n            if (type === 'welcome') {\r\n                this.welcomeHeader = document.createElement('welcome-header');\r\n                this.welcomeHeader.loginCallback = () => this.handleLoginOption();\r\n                this.welcomeHeader.apiKeyCallback = () => this.handleApiKeyOption();\r\n                this.headerContainer.appendChild(this.welcomeHeader);\r\n                console.log('[HeaderController] ensureHeader: Header of type:', type, 'created.');\r\n            } else if (type === 'apikey') {\r\n                this.apiKeyHeader = document.createElement('apikey-header');\r\n                this.apiKeyHeader.stateUpdateCallback = (userState) => this.handleStateUpdate(userState);\r\n                this.apiKeyHeader.backCallback = () => this.transitionToWelcomeHeader();\r\n                this.apiKeyHeader.addEventListener('request-resize', e => {\r\n                    this._resizeForApiKey(e.detail.height); \r\n                });\r\n                this.headerContainer.appendChild(this.apiKeyHeader);\r\n                console.log('[HeaderController] ensureHeader: Header of type:', type, 'created.');\r\n            } else if (type === 'permission') {\r\n                this.permissionHeader = document.createElement('permission-setup');\r\n                this.permissionHeader.addEventListener('request-resize', e => {\r\n                    this._resizeForPermissionHeader(e.detail.height); \r\n                });\r\n                this.permissionHeader.continueCallback = async () => {\r\n                    if (window.api && window.api.headerController) {\r\n                        console.log('[HeaderController] Re-initializing model state after permission grant...');\r\n                        await window.api.headerController.reInitializeModelState();\r\n                    }\r\n                    this.transitionToMainHeader();\r\n                };\r\n                this.headerContainer.appendChild(this.permissionHeader);\r\n            } else {\r\n                this.mainHeader = document.createElement('main-header');\r\n                this.headerContainer.appendChild(this.mainHeader);\r\n                this.mainHeader.startSlideInAnimation?.();\r\n            }\r\n\r\n            this.currentHeaderType = type;\r\n            this.notifyHeaderState(type === 'permission' ? 'apikey' : type); // Keep permission state as apikey for compatibility\r\n        };\r\n\r\n        console.log('[HeaderController] Manager initialized');\r\n\r\n        // WelcomeHeader 콜백 메서드들\r\n        this.handleLoginOption = this.handleLoginOption.bind(this);\r\n        this.handleApiKeyOption = this.handleApiKeyOption.bind(this);\r\n\r\n        this._bootstrap();\r\n\r\n        if (window.api) {\r\n            window.api.headerController.onUserStateChanged((event, userState) => {\r\n                console.log('[HeaderController] Received user state change:', userState);\r\n                this.handleStateUpdate(userState);\r\n            });\r\n\r\n            window.api.headerController.onAuthFailed((event, { message }) => {\r\n                console.error('[HeaderController] Received auth failure from main process:', message);\r\n                if (this.apiKeyHeader) {\r\n                    this.apiKeyHeader.errorMessage = 'Authentication failed. Please try again.';\r\n                    this.apiKeyHeader.isLoading = false;\r\n                }\r\n            });\r\n            window.api.headerController.onForceShowApiKeyHeader(async () => {\r\n                console.log('[HeaderController] Received broadcast to show apikey header. Switching now.');\r\n                const isConfigured = await window.api.apiKeyHeader.areProvidersConfigured();\r\n                if (!isConfigured) {\r\n                    await this._resizeForWelcome();\r\n                    this.ensureHeader('welcome');\r\n                } else {\r\n                    await this._resizeForApiKey();\r\n                    this.ensureHeader('apikey');\r\n                }\r\n            });            \r\n        }\r\n    }\r\n\r\n    notifyHeaderState(stateOverride) {\r\n        const state = stateOverride || this.currentHeaderType || 'apikey';\r\n        if (window.api) {\r\n            window.api.headerController.sendHeaderStateChanged(state);\r\n        }\r\n    }\r\n\r\n    async _bootstrap() {\r\n        // The initial state will be sent by the main process via 'user-state-changed'\r\n        // We just need to request it.\r\n        if (window.api) {\r\n            const userState = await window.api.common.getCurrentUser();\r\n            console.log('[HeaderController] Bootstrapping with initial user state:', userState);\r\n            this.handleStateUpdate(userState);\r\n        } else {\r\n            // Fallback for non-electron environment (testing/web)\r\n            this.ensureHeader('welcome');\r\n        }\r\n    }\r\n\r\n\r\n    //////// after_modelStateService ////////\r\n    async handleStateUpdate(userState) {\r\n        const isConfigured = await window.api.apiKeyHeader.areProvidersConfigured();\r\n\r\n        if (isConfigured) {\r\n            // If providers are configured, always check permissions regardless of login state.\r\n            const permissionResult = await this.checkPermissions();\r\n            if (permissionResult.success) {\r\n                this.transitionToMainHeader();\r\n            } else {\r\n                this.transitionToPermissionHeader();\r\n            }\r\n        } else {\r\n            // If no providers are configured, show the welcome header to prompt for setup.\r\n            await this._resizeForWelcome();\r\n            this.ensureHeader('welcome');\r\n        }\r\n    }\r\n\r\n    // WelcomeHeader 콜백 메서드들\r\n    async handleLoginOption() {\r\n        console.log('[HeaderController] Login option selected');\r\n        if (window.api) {\r\n            await window.api.common.startFirebaseAuth();\r\n        }\r\n    }\r\n\r\n    async handleApiKeyOption() {\r\n        console.log('[HeaderController] API key option selected');\r\n        await this._resizeForApiKey(400);\r\n        this.ensureHeader('apikey');\r\n        // ApiKeyHeader에 뒤로가기 콜백 설정\r\n        if (this.apiKeyHeader) {\r\n            this.apiKeyHeader.backCallback = () => this.transitionToWelcomeHeader();\r\n        }\r\n    }\r\n\r\n    async transitionToWelcomeHeader() {\r\n        if (this.currentHeaderType === 'welcome') {\r\n            return this._resizeForWelcome();\r\n        }\r\n\r\n        await this._resizeForWelcome();\r\n        this.ensureHeader('welcome');\r\n    }\r\n    //////// after_modelStateService ////////\r\n\r\n    async transitionToPermissionHeader() {\r\n        // Prevent duplicate transitions\r\n        if (this.currentHeaderType === 'permission') {\r\n            console.log('[HeaderController] Already showing permission setup, skipping transition');\r\n            return;\r\n        }\r\n\r\n        // Check if permissions were previously completed\r\n        if (window.api) {\r\n            try {\r\n                const permissionsCompleted = await window.api.headerController.checkPermissionsCompleted();\r\n                if (permissionsCompleted) {\r\n                    console.log('[HeaderController] Permissions were previously completed, checking current status...');\r\n                    \r\n                    // Double check current permission status\r\n                    const permissionResult = await this.checkPermissions();\r\n                    if (permissionResult.success) {\r\n                        // Skip permission setup if already granted\r\n                        this.transitionToMainHeader();\r\n                        return;\r\n                    }\r\n                    \r\n                    console.log('[HeaderController] Permissions were revoked, showing setup again');\r\n                }\r\n            } catch (error) {\r\n                console.error('[HeaderController] Error checking permissions completed status:', error);\r\n            }\r\n        }\r\n\r\n        let initialHeight = 220;\r\n        if (window.api) {\r\n            try {\r\n                const userState = await window.api.common.getCurrentUser();\r\n                if (userState.mode === 'firebase') {\r\n                    initialHeight = 280;\r\n                }\r\n            } catch (e) {\r\n                console.error('Could not get user state for resize', e);\r\n            }\r\n        }\r\n\r\n        await this._resizeForPermissionHeader(initialHeight);\r\n        this.ensureHeader('permission');\r\n    }\r\n\r\n    async transitionToMainHeader(animate = true) {\r\n        if (this.currentHeaderType === 'main') {\r\n            return this._resizeForMain();\r\n        }\r\n\r\n        await this._resizeForMain();\r\n        this.ensureHeader('main');\r\n    }\r\n\r\n    async _resizeForMain() {\r\n        if (!window.api) return;\r\n        console.log('[HeaderController] _resizeForMain: Resizing window to 353x47');\r\n        return window.api.headerController.resizeHeaderWindow({ width: 353, height: 47 }).catch(() => {});\r\n    }\r\n\r\n    async _resizeForApiKey(height = 370) {\r\n        if (!window.api) return;\r\n        console.log(`[HeaderController] _resizeForApiKey: Resizing window to 456x${height}`);\r\n        return window.api.headerController.resizeHeaderWindow({ width: 456, height: height }).catch(() => {});\r\n    }\r\n\r\n    async _resizeForPermissionHeader(height) {\r\n        if (!window.api) return;\r\n        const finalHeight = height || 220;\r\n        return window.api.headerController.resizeHeaderWindow({ width: 285, height: finalHeight })\r\n            .catch(() => {});\r\n    }\r\n\r\n    async _resizeForWelcome() {\r\n        if (!window.api) return;\r\n        console.log('[HeaderController] _resizeForWelcome: Resizing window to 456x370');\r\n        return window.api.headerController.resizeHeaderWindow({ width: 456, height: 364 })\r\n            .catch(() => {});\r\n    }\r\n\r\n    async checkPermissions() {\r\n        if (!window.api) {\r\n            return { success: true };\r\n        }\r\n        \r\n        try {\r\n            const permissions = await window.api.headerController.checkSystemPermissions();\r\n            console.log('[HeaderController] Current permissions:', permissions);\r\n            \r\n            if (!permissions.needsSetup) {\r\n                return { success: true };\r\n            }\r\n\r\n            let errorMessage = '';\r\n            if (!permissions.microphone && !permissions.screen) {\r\n                errorMessage = 'Microphone and screen recording access required';\r\n            }\r\n            \r\n            return { \r\n                success: false, \r\n                error: errorMessage\r\n            };\r\n        } catch (error) {\r\n            console.error('[HeaderController] Error checking permissions:', error);\r\n            return { \r\n                success: false, \r\n                error: 'Failed to check permissions' \r\n            };\r\n        }\r\n    }\r\n}\r\n\r\nwindow.addEventListener('DOMContentLoaded', () => {\r\n    new HeaderTransitionManager();\r\n});\r\n"], "mappings": ";AAKA,IAAM,IAAE;AAAR,IAAe,IAAE,EAAE,eAAa,WAAS,EAAE,YAAU,EAAE,SAAS,iBAAe,wBAAuB,SAAS,aAAW,aAAY,cAAc;AAApJ,IAA8J,IAAE,OAAO;AAAvK,IAAyK,IAAE,oBAAI;AAAQ,IAAM,IAAN,MAAO;AAAA,EAAC,YAAYA,IAAEC,IAAEC,IAAE;AAAC,QAAG,KAAK,eAAa,MAAGA,OAAI,EAAE,OAAM,MAAM,mEAAmE;AAAE,SAAK,UAAQF,IAAE,KAAK,IAAEC;AAAA,EAAC;AAAA,EAAC,IAAI,aAAY;AAAC,QAAID,KAAE,KAAK;AAAE,UAAMG,KAAE,KAAK;AAAE,QAAG,KAAG,WAASH,IAAE;AAAC,YAAME,KAAE,WAASC,MAAG,MAAIA,GAAE;AAAO,MAAAD,OAAIF,KAAE,EAAE,IAAIG,EAAC,IAAG,WAASH,QAAK,KAAK,IAAEA,KAAE,IAAI,iBAAe,YAAY,KAAK,OAAO,GAAEE,MAAG,EAAE,IAAIC,IAAEH,EAAC;AAAA,IAAE;AAAC,WAAOA;AAAA,EAAC;AAAA,EAAC,WAAU;AAAC,WAAO,KAAK;AAAA,EAAO;AAAC;AAAC,IAAM,IAAE,CAAAA,OAAG,IAAI,EAAE,YAAU,OAAOA,KAAEA,KAAEA,KAAE,IAAG,QAAO,CAAC;AAAnD,IAAqD,IAAE,CAACA,OAAKC,OAAI;AAAC,QAAMC,KAAE,MAAIF,GAAE,SAAOA,GAAE,CAAC,IAAEC,GAAE,OAAQ,CAACA,IAAEE,IAAED,OAAID,MAAG,CAAAD,OAAG;AAAC,QAAG,SAAKA,GAAE,aAAa,QAAOA,GAAE;AAAQ,QAAG,YAAU,OAAOA,GAAE,QAAOA;AAAE,UAAM,MAAM,qEAAmEA,KAAE,sFAAsF;AAAA,EAAC,GAAGG,EAAC,IAAEH,GAAEE,KAAE,CAAC,GAAGF,GAAE,CAAC,CAAC;AAAE,SAAO,IAAI,EAAEE,IAAEF,IAAE,CAAC;AAAC;AAA5Y,IAA8Y,IAAE,CAACC,IAAEE,OAAI;AAAC,MAAEF,GAAE,qBAAmBE,GAAE,IAAK,CAAAH,OAAGA,cAAa,gBAAcA,KAAEA,GAAE,UAAW,IAAEG,GAAE,QAAS,CAAAA,OAAG;AAAC,UAAMD,KAAE,SAAS,cAAc,OAAO,GAAEE,KAAE,EAAE;AAAS,eAASA,MAAGF,GAAE,aAAa,SAAQE,EAAC,GAAEF,GAAE,cAAYC,GAAE,SAAQF,GAAE,YAAYC,EAAC;AAAA,EAAC,CAAE;AAAC;AAA1nB,IAA4nB,IAAE,IAAE,CAAAF,OAAGA,KAAE,CAAAA,OAAGA,cAAa,iBAAe,CAAAA,OAAG;AAAC,MAAIC,KAAE;AAAG,aAAUE,MAAKH,GAAE,SAAS,CAAAC,MAAGE,GAAE;AAAQ,SAAO,EAAEF,EAAC;AAAC,GAAGD,EAAC,IAAEA;AAK1yC,IAAI;AAAE,IAAM,IAAE;AAAR,IAAe,IAAE,EAAE;AAAnB,IAAgC,IAAE,IAAE,EAAE,cAAY;AAAlD,IAAqD,IAAE,EAAE;AAAzD,IAAwF,IAAE,EAAC,YAAYA,IAAEC,IAAE;AAAC,UAAOA,IAAE;AAAA,IAAC,KAAK;AAAQ,MAAAD,KAAEA,KAAE,IAAE;AAAK;AAAA,IAAM,KAAK;AAAA,IAAO,KAAK;AAAM,MAAAA,KAAE,QAAMA,KAAEA,KAAE,KAAK,UAAUA,EAAC;AAAA,EAAC;AAAC,SAAOA;AAAC,GAAE,cAAcA,IAAEC,IAAE;AAAC,MAAIE,KAAEH;AAAE,UAAOC,IAAE;AAAA,IAAC,KAAK;AAAQ,MAAAE,KAAE,SAAOH;AAAE;AAAA,IAAM,KAAK;AAAO,MAAAG,KAAE,SAAOH,KAAE,OAAK,OAAOA,EAAC;AAAE;AAAA,IAAM,KAAK;AAAA,IAAO,KAAK;AAAM,UAAG;AAAC,QAAAG,KAAE,KAAK,MAAMH,EAAC;AAAA,MAAC,SAAOA,IAAE;AAAC,QAAAG,KAAE;AAAA,MAAI;AAAA,EAAC;AAAC,SAAOA;AAAC,EAAC;AAAvY,IAAyY,IAAE,CAACH,IAAEC,OAAIA,OAAID,OAAIC,MAAGA,MAAGD,MAAGA;AAAna,IAAsa,IAAE,EAAC,WAAU,MAAG,MAAK,QAAO,WAAU,GAAE,SAAQ,OAAG,YAAW,EAAC;AAAre,IAAue,IAAE;AAAY,IAAM,IAAN,cAAgB,YAAW;AAAA,EAAC,cAAa;AAAC,UAAM,GAAE,KAAK,IAAE,oBAAI,OAAI,KAAK,kBAAgB,OAAG,KAAK,aAAW,OAAG,KAAK,IAAE,MAAK,KAAK,EAAE;AAAA,EAAC;AAAA,EAAC,OAAO,eAAeA,IAAE;AAAC,QAAIC;AAAE,SAAK,SAAS,IAAG,UAAQA,KAAE,KAAK,MAAI,WAASA,KAAEA,KAAE,KAAK,IAAE,CAAC,GAAG,KAAKD,EAAC;AAAA,EAAC;AAAA,EAAC,WAAW,qBAAoB;AAAC,SAAK,SAAS;AAAE,UAAMA,KAAE,CAAC;AAAE,WAAO,KAAK,kBAAkB,QAAS,CAACC,IAAEE,OAAI;AAAC,YAAMD,KAAE,KAAK,EAAEC,IAAEF,EAAC;AAAE,iBAASC,OAAI,KAAK,EAAE,IAAIA,IAAEC,EAAC,GAAEH,GAAE,KAAKE,EAAC;AAAA,IAAE,CAAE,GAAEF;AAAA,EAAC;AAAA,EAAC,OAAO,eAAeA,IAAEC,KAAE,GAAE;AAAC,QAAGA,GAAE,UAAQA,GAAE,YAAU,QAAI,KAAK,SAAS,GAAE,KAAK,kBAAkB,IAAID,IAAEC,EAAC,GAAE,CAACA,GAAE,cAAY,CAAC,KAAK,UAAU,eAAeD,EAAC,GAAE;AAAC,YAAMG,KAAE,YAAU,OAAOH,KAAE,OAAO,IAAE,OAAKA,IAAEE,KAAE,KAAK,sBAAsBF,IAAEG,IAAEF,EAAC;AAAE,iBAASC,MAAG,OAAO,eAAe,KAAK,WAAUF,IAAEE,EAAC;AAAA,IAAC;AAAA,EAAC;AAAA,EAAC,OAAO,sBAAsBF,IAAEC,IAAEE,IAAE;AAAC,WAAM,EAAC,MAAK;AAAC,aAAO,KAAKF,EAAC;AAAA,IAAC,GAAE,IAAIC,IAAE;AAAC,YAAME,KAAE,KAAKJ,EAAC;AAAE,WAAKC,EAAC,IAAEC,IAAE,KAAK,cAAcF,IAAEI,IAAED,EAAC;AAAA,IAAC,GAAE,cAAa,MAAG,YAAW,KAAE;AAAA,EAAC;AAAA,EAAC,OAAO,mBAAmBH,IAAE;AAAC,WAAO,KAAK,kBAAkB,IAAIA,EAAC,KAAG;AAAA,EAAC;AAAA,EAAC,OAAO,WAAU;AAAC,QAAG,KAAK,eAAe,CAAC,EAAE,QAAM;AAAG,SAAK,CAAC,IAAE;AAAG,UAAMA,KAAE,OAAO,eAAe,IAAI;AAAE,QAAGA,GAAE,SAAS,GAAE,WAASA,GAAE,MAAI,KAAK,IAAE,CAAC,GAAGA,GAAE,CAAC,IAAG,KAAK,oBAAkB,IAAI,IAAIA,GAAE,iBAAiB,GAAE,KAAK,IAAE,oBAAI,OAAI,KAAK,eAAe,YAAY,GAAE;AAAC,YAAMA,KAAE,KAAK,YAAWC,KAAE,CAAC,GAAG,OAAO,oBAAoBD,EAAC,GAAE,GAAG,OAAO,sBAAsBA,EAAC,CAAC;AAAE,iBAAUG,MAAKF,GAAE,MAAK,eAAeE,IAAEH,GAAEG,EAAC,CAAC;AAAA,IAAC;AAAC,WAAO,KAAK,gBAAc,KAAK,eAAe,KAAK,MAAM,GAAE;AAAA,EAAE;AAAA,EAAC,OAAO,eAAeH,IAAE;AAAC,UAAMC,KAAE,CAAC;AAAE,QAAG,MAAM,QAAQD,EAAC,GAAE;AAAC,YAAMG,KAAE,IAAI,IAAIH,GAAE,KAAK,IAAE,CAAC,EAAE,QAAQ,CAAC;AAAE,iBAAUA,MAAKG,GAAE,CAAAF,GAAE,QAAQ,EAAED,EAAC,CAAC;AAAA,IAAC,MAAM,YAASA,MAAGC,GAAE,KAAK,EAAED,EAAC,CAAC;AAAE,WAAOC;AAAA,EAAC;AAAA,EAAC,OAAO,EAAED,IAAEC,IAAE;AAAC,UAAME,KAAEF,GAAE;AAAU,WAAM,UAAKE,KAAE,SAAO,YAAU,OAAOA,KAAEA,KAAE,YAAU,OAAOH,KAAEA,GAAE,YAAY,IAAE;AAAA,EAAM;AAAA,EAAC,IAAG;AAAC,QAAIA;AAAE,SAAK,IAAE,IAAI,QAAS,CAAAA,OAAG,KAAK,iBAAeA,EAAE,GAAE,KAAK,OAAK,oBAAI,OAAI,KAAK,EAAE,GAAE,KAAK,cAAc,GAAE,UAAQA,KAAE,KAAK,YAAY,MAAI,WAASA,MAAGA,GAAE,QAAS,CAAAA,OAAGA,GAAE,IAAI,CAAE;AAAA,EAAC;AAAA,EAAC,cAAcA,IAAE;AAAC,QAAIC,IAAEE;AAAE,KAAC,UAAQF,KAAE,KAAK,MAAI,WAASA,KAAEA,KAAE,KAAK,IAAE,CAAC,GAAG,KAAKD,EAAC,GAAE,WAAS,KAAK,cAAY,KAAK,gBAAc,UAAQG,KAAEH,GAAE,kBAAgB,WAASG,MAAGA,GAAE,KAAKH,EAAC;AAAA,EAAE;AAAA,EAAC,iBAAiBA,IAAE;AAAC,QAAIC;AAAE,cAAQA,KAAE,KAAK,MAAI,WAASA,MAAGA,GAAE,OAAO,KAAK,EAAE,QAAQD,EAAC,MAAI,GAAE,CAAC;AAAA,EAAC;AAAA,EAAC,IAAG;AAAC,SAAK,YAAY,kBAAkB,QAAS,CAACA,IAAEC,OAAI;AAAC,WAAK,eAAeA,EAAC,MAAI,KAAK,EAAE,IAAIA,IAAE,KAAKA,EAAC,CAAC,GAAE,OAAO,KAAKA,EAAC;AAAA,IAAE,CAAE;AAAA,EAAC;AAAA,EAAC,mBAAkB;AAAC,QAAID;AAAE,UAAMC,KAAE,UAAQD,KAAE,KAAK,eAAa,WAASA,KAAEA,KAAE,KAAK,aAAa,KAAK,YAAY,iBAAiB;AAAE,WAAO,EAAEC,IAAE,KAAK,YAAY,aAAa,GAAEA;AAAA,EAAC;AAAA,EAAC,oBAAmB;AAAC,QAAID;AAAE,eAAS,KAAK,eAAa,KAAK,aAAW,KAAK,iBAAiB,IAAG,KAAK,eAAe,IAAE,GAAE,UAAQA,KAAE,KAAK,MAAI,WAASA,MAAGA,GAAE,QAAS,CAAAA,OAAG;AAAC,UAAIC;AAAE,aAAO,UAAQA,KAAED,GAAE,kBAAgB,WAASC,KAAE,SAAOA,GAAE,KAAKD,EAAC;AAAA,IAAC,CAAE;AAAA,EAAC;AAAA,EAAC,eAAeA,IAAE;AAAA,EAAC;AAAA,EAAC,uBAAsB;AAAC,QAAIA;AAAE,cAAQA,KAAE,KAAK,MAAI,WAASA,MAAGA,GAAE,QAAS,CAAAA,OAAG;AAAC,UAAIC;AAAE,aAAO,UAAQA,KAAED,GAAE,qBAAmB,WAASC,KAAE,SAAOA,GAAE,KAAKD,EAAC;AAAA,IAAC,CAAE;AAAA,EAAC;AAAA,EAAC,yBAAyBA,IAAEC,IAAEE,IAAE;AAAC,SAAK,KAAKH,IAAEG,EAAC;AAAA,EAAC;AAAA,EAAC,EAAEH,IAAEC,IAAEE,KAAE,GAAE;AAAC,QAAID;AAAE,UAAME,KAAE,KAAK,YAAY,EAAEJ,IAAEG,EAAC;AAAE,QAAG,WAASC,MAAG,SAAKD,GAAE,SAAQ;AAAC,YAAME,MAAG,YAAU,UAAQH,KAAEC,GAAE,cAAY,WAASD,KAAE,SAAOA,GAAE,eAAaC,GAAE,YAAU,GAAG,YAAYF,IAAEE,GAAE,IAAI;AAAE,WAAK,IAAEH,IAAE,QAAMK,KAAE,KAAK,gBAAgBD,EAAC,IAAE,KAAK,aAAaA,IAAEC,EAAC,GAAE,KAAK,IAAE;AAAA,IAAI;AAAA,EAAC;AAAA,EAAC,KAAKL,IAAEC,IAAE;AAAC,QAAIE;AAAE,UAAMD,KAAE,KAAK,aAAYE,KAAEF,GAAE,EAAE,IAAIF,EAAC;AAAE,QAAG,WAASI,MAAG,KAAK,MAAIA,IAAE;AAAC,YAAMJ,KAAEE,GAAE,mBAAmBE,EAAC,GAAEC,KAAE,cAAY,OAAOL,GAAE,YAAU,EAAC,eAAcA,GAAE,UAAS,IAAE,YAAU,UAAQG,KAAEH,GAAE,cAAY,WAASG,KAAE,SAAOA,GAAE,iBAAeH,GAAE,YAAU;AAAE,WAAK,IAAEI,IAAE,KAAKA,EAAC,IAAEC,GAAE,cAAcJ,IAAED,GAAE,IAAI,GAAE,KAAK,IAAE;AAAA,IAAI;AAAA,EAAC;AAAA,EAAC,cAAcA,IAAEC,IAAEE,IAAE;AAAC,QAAID,KAAE;AAAG,eAASF,SAAMG,KAAEA,MAAG,KAAK,YAAY,mBAAmBH,EAAC,GAAG,cAAY,GAAG,KAAKA,EAAC,GAAEC,EAAC,KAAG,KAAK,KAAK,IAAID,EAAC,KAAG,KAAK,KAAK,IAAIA,IAAEC,EAAC,GAAE,SAAKE,GAAE,WAAS,KAAK,MAAIH,OAAI,WAAS,KAAK,MAAI,KAAK,IAAE,oBAAI,QAAK,KAAK,EAAE,IAAIA,IAAEG,EAAC,MAAID,KAAE,QAAI,CAAC,KAAK,mBAAiBA,OAAI,KAAK,IAAE,KAAK,EAAE;AAAA,EAAE;AAAA,EAAC,MAAM,IAAG;AAAC,SAAK,kBAAgB;AAAG,QAAG;AAAC,YAAM,KAAK;AAAA,IAAC,SAAOF,IAAE;AAAC,cAAQ,OAAOA,EAAC;AAAA,IAAC;AAAC,UAAMA,KAAE,KAAK,eAAe;AAAE,WAAO,QAAMA,MAAG,MAAMA,IAAE,CAAC,KAAK;AAAA,EAAe;AAAA,EAAC,iBAAgB;AAAC,WAAO,KAAK,cAAc;AAAA,EAAC;AAAA,EAAC,gBAAe;AAAC,QAAIA;AAAE,QAAG,CAAC,KAAK,gBAAgB;AAAO,SAAK,YAAW,KAAK,MAAI,KAAK,EAAE,QAAS,CAACA,IAAEC,OAAI,KAAKA,EAAC,IAAED,EAAE,GAAE,KAAK,IAAE;AAAQ,QAAIC,KAAE;AAAG,UAAME,KAAE,KAAK;AAAK,QAAG;AAAC,MAAAF,KAAE,KAAK,aAAaE,EAAC,GAAEF,MAAG,KAAK,WAAWE,EAAC,GAAE,UAAQH,KAAE,KAAK,MAAI,WAASA,MAAGA,GAAE,QAAS,CAAAA,OAAG;AAAC,YAAIC;AAAE,eAAO,UAAQA,KAAED,GAAE,eAAa,WAASC,KAAE,SAAOA,GAAE,KAAKD,EAAC;AAAA,MAAC,CAAE,GAAE,KAAK,OAAOG,EAAC,KAAG,KAAK,EAAE;AAAA,IAAC,SAAOH,IAAE;AAAC,YAAMC,KAAE,OAAG,KAAK,EAAE,GAAED;AAAA,IAAC;AAAC,IAAAC,MAAG,KAAK,KAAKE,EAAC;AAAA,EAAC;AAAA,EAAC,WAAWH,IAAE;AAAA,EAAC;AAAA,EAAC,KAAKA,IAAE;AAAC,QAAIC;AAAE,cAAQA,KAAE,KAAK,MAAI,WAASA,MAAGA,GAAE,QAAS,CAAAD,OAAG;AAAC,UAAIC;AAAE,aAAO,UAAQA,KAAED,GAAE,gBAAc,WAASC,KAAE,SAAOA,GAAE,KAAKD,EAAC;AAAA,IAAC,CAAE,GAAE,KAAK,eAAa,KAAK,aAAW,MAAG,KAAK,aAAaA,EAAC,IAAG,KAAK,QAAQA,EAAC;AAAA,EAAC;AAAA,EAAC,IAAG;AAAC,SAAK,OAAK,oBAAI,OAAI,KAAK,kBAAgB;AAAA,EAAE;AAAA,EAAC,IAAI,iBAAgB;AAAC,WAAO,KAAK,kBAAkB;AAAA,EAAC;AAAA,EAAC,oBAAmB;AAAC,WAAO,KAAK;AAAA,EAAC;AAAA,EAAC,aAAaA,IAAE;AAAC,WAAM;AAAA,EAAE;AAAA,EAAC,OAAOA,IAAE;AAAC,eAAS,KAAK,MAAI,KAAK,EAAE,QAAS,CAACA,IAAEC,OAAI,KAAK,EAAEA,IAAE,KAAKA,EAAC,GAAED,EAAC,CAAE,GAAE,KAAK,IAAE,SAAQ,KAAK,EAAE;AAAA,EAAC;AAAA,EAAC,QAAQA,IAAE;AAAA,EAAC;AAAA,EAAC,aAAaA,IAAE;AAAA,EAAC;AAAC;AAK1rK,IAAI;AAAE,EAAE,CAAC,IAAE,MAAG,EAAE,oBAAkB,oBAAI,OAAI,EAAE,gBAAc,CAAC,GAAE,EAAE,oBAAkB,EAAC,MAAK,OAAM,GAAE,QAAM,KAAG,EAAE,EAAC,iBAAgB,EAAC,CAAC,IAAG,UAAQ,IAAE,EAAE,4BAA0B,WAAS,IAAE,IAAE,EAAE,0BAAwB,CAAC,GAAG,KAAK,OAAO;AAAE,IAAM,IAAE;AAAR,IAAe,IAAE,EAAE;AAAnB,IAAgC,IAAE,IAAE,EAAE,aAAa,YAAW,EAAC,YAAW,CAAAA,OAAGA,GAAC,CAAC,IAAE;AAAjF,IAAwF,IAAE;AAA1F,IAAkG,IAAE,QAAQ,KAAK,OAAO,IAAE,IAAI,MAAM,CAAC,CAAC;AAAtI,IAA0I,IAAE,MAAI;AAAhJ,IAAkJ,IAAE,IAAI,CAAC;AAAzJ,IAA6J,IAAE;AAA/J,IAAwK,IAAE,MAAI,EAAE,cAAc,EAAE;AAAhM,IAAkM,IAAE,CAAAA,OAAG,SAAOA,MAAG,YAAU,OAAOA,MAAG,cAAY,OAAOA;AAAxP,IAA0P,IAAE,MAAM;AAAlQ,IAA0Q,IAAE,CAAAA,OAAG,EAAEA,EAAC,KAAG,cAAY,QAAO,QAAMA,KAAE,SAAOA,GAAE,OAAO,QAAQ;AAAxU,IAA2U,IAAE;AAA7U,IAA2V,IAAE;AAA7V,IAAmZ,IAAE;AAArZ,IAA4Z,IAAE;AAA9Z,IAAma,IAAE,OAAO,KAAK,CAAC,qBAAqB,CAAC,KAAK,CAAC;AAAA,2BAAsC,GAAG;AAAvf,IAAyf,IAAE;AAA3f,IAAggB,IAAE;AAAlgB,IAAugB,IAAE;AAAzgB,IAA8iB,IAAE,CAAAA,OAAG,CAACC,OAAKE,QAAK,EAAC,YAAWH,IAAE,SAAQC,IAAE,QAAOE,GAAC;AAA9lB,IAAimB,IAAE,EAAE,CAAC;AAAtmB,IAAwmB,IAAE,EAAE,CAAC;AAA7mB,IAA+mB,IAAE,OAAO,IAAI,cAAc;AAA1oB,IAA4oB,IAAE,OAAO,IAAI,aAAa;AAAtqB,IAAwqB,IAAE,oBAAI;AAA9qB,IAAsrB,IAAE,EAAE,iBAAiB,GAAE,KAAI,MAAK,KAAE;AAAxtB,IAA0tB,IAAE,CAACH,IAAEC,OAAI;AAAC,QAAME,KAAEH,GAAE,SAAO,GAAEE,KAAE,CAAC;AAAE,MAAIE,IAAEC,KAAE,MAAIJ,KAAE,UAAQ,IAAGK,KAAE;AAAE,WAAQL,KAAE,GAAEA,KAAEE,IAAEF,MAAI;AAAC,UAAME,KAAEH,GAAEC,EAAC;AAAE,QAAIM,IAAEC,IAAEC,KAAE,IAAGC,KAAE;AAAE,WAAKA,KAAEP,GAAE,WAASG,GAAE,YAAUI,IAAEF,KAAEF,GAAE,KAAKH,EAAC,GAAE,SAAOK,MAAI,CAAAE,KAAEJ,GAAE,WAAUA,OAAI,IAAE,UAAQE,GAAE,CAAC,IAAEF,KAAE,IAAE,WAASE,GAAE,CAAC,IAAEF,KAAE,IAAE,WAASE,GAAE,CAAC,KAAG,EAAE,KAAKA,GAAE,CAAC,CAAC,MAAIJ,KAAE,OAAO,OAAKI,GAAE,CAAC,GAAE,GAAG,IAAGF,KAAE,KAAG,WAASE,GAAE,CAAC,MAAIF,KAAE,KAAGA,OAAI,IAAE,QAAME,GAAE,CAAC,KAAGF,KAAE,QAAMF,KAAEA,KAAE,GAAEK,KAAE,MAAI,WAASD,GAAE,CAAC,IAAEC,KAAE,MAAIA,KAAEH,GAAE,YAAUE,GAAE,CAAC,EAAE,QAAOD,KAAEC,GAAE,CAAC,GAAEF,KAAE,WAASE,GAAE,CAAC,IAAE,IAAE,QAAMA,GAAE,CAAC,IAAE,IAAE,KAAGF,OAAI,KAAGA,OAAI,IAAEA,KAAE,IAAEA,OAAI,KAAGA,OAAI,IAAEA,KAAE,KAAGA,KAAE,GAAEF,KAAE;AAAQ,UAAMO,KAAEL,OAAI,KAAGN,GAAEC,KAAE,CAAC,EAAE,WAAW,IAAI,IAAE,MAAI;AAAG,IAAAI,MAAGC,OAAI,IAAEH,KAAE,IAAEM,MAAG,KAAGP,GAAE,KAAKK,EAAC,GAAEJ,GAAE,MAAM,GAAEM,EAAC,IAAE,IAAEN,GAAE,MAAMM,EAAC,IAAE,IAAEE,MAAGR,KAAE,KAAG,OAAKM,MAAGP,GAAE,KAAK,MAAM,GAAED,MAAGU;AAAA,EAAE;AAAC,QAAMJ,KAAEF,MAAGL,GAAEG,EAAC,KAAG,UAAQ,MAAIF,KAAE,WAAS;AAAI,MAAG,CAAC,MAAM,QAAQD,EAAC,KAAG,CAACA,GAAE,eAAe,KAAK,EAAE,OAAM,MAAM,gCAAgC;AAAE,SAAM,CAAC,WAAS,IAAE,EAAE,WAAWO,EAAC,IAAEA,IAAEL,EAAC;AAAC;AAAE,IAAM,IAAN,MAAM,GAAC;AAAA,EAAC,YAAY,EAAC,SAAQF,IAAE,YAAWC,GAAC,GAAEE,IAAE;AAAC,QAAID;AAAE,SAAK,QAAM,CAAC;AAAE,QAAIE,KAAE,GAAEC,KAAE;AAAE,UAAMC,KAAEN,GAAE,SAAO,GAAEO,KAAE,KAAK,OAAM,CAACC,IAAEC,EAAC,IAAE,EAAET,IAAEC,EAAC;AAAE,QAAG,KAAK,KAAG,GAAE,cAAcO,IAAEL,EAAC,GAAE,EAAE,cAAY,KAAK,GAAG,SAAQ,MAAIF,IAAE;AAAC,YAAMD,KAAE,KAAK,GAAG,SAAQC,KAAED,GAAE;AAAW,MAAAC,GAAE,OAAO,GAAED,GAAE,OAAO,GAAGC,GAAE,UAAU;AAAA,IAAC;AAAC,WAAK,UAAQC,KAAE,EAAE,SAAS,MAAIK,GAAE,SAAOD,MAAG;AAAC,UAAG,MAAIJ,GAAE,UAAS;AAAC,YAAGA,GAAE,cAAc,GAAE;AAAC,gBAAMF,KAAE,CAAC;AAAE,qBAAUC,MAAKC,GAAE,kBAAkB,EAAE,KAAGD,GAAE,SAAS,CAAC,KAAGA,GAAE,WAAW,CAAC,GAAE;AAAC,kBAAME,KAAEM,GAAEJ,IAAG;AAAE,gBAAGL,GAAE,KAAKC,EAAC,GAAE,WAASE,IAAE;AAAC,oBAAMH,KAAEE,GAAE,aAAaC,GAAE,YAAY,IAAE,CAAC,EAAE,MAAM,CAAC,GAAEF,KAAE,eAAe,KAAKE,EAAC;AAAE,cAAAI,GAAE,KAAK,EAAC,MAAK,GAAE,OAAMH,IAAE,MAAKH,GAAE,CAAC,GAAE,SAAQD,IAAE,MAAK,QAAMC,GAAE,CAAC,IAAE,IAAE,QAAMA,GAAE,CAAC,IAAE,KAAG,QAAMA,GAAE,CAAC,IAAE,KAAG,EAAC,CAAC;AAAA,YAAC,MAAM,CAAAM,GAAE,KAAK,EAAC,MAAK,GAAE,OAAMH,GAAC,CAAC;AAAA,UAAC;AAAC,qBAAUH,MAAKD,GAAE,CAAAE,GAAE,gBAAgBD,EAAC;AAAA,QAAC;AAAC,YAAG,EAAE,KAAKC,GAAE,OAAO,GAAE;AAAC,gBAAMF,KAAEE,GAAE,YAAY,MAAM,CAAC,GAAED,KAAED,GAAE,SAAO;AAAE,cAAGC,KAAE,GAAE;AAAC,YAAAC,GAAE,cAAY,IAAE,EAAE,cAAY;AAAG,qBAAQC,KAAE,GAAEA,KAAEF,IAAEE,KAAI,CAAAD,GAAE,OAAOF,GAAEG,EAAC,GAAE,EAAE,CAAC,GAAE,EAAE,SAAS,GAAEI,GAAE,KAAK,EAAC,MAAK,GAAE,OAAM,EAAEH,GAAC,CAAC;AAAE,YAAAF,GAAE,OAAOF,GAAEC,EAAC,GAAE,EAAE,CAAC;AAAA,UAAC;AAAA,QAAC;AAAA,MAAC,WAAS,MAAIC,GAAE,SAAS,KAAGA,GAAE,SAAO,EAAE,CAAAK,GAAE,KAAK,EAAC,MAAK,GAAE,OAAMH,GAAC,CAAC;AAAA,WAAM;AAAC,YAAIJ,KAAE;AAAG,eAAK,QAAMA,KAAEE,GAAE,KAAK,QAAQ,GAAEF,KAAE,CAAC,KAAI,CAAAO,GAAE,KAAK,EAAC,MAAK,GAAE,OAAMH,GAAC,CAAC,GAAEJ,MAAG,EAAE,SAAO;AAAA,MAAC;AAAC,MAAAI;AAAA,IAAG;AAAA,EAAC;AAAA,EAAC,OAAO,cAAcJ,IAAEC,IAAE;AAAC,UAAME,KAAE,EAAE,cAAc,UAAU;AAAE,WAAOA,GAAE,YAAUH,IAAEG;AAAA,EAAC;AAAC;AAAC,SAAS,EAAEH,IAAEC,IAAEE,KAAEH,IAAEE,IAAE;AAAC,MAAIE,IAAEC,IAAEC,IAAEC;AAAE,MAAGN,OAAI,EAAE,QAAOA;AAAE,MAAIO,KAAE,WAASN,KAAE,UAAQE,KAAED,GAAE,MAAI,WAASC,KAAE,SAAOA,GAAEF,EAAC,IAAEC,GAAE;AAAE,QAAMM,KAAE,EAAER,EAAC,IAAE,SAAOA,GAAE;AAAgB,UAAO,QAAMO,KAAE,SAAOA,GAAE,iBAAeC,OAAI,UAAQJ,KAAE,QAAMG,KAAE,SAAOA,GAAE,SAAO,WAASH,MAAGA,GAAE,KAAKG,IAAE,KAAE,GAAE,WAASC,KAAED,KAAE,UAAQA,KAAE,IAAIC,GAAET,EAAC,GAAEQ,GAAE,KAAKR,IAAEG,IAAED,EAAC,IAAG,WAASA,MAAG,UAAQI,MAAGC,KAAEJ,IAAG,MAAI,WAASG,KAAEA,KAAEC,GAAE,IAAE,CAAC,GAAGL,EAAC,IAAEM,KAAEL,GAAE,IAAEK,KAAG,WAASA,OAAIP,KAAE,EAAED,IAAEQ,GAAE,KAAKR,IAAEC,GAAE,MAAM,GAAEO,IAAEN,EAAC,IAAGD;AAAC;AAAC,IAAM,IAAN,MAAO;AAAA,EAAC,YAAYD,IAAEC,IAAE;AAAC,SAAK,OAAK,CAAC,GAAE,KAAK,OAAK,QAAO,KAAK,OAAKD,IAAE,KAAK,OAAKC;AAAA,EAAC;AAAA,EAAC,IAAI,aAAY;AAAC,WAAO,KAAK,KAAK;AAAA,EAAU;AAAA,EAAC,IAAI,OAAM;AAAC,WAAO,KAAK,KAAK;AAAA,EAAI;AAAA,EAAC,EAAED,IAAE;AAAC,QAAIC;AAAE,UAAK,EAAC,IAAG,EAAC,SAAQE,GAAC,GAAE,OAAMD,GAAC,IAAE,KAAK,MAAKE,MAAG,UAAQH,KAAE,QAAMD,KAAE,SAAOA,GAAE,kBAAgB,WAASC,KAAEA,KAAE,GAAG,WAAWE,IAAE,IAAE;AAAE,MAAE,cAAYC;AAAE,QAAIC,KAAE,EAAE,SAAS,GAAEC,KAAE,GAAEC,KAAE,GAAEC,KAAEN,GAAE,CAAC;AAAE,WAAK,WAASM,MAAG;AAAC,UAAGF,OAAIE,GAAE,OAAM;AAAC,YAAIP;AAAE,cAAIO,GAAE,OAAKP,KAAE,IAAI,EAAEI,IAAEA,GAAE,aAAY,MAAKL,EAAC,IAAE,MAAIQ,GAAE,OAAKP,KAAE,IAAIO,GAAE,KAAKH,IAAEG,GAAE,MAAKA,GAAE,SAAQ,MAAKR,EAAC,IAAE,MAAIQ,GAAE,SAAOP,KAAE,IAAI,GAAGI,IAAE,MAAKL,EAAC,IAAG,KAAK,KAAK,KAAKC,EAAC,GAAEO,KAAEN,GAAE,EAAEK,EAAC;AAAA,MAAC;AAAC,MAAAD,QAAK,QAAME,KAAE,SAAOA,GAAE,WAASH,KAAE,EAAE,SAAS,GAAEC;AAAA,IAAI;AAAC,WAAOF;AAAA,EAAC;AAAA,EAAC,EAAEJ,IAAE;AAAC,QAAIC,KAAE;AAAE,eAAUE,MAAK,KAAK,KAAK,YAASA,OAAI,WAASA,GAAE,WAASA,GAAE,KAAKH,IAAEG,IAAEF,EAAC,GAAEA,MAAGE,GAAE,QAAQ,SAAO,KAAGA,GAAE,KAAKH,GAAEC,EAAC,CAAC,IAAGA;AAAA,EAAG;AAAC;AAAC,IAAM,IAAN,MAAM,GAAC;AAAA,EAAC,YAAYD,IAAEC,IAAEE,IAAED,IAAE;AAAC,QAAIE;AAAE,SAAK,OAAK,GAAE,KAAK,OAAK,GAAE,KAAK,OAAK,QAAO,KAAK,OAAKJ,IAAE,KAAK,OAAKC,IAAE,KAAK,OAAKE,IAAE,KAAK,UAAQD,IAAE,KAAK,IAAE,UAAQE,KAAE,QAAMF,KAAE,SAAOA,GAAE,gBAAc,WAASE,MAAGA;AAAA,EAAC;AAAA,EAAC,IAAI,OAAM;AAAC,QAAIJ,IAAEC;AAAE,WAAO,UAAQA,KAAE,UAAQD,KAAE,KAAK,SAAO,WAASA,KAAE,SAAOA,GAAE,SAAO,WAASC,KAAEA,KAAE,KAAK;AAAA,EAAC;AAAA,EAAC,IAAI,aAAY;AAAC,QAAID,KAAE,KAAK,KAAK;AAAW,UAAMC,KAAE,KAAK;AAAK,WAAO,WAASA,MAAG,QAAM,QAAMD,KAAE,SAAOA,GAAE,cAAYA,KAAEC,GAAE,aAAYD;AAAA,EAAC;AAAA,EAAC,IAAI,YAAW;AAAC,WAAO,KAAK;AAAA,EAAI;AAAA,EAAC,IAAI,UAAS;AAAC,WAAO,KAAK;AAAA,EAAI;AAAA,EAAC,KAAKA,IAAEC,KAAE,MAAK;AAAC,IAAAD,KAAE,EAAE,MAAKA,IAAEC,EAAC,GAAE,EAAED,EAAC,IAAEA,OAAI,KAAG,QAAMA,MAAG,OAAKA,MAAG,KAAK,SAAO,KAAG,KAAK,KAAK,GAAE,KAAK,OAAK,KAAGA,OAAI,KAAK,QAAMA,OAAI,KAAG,KAAK,EAAEA,EAAC,IAAE,WAASA,GAAE,aAAW,KAAK,EAAEA,EAAC,IAAE,WAASA,GAAE,WAAS,KAAK,EAAEA,EAAC,IAAE,EAAEA,EAAC,IAAE,KAAK,EAAEA,EAAC,IAAE,KAAK,EAAEA,EAAC;AAAA,EAAC;AAAA,EAAC,EAAEA,IAAE;AAAC,WAAO,KAAK,KAAK,WAAW,aAAaA,IAAE,KAAK,IAAI;AAAA,EAAC;AAAA,EAAC,EAAEA,IAAE;AAAC,SAAK,SAAOA,OAAI,KAAK,KAAK,GAAE,KAAK,OAAK,KAAK,EAAEA,EAAC;AAAA,EAAE;AAAA,EAAC,EAAEA,IAAE;AAAC,SAAK,SAAO,KAAG,EAAE,KAAK,IAAI,IAAE,KAAK,KAAK,YAAY,OAAKA,KAAE,KAAK,EAAE,EAAE,eAAeA,EAAC,CAAC,GAAE,KAAK,OAAKA;AAAA,EAAC;AAAA,EAAC,EAAEA,IAAE;AAAC,QAAIC;AAAE,UAAK,EAAC,QAAOE,IAAE,YAAWD,GAAC,IAAEF,IAAEI,KAAE,YAAU,OAAOF,KAAE,KAAK,KAAKF,EAAC,KAAG,WAASE,GAAE,OAAKA,GAAE,KAAG,EAAE,cAAcA,GAAE,GAAE,KAAK,OAAO,IAAGA;AAAG,SAAI,UAAQD,KAAE,KAAK,SAAO,WAASA,KAAE,SAAOA,GAAE,UAAQG,GAAE,MAAK,KAAK,EAAED,EAAC;AAAA,SAAM;AAAC,YAAMH,KAAE,IAAI,EAAEI,IAAE,IAAI,GAAEH,KAAED,GAAE,EAAE,KAAK,OAAO;AAAE,MAAAA,GAAE,EAAEG,EAAC,GAAE,KAAK,EAAEF,EAAC,GAAE,KAAK,OAAKD;AAAA,IAAC;AAAA,EAAC;AAAA,EAAC,KAAKA,IAAE;AAAC,QAAIC,KAAE,EAAE,IAAID,GAAE,OAAO;AAAE,WAAO,WAASC,MAAG,EAAE,IAAID,GAAE,SAAQC,KAAE,IAAI,EAAED,EAAC,CAAC,GAAEC;AAAA,EAAC;AAAA,EAAC,EAAED,IAAE;AAAC,MAAE,KAAK,IAAI,MAAI,KAAK,OAAK,CAAC,GAAE,KAAK,KAAK;AAAG,UAAMC,KAAE,KAAK;AAAK,QAAIE,IAAED,KAAE;AAAE,eAAUE,MAAKJ,GAAE,CAAAE,OAAID,GAAE,SAAOA,GAAE,KAAKE,KAAE,IAAI,GAAE,KAAK,EAAE,EAAE,CAAC,GAAE,KAAK,EAAE,EAAE,CAAC,GAAE,MAAK,KAAK,OAAO,CAAC,IAAEA,KAAEF,GAAEC,EAAC,GAAEC,GAAE,KAAKC,EAAC,GAAEF;AAAI,IAAAA,KAAED,GAAE,WAAS,KAAK,KAAKE,MAAGA,GAAE,KAAK,aAAYD,EAAC,GAAED,GAAE,SAAOC;AAAA,EAAE;AAAA,EAAC,KAAKF,KAAE,KAAK,KAAK,aAAYC,IAAE;AAAC,QAAIE;AAAE,SAAI,UAAQA,KAAE,KAAK,SAAO,WAASA,MAAGA,GAAE,KAAK,MAAK,OAAG,MAAGF,EAAC,GAAED,MAAGA,OAAI,KAAK,QAAM;AAAC,YAAMC,KAAED,GAAE;AAAY,MAAAA,GAAE,OAAO,GAAEA,KAAEC;AAAA,IAAC;AAAA,EAAC;AAAA,EAAC,aAAaD,IAAE;AAAC,QAAIC;AAAE,eAAS,KAAK,SAAO,KAAK,IAAED,IAAE,UAAQC,KAAE,KAAK,SAAO,WAASA,MAAGA,GAAE,KAAK,MAAKD,EAAC;AAAA,EAAE;AAAC;AAAC,IAAM,IAAN,MAAO;AAAA,EAAC,YAAYA,IAAEC,IAAEE,IAAED,IAAEE,IAAE;AAAC,SAAK,OAAK,GAAE,KAAK,OAAK,GAAE,KAAK,OAAK,QAAO,KAAK,UAAQJ,IAAE,KAAK,OAAKC,IAAE,KAAK,OAAKC,IAAE,KAAK,UAAQE,IAAED,GAAE,SAAO,KAAG,OAAKA,GAAE,CAAC,KAAG,OAAKA,GAAE,CAAC,KAAG,KAAK,OAAK,MAAMA,GAAE,SAAO,CAAC,EAAE,KAAK,IAAI,QAAM,GAAE,KAAK,UAAQA,MAAG,KAAK,OAAK;AAAA,EAAC;AAAA,EAAC,IAAI,UAAS;AAAC,WAAO,KAAK,QAAQ;AAAA,EAAO;AAAA,EAAC,IAAI,OAAM;AAAC,WAAO,KAAK,KAAK;AAAA,EAAI;AAAA,EAAC,KAAKH,IAAEC,KAAE,MAAKE,IAAED,IAAE;AAAC,UAAME,KAAE,KAAK;AAAQ,QAAIC,KAAE;AAAG,QAAG,WAASD,GAAE,CAAAJ,KAAE,EAAE,MAAKA,IAAEC,IAAE,CAAC,GAAEI,KAAE,CAAC,EAAEL,EAAC,KAAGA,OAAI,KAAK,QAAMA,OAAI,GAAEK,OAAI,KAAK,OAAKL;AAAA,SAAO;AAAC,YAAME,KAAEF;AAAE,UAAIM,IAAEC;AAAE,WAAIP,KAAEI,GAAE,CAAC,GAAEE,KAAE,GAAEA,KAAEF,GAAE,SAAO,GAAEE,KAAI,CAAAC,KAAE,EAAE,MAAKL,GAAEC,KAAEG,EAAC,GAAEL,IAAEK,EAAC,GAAEC,OAAI,MAAIA,KAAE,KAAK,KAAKD,EAAC,IAAGD,OAAIA,KAAE,CAAC,EAAEE,EAAC,KAAGA,OAAI,KAAK,KAAKD,EAAC,IAAGC,OAAI,IAAEP,KAAE,IAAEA,OAAI,MAAIA,OAAI,QAAMO,KAAEA,KAAE,MAAIH,GAAEE,KAAE,CAAC,IAAG,KAAK,KAAKA,EAAC,IAAEC;AAAA,IAAC;AAAC,IAAAF,MAAG,CAACH,MAAG,KAAK,EAAEF,EAAC;AAAA,EAAC;AAAA,EAAC,EAAEA,IAAE;AAAC,IAAAA,OAAI,IAAE,KAAK,QAAQ,gBAAgB,KAAK,IAAI,IAAE,KAAK,QAAQ,aAAa,KAAK,MAAK,QAAMA,KAAEA,KAAE,EAAE;AAAA,EAAC;AAAC;AAAC,IAAM,IAAN,cAAgB,EAAC;AAAA,EAAC,cAAa;AAAC,UAAM,GAAG,SAAS,GAAE,KAAK,OAAK;AAAA,EAAC;AAAA,EAAC,EAAEA,IAAE;AAAC,SAAK,QAAQ,KAAK,IAAI,IAAEA,OAAI,IAAE,SAAOA;AAAA,EAAC;AAAC;AAAC,IAAM,KAAG,IAAE,EAAE,cAAY;AAAG,IAAM,KAAN,cAAiB,EAAC;AAAA,EAAC,cAAa;AAAC,UAAM,GAAG,SAAS,GAAE,KAAK,OAAK;AAAA,EAAC;AAAA,EAAC,EAAEA,IAAE;AAAC,IAAAA,MAAGA,OAAI,IAAE,KAAK,QAAQ,aAAa,KAAK,MAAK,EAAE,IAAE,KAAK,QAAQ,gBAAgB,KAAK,IAAI;AAAA,EAAC;AAAC;AAAC,IAAM,KAAN,cAAiB,EAAC;AAAA,EAAC,YAAYA,IAAEC,IAAEE,IAAED,IAAEE,IAAE;AAAC,UAAMJ,IAAEC,IAAEE,IAAED,IAAEE,EAAC,GAAE,KAAK,OAAK;AAAA,EAAC;AAAA,EAAC,KAAKJ,IAAEC,KAAE,MAAK;AAAC,QAAIE;AAAE,SAAIH,KAAE,UAAQG,KAAE,EAAE,MAAKH,IAAEC,IAAE,CAAC,MAAI,WAASE,KAAEA,KAAE,OAAK,EAAE;AAAO,UAAMD,KAAE,KAAK,MAAKE,KAAEJ,OAAI,KAAGE,OAAI,KAAGF,GAAE,YAAUE,GAAE,WAASF,GAAE,SAAOE,GAAE,QAAMF,GAAE,YAAUE,GAAE,SAAQG,KAAEL,OAAI,MAAIE,OAAI,KAAGE;AAAG,IAAAA,MAAG,KAAK,QAAQ,oBAAoB,KAAK,MAAK,MAAKF,EAAC,GAAEG,MAAG,KAAK,QAAQ,iBAAiB,KAAK,MAAK,MAAKL,EAAC,GAAE,KAAK,OAAKA;AAAA,EAAC;AAAA,EAAC,YAAYA,IAAE;AAAC,QAAIC,IAAEE;AAAE,kBAAY,OAAO,KAAK,OAAK,KAAK,KAAK,KAAK,UAAQA,KAAE,UAAQF,KAAE,KAAK,YAAU,WAASA,KAAE,SAAOA,GAAE,SAAO,WAASE,KAAEA,KAAE,KAAK,SAAQH,EAAC,IAAE,KAAK,KAAK,YAAYA,EAAC;AAAA,EAAC;AAAC;AAAC,IAAM,KAAN,MAAQ;AAAA,EAAC,YAAYA,IAAEC,IAAEE,IAAE;AAAC,SAAK,UAAQH,IAAE,KAAK,OAAK,GAAE,KAAK,OAAK,QAAO,KAAK,OAAKC,IAAE,KAAK,UAAQE;AAAA,EAAC;AAAA,EAAC,IAAI,OAAM;AAAC,WAAO,KAAK,KAAK;AAAA,EAAI;AAAA,EAAC,KAAKH,IAAE;AAAC,MAAE,MAAKA,EAAC;AAAA,EAAC;AAAC;AAAC,IAAwE,KAAG,EAAE;AAAuB,QAAM,MAAI,GAAG,GAAE,CAAC,IAAG,UAAQ,IAAE,EAAE,oBAAkB,WAAS,IAAE,IAAE,EAAE,kBAAgB,CAAC,GAAG,KAAK,OAAO;AAAE,IAAM,KAAG,CAACY,IAAEC,IAAEC,OAAI;AAAC,MAAIC,IAAEC;AAAE,QAAMC,KAAE,UAAQF,KAAE,QAAMD,KAAE,SAAOA,GAAE,iBAAe,WAASC,KAAEA,KAAEF;AAAE,MAAIK,KAAED,GAAE;AAAW,MAAG,WAASC,IAAE;AAAC,UAAMN,KAAE,UAAQI,KAAE,QAAMF,KAAE,SAAOA,GAAE,iBAAe,WAASE,KAAEA,KAAE;AAAK,IAAAC,GAAE,aAAWC,KAAE,IAAI,EAAEL,GAAE,aAAa,EAAE,GAAED,EAAC,GAAEA,IAAE,QAAO,QAAME,KAAEA,KAAE,CAAC,CAAC;AAAA,EAAC;AAAC,SAAOI,GAAE,KAAKN,EAAC,GAAEM;AAAC;AAKj4P,IAAI;AAAJ,IAAO;AAAc,IAAM,KAAN,cAAiB,EAAC;AAAA,EAAC,cAAa;AAAC,UAAM,GAAG,SAAS,GAAE,KAAK,gBAAc,EAAC,MAAK,KAAI,GAAE,KAAK,KAAG;AAAA,EAAM;AAAA,EAAC,mBAAkB;AAAC,QAAIC,IAAEC;AAAE,UAAMC,KAAE,MAAM,iBAAiB;AAAE,WAAO,UAAQF,MAAGC,KAAE,KAAK,eAAe,iBAAe,WAASD,OAAIC,GAAE,eAAaC,GAAE,aAAYA;AAAA,EAAC;AAAA,EAAC,OAAOF,IAAE;AAAC,UAAMC,KAAE,KAAK,OAAO;AAAE,SAAK,eAAa,KAAK,cAAc,cAAY,KAAK,cAAa,MAAM,OAAOD,EAAC,GAAE,KAAK,KAAG,GAAGC,IAAE,KAAK,YAAW,KAAK,aAAa;AAAA,EAAC;AAAA,EAAC,oBAAmB;AAAC,QAAID;AAAE,UAAM,kBAAkB,GAAE,UAAQA,KAAE,KAAK,OAAK,WAASA,MAAGA,GAAE,aAAa,IAAE;AAAA,EAAC;AAAA,EAAC,uBAAsB;AAAC,QAAIA;AAAE,UAAM,qBAAqB,GAAE,UAAQA,KAAE,KAAK,OAAK,WAASA,MAAGA,GAAE,aAAa,KAAE;AAAA,EAAC;AAAA,EAAC,SAAQ;AAAC,WAAO;AAAA,EAAC;AAAC;AAAC,GAAG,YAAU,MAAG,GAAG,gBAAc,MAAG,UAAQ,KAAG,WAAW,6BAA2B,WAAS,MAAI,GAAG,KAAK,YAAW,EAAC,YAAW,GAAE,CAAC;AAAE,IAAM,KAAG,WAAW;AAA0B,QAAM,MAAI,GAAG,EAAC,YAAW,GAAE,CAAC;CAAyD,UAAQ,KAAG,WAAW,uBAAqB,WAAS,KAAG,KAAG,WAAW,qBAAmB,CAAC,GAAG,KAAK,OAAO;;;AClB/gC,IAAM,aAAN,cAAyB,GAAW;AAAA,EACvC,OAAO,aAAa;AAAA,IAChB,mBAAmB,EAAE,MAAM,SAAS,OAAO,KAAK;AAAA,IAChD,WAAW,EAAE,MAAM,QAAQ,OAAO,KAAK;AAAA,IACvC,qBAAqB,EAAE,MAAM,QAAQ,OAAO,KAAK;AAAA,EACrD;AAAA,EAEA,OAAO,SAAS;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EA6UhB,cAAc;AACV,UAAM;AACN,SAAK,YAAY,CAAC;AAClB,SAAK,YAAY;AACjB,SAAK,cAAc;AACnB,SAAK,YAAY;AACjB,SAAK,oBAAoB;AACzB,SAAK,oBAAoB;AACzB,SAAK,sBAAsB;AAC3B,SAAK,oBAAoB;AACzB,SAAK,qBAAqB,KAAK,mBAAmB,KAAK,IAAI;AAC3D,SAAK,kBAAkB,KAAK,gBAAgB,KAAK,IAAI;AACrD,SAAK,gBAAgB,KAAK,cAAc,KAAK,IAAI;AACjD,SAAK,YAAY;AACjB,SAAK,iBAAiB;AAAA,EAC1B;AAAA,EAEA,qBAAqB,QAAQ;AACzB,YAAQ,QAAQ;AAAA,MACZ,KAAK;AAAiB,eAAO;AAAA,MAC7B,KAAK;AAAgB,eAAO;AAAA,MAC5B,KAAK;AAAgB,eAAO;AAAA,MAC5B;AAAqB,eAAO;AAAA,IAChC;AAAA,EACJ;AAAA,EAEA,MAAM,gBAAgBG,IAAG;AACrB,IAAAA,GAAE,eAAe;AAEjB,UAAM,kBAAkB,MAAM,OAAO,IAAI,WAAW,kBAAkB;AAEtE,SAAK,YAAY;AAAA,MACb,eAAeA,GAAE;AAAA,MACjB,eAAeA,GAAE;AAAA,MACjB,gBAAgB,gBAAgB;AAAA,MAChC,gBAAgB,gBAAgB;AAAA,MAChC,OAAO;AAAA,IACX;AAEA,WAAO,iBAAiB,aAAa,KAAK,iBAAiB,EAAE,SAAS,KAAK,CAAC;AAC5E,WAAO,iBAAiB,WAAW,KAAK,eAAe,EAAE,MAAM,MAAM,SAAS,KAAK,CAAC;AAAA,EACxF;AAAA,EAEA,gBAAgBA,IAAG;AACf,QAAI,CAAC,KAAK,UAAW;AAErB,UAAM,SAAS,KAAK,IAAIA,GAAE,UAAU,KAAK,UAAU,aAAa;AAChE,UAAM,SAAS,KAAK,IAAIA,GAAE,UAAU,KAAK,UAAU,aAAa;AAEhE,QAAI,SAAS,KAAK,SAAS,GAAG;AAC1B,WAAK,UAAU,QAAQ;AAAA,IAC3B;AAEA,UAAM,aAAa,KAAK,UAAU,kBAAkBA,GAAE,UAAU,KAAK,UAAU;AAC/E,UAAM,aAAa,KAAK,UAAU,kBAAkBA,GAAE,UAAU,KAAK,UAAU;AAE/E,WAAO,IAAI,WAAW,aAAa,YAAY,UAAU;AAAA,EAC7D;AAAA,EAEA,cAAcA,IAAG;AACb,QAAI,CAAC,KAAK,UAAW;AAErB,UAAM,aAAa,KAAK,UAAU;AAElC,WAAO,oBAAoB,aAAa,KAAK,iBAAiB,EAAE,SAAS,KAAK,CAAC;AAC/E,SAAK,YAAY;AAEjB,QAAI,YAAY;AACZ,WAAK,iBAAiB;AACtB,iBAAW,MAAM;AACb,aAAK,iBAAiB;AAAA,MAC1B,GAAG,CAAC;AAAA,IACR;AAAA,EACJ;AAAA,EAEA,mBAAmB;AACf,QAAI,KAAK,aAAa;AAClB,cAAQ,IAAI,6DAA6D;AACzE;AAAA,IACJ;AAEA,QAAI,KAAK,mBAAmB;AACxB,mBAAa,KAAK,iBAAiB;AACnC,WAAK,oBAAoB;AAAA,IAC7B;AAEA,SAAK,cAAc;AAEnB,QAAI,KAAK,WAAW;AAChB,WAAK,KAAK;AAAA,IACd,OAAO;AACH,WAAK,KAAK;AAAA,IACd;AAAA,EACJ;AAAA,EAEA,OAAO;AACH,SAAK,UAAU,OAAO,SAAS;AAC/B,SAAK,UAAU,IAAI,QAAQ;AAAA,EAC/B;AAAA,EAEA,OAAO;AACH,SAAK,UAAU,OAAO,UAAU,QAAQ;AACxC,SAAK,UAAU,IAAI,SAAS;AAAA,EAChC;AAAA,EAEA,mBAAmBA,IAAG;AAClB,QAAIA,GAAE,WAAW,KAAM;AAEvB,SAAK,cAAc;AAEnB,QAAI,KAAK,UAAU,SAAS,QAAQ,GAAG;AACnC,WAAK,UAAU,IAAI,QAAQ;AAC3B,UAAI,OAAO,KAAK;AACZ,eAAO,IAAI,WAAW,4BAA4B,QAAQ;AAAA,MAC9D;AAAA,IACJ,WAAW,KAAK,UAAU,SAAS,SAAS,GAAG;AAC3C,UAAI,OAAO,KAAK;AACZ,eAAO,IAAI,WAAW,4BAA4B,SAAS;AAAA,MAC/D;AAAA,IACJ;AAAA,EACJ;AAAA,EAEA,wBAAwB;AACpB,QAAI,KAAK,UAAW;AACpB,SAAK,UAAU,IAAI,YAAY;AAAA,EACnC;AAAA,EAEA,oBAAoB;AAChB,UAAM,kBAAkB;AACxB,SAAK,iBAAiB,gBAAgB,KAAK,kBAAkB;AAE7D,QAAI,OAAO,KAAK;AAEZ,WAAK,4BAA4B,CAAC,OAAO,EAAE,QAAQ,MAAM;AACrD,YAAI,SAAS;AACT,eAAK,sBAAuB;AAAA,YACxB,eAAe;AAAA,YACf,WAAW;AAAA,YACX,cAAc;AAAA,UAClB,EAAG,KAAK,mBAAmB,KAAK;AAAA,QACpC,OAAO;AACH,eAAK,sBAAsB;AAAA,QAC/B;AACA,aAAK,oBAAoB;AAAA,MAC7B;AACA,aAAO,IAAI,WAAW,4BAA4B,KAAK,yBAAyB;AAEhF,WAAK,oBAAoB,CAAC,OAAO,aAAa;AAC1C,gBAAQ,IAAI,4CAA4C,QAAQ;AAChE,aAAK,YAAY;AAAA,MACrB;AACA,aAAO,IAAI,WAAW,mBAAmB,KAAK,iBAAiB;AAAA,IACnE;AAAA,EACJ;AAAA,EAEA,uBAAuB;AACnB,UAAM,qBAAqB;AAC3B,SAAK,oBAAoB,gBAAgB,KAAK,kBAAkB;AAEhE,QAAI,KAAK,mBAAmB;AACxB,mBAAa,KAAK,iBAAiB;AACnC,WAAK,oBAAoB;AAAA,IAC7B;AAEA,QAAI,OAAO,KAAK;AACZ,UAAI,KAAK,2BAA2B;AAChC,eAAO,IAAI,WAAW,kCAAkC,KAAK,yBAAyB;AAAA,MAC1F;AACA,UAAI,KAAK,mBAAmB;AACxB,eAAO,IAAI,WAAW,yBAAyB,KAAK,iBAAiB;AAAA,MACzE;AAAA,IACJ;AAAA,EACJ;AAAA,EAEA,mBAAmB,SAAS;AACxB,QAAI,KAAK,eAAgB;AACzB,QAAI,OAAO,KAAK;AACZ,cAAQ,IAAI,6CAA6C,KAAK,IAAI,CAAC,EAAE;AACrE,aAAO,IAAI,WAAW,mBAAmB;AAAA,IAE7C;AAAA,EACJ;AAAA,EAEA,qBAAqB;AACjB,QAAI,KAAK,eAAgB;AACzB,QAAI,OAAO,KAAK;AACZ,cAAQ,IAAI,6CAA6C,KAAK,IAAI,CAAC,EAAE;AACrE,aAAO,IAAI,WAAW,mBAAmB;AAAA,IAC7C;AAAA,EACJ;AAAA,EAEA,MAAM,qBAAqB;AACvB,QAAI,KAAK,eAAgB;AACzB,QAAI,KAAK,mBAAmB;AACxB;AAAA,IACJ;AAEA,SAAK,oBAAoB;AAEzB,QAAI;AACA,YAAM,mBAAmB,KAAK,qBAAqB,KAAK,mBAAmB;AAC3E,UAAI,OAAO,KAAK;AACZ,cAAM,OAAO,IAAI,WAAW,sBAAsB,gBAAgB;AAAA,MACtE;AAAA,IACJ,SAAS,OAAO;AACZ,cAAQ,MAAM,yCAAyC,KAAK;AAC5D,WAAK,oBAAoB;AAAA,IAC7B;AAAA,EACJ;AAAA,EAEA,MAAM,kBAAkB;AACpB,QAAI,KAAK,eAAgB;AAEzB,QAAI;AACA,UAAI,OAAO,KAAK;AACZ,cAAM,OAAO,IAAI,WAAW,mBAAmB;AAAA,MACnD;AAAA,IACJ,SAAS,OAAO;AACZ,cAAQ,MAAM,qCAAqC,KAAK;AAAA,IAC5D;AAAA,EACJ;AAAA,EAEA,MAAM,oCAAoC;AACtC,QAAI,KAAK,eAAgB;AAEzB,QAAI;AACA,UAAI,OAAO,KAAK;AACZ,cAAM,OAAO,IAAI,WAAW,+BAA+B;AAAA,MAC/D;AAAA,IACJ,SAAS,OAAO;AACZ,cAAQ,MAAM,wDAAwD,KAAK;AAAA,IAC/E;AAAA,EACJ;AAAA,EAGA,eAAe,aAAa;AACxB,QAAI,CAAC,YAAa,QAAO;AAEzB,UAAM,SAAS;AAAA,MACX,OAAO;AAAA,MAAK,WAAW;AAAA,MACvB,QAAQ;AAAA,MAAK,WAAW;AAAA,MACxB,OAAO;AAAA,MAAK,UAAU;AAAA,MACtB,SAAS;AAAA,MACT,SAAS;AAAA,MACT,aAAa;AAAA,MACb,UAAU;AAAA,MACV,OAAO;AAAA,MACP,UAAU;AAAA,MACV,MAAM;AAAA,MAAK,QAAQ;AAAA,MAAK,QAAQ;AAAA,MAAK,SAAS;AAAA,MAC9C,MAAM;AAAA,IACV;AAEA,UAAM,OAAO,YAAY,MAAM,GAAG;AAClC,WAAO,IAAO,KAAK,IAAI,SAAO;AAAA,oCACF,OAAO,GAAG,KAAK,GAAG;AAAA,SAC7C,CAAC;AAAA,EACN;AAAA,EAEA,SAAS;AACL,UAAM,mBAAmB,KAAK,qBAAqB,KAAK,mBAAmB;AAE3E,UAAM,gBAAgB;AAAA,MAClB,QAAQ,qBAAqB;AAAA,MAC7B,MAAM,qBAAqB;AAAA,IAC/B;AACA,UAAM,eAAe,qBAAqB,UAAU,qBAAqB;AAEzE,WAAO;AAAA,6CAC8B,KAAK,eAAe;AAAA;AAAA,2CAEtB,OAAO,KAAK,aAAa,EAAE,OAAO,CAAAC,OAAK,cAAcA,EAAC,CAAC,EAAE,KAAK,GAAG,CAAC;AAAA,6BAChF,KAAK,kBAAkB;AAAA,gCACpB,KAAK,iBAAiB;AAAA;AAAA,sBAEhC,KAAK,oBACD;AAAA;AAAA;AAAA;AAAA,4BAKA;AAAA;AAAA,mEAEyC,gBAAgB;AAAA;AAAA;AAAA,kCAGjD,eACI;AAAA;AAAA;AAAA;AAAA,wCAKA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,qCAMD;AAAA;AAAA,yBAEZ;AAAA;AAAA;AAAA,gEAGuC,MAAM,KAAK,gBAAgB,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,0BAKlE,KAAK,eAAe,KAAK,UAAU,QAAQ,CAAC;AAAA;AAAA;AAAA;AAAA,qDAIjB,MAAM,KAAK,kCAAkC,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,0BAKzE,KAAK,eAAe,KAAK,UAAU,gBAAgB,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,kCAM5C,CAACD,OAAM,KAAK,mBAAmBA,GAAE,aAAa,CAAC;AAAA,kCAC/C,MAAM,KAAK,mBAAmB,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAU7D;AACJ;AAEA,eAAe,OAAO,eAAe,UAAU;;;ACpqBxC,IAAM,eAAN,cAA2B,GAAW;AAAA;AAAA,EAEzC,OAAO,aAAa;AAAA,IAChB,WAAW,EAAE,MAAM,OAAO;AAAA,IAC1B,WAAW,EAAE,MAAM,OAAO;AAAA,IAC1B,aAAa,EAAE,MAAM,OAAO;AAAA,IAC5B,aAAa,EAAE,MAAM,OAAO;AAAA,IAC5B,WAAW,EAAE,MAAM,QAAQ;AAAA,IAC3B,cAAc,EAAE,MAAM,OAAO;AAAA,IAC7B,gBAAgB,EAAE,MAAM,OAAO;AAAA,IAC/B,WAAW,EAAE,MAAM,QAAQ,OAAO,KAAK;AAAA,IACvC,kBAAkB,EAAE,MAAM,OAAO,OAAO,KAAK;AAAA,IAC7C,kBAAkB,EAAE,MAAM,OAAO,OAAO,KAAK;AAAA,IAC7C,kBAAkB,EAAE,MAAM,QAAQ,OAAO,KAAK;AAAA,IAC9C,kBAAkB,EAAE,MAAM,QAAQ,OAAO,KAAK;AAAA,IAC9C,cAAc,EAAE,MAAM,QAAQ,OAAO,KAAK;AAAA,IAC1C,iBAAiB,EAAE,MAAM,QAAQ,OAAO,KAAK;AAAA,IAC7C,iBAAiB,EAAE,MAAM,QAAQ,OAAO,KAAK;AAAA,IAC7C,yBAAyB,EAAE,MAAM,QAAQ,OAAO,KAAK;AAAA,IACrD,cAAc,EAAE,MAAM,SAAS;AAAA,IAC/B,UAAU,EAAE,MAAM,OAAO;AAAA,IACzB,UAAU,EAAE,MAAM,OAAO;AAAA,EAC7B;AAAA;AAAA,EAGA,OAAO,SAAS;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EA2ShB,cAAc;AACV,UAAM;AACN,SAAK,YAAY;AACjB,SAAK,eAAe;AACpB,SAAK,iBAAiB;AACtB,SAAK,mBAAmB;AAExB,SAAK,YAAY;AACjB,SAAK,YAAY;AACjB,SAAK,cAAc;AACnB,SAAK,cAAc;AACnB,SAAK,YAAY,EAAE,KAAK,CAAC,GAAG,KAAK,CAAC,EAAE;AAEpC,SAAK,mBAAmB,CAAC;AACzB,SAAK,mBAAmB,CAAC;AACzB,SAAK,mBAAmB;AACxB,SAAK,mBAAmB;AACxB,SAAK,eAAe,EAAE,WAAW,OAAO,SAAS,MAAM;AACvD,SAAK,kBAAkB;AACvB,SAAK,kBAAkB;AACvB,SAAK,0BAA0B,CAAC;AAChC,SAAK,eAAe,MAAM;AAAA,IAAC;AAC3B,SAAK,WAAW;AAChB,SAAK,WAAW;AAGhB,SAAK,mBAAmB,oBAAI,IAAI;AAChC,SAAK,oBAAoB,oBAAI,IAAI;AACjC,SAAK,kBAAkB;AACvB,SAAK,kBAAkB,KAAK,IAAI;AAChC,SAAK,aAAa;AAClB,SAAK,aAAa;AAClB,SAAK,iBAAiB;AAGtB,SAAK,iBAAiB,CAAC;AACvB,SAAK,0BAA0B;AAC/B,SAAK,eAAe;AACpB,SAAK,mBAAmB;AAAA,MACpB,iBAAiB;AAAA,MACjB,sBAAsB;AAAA,MACtB,kBAAkB;AAAA,MAClB,UAAU;AAAA,MACV,qBAAqB;AAAA,IACzB;AAGA,SAAK,aAAa;AAClB,SAAK,mBAAmB;AAGxB,SAAK,cAAc;AAAA,MACf,SAAS;AAAA,MACT,YAAY;AAAA,MACZ,YAAY;AAAA;AAAA,MACZ,WAAW;AAAA,MACX,qBAAqB;AAAA,MACrB,aAAa;AAAA,IACjB;AAGA,SAAK,qBAAqB;AAC1B,SAAK,mBAAmB;AAGxB,SAAK,iBAAiB,KAAK,eAAe,KAAK,IAAI;AACnD,SAAK,eAAe,KAAK,aAAa,KAAK,IAAI;AAC/C,SAAK,cAAc,KAAK,YAAY,KAAK,IAAI;AAC7C,SAAK,qBAAqB,KAAK,mBAAmB,KAAK,IAAI;AAC3D,SAAK,uBAAuB,KAAK,qBAAqB,KAAK,IAAI;AAC/D,SAAK,0BAA0B,KAAK,wBAAwB,KAAK,IAAI;AACrE,SAAK,0BAA0B,KAAK,wBAAwB,KAAK,IAAI;AACrE,SAAK,uBAAuB,KAAK,qBAAqB,KAAK,IAAI;AAC/D,SAAK,sBAAsB,KAAK,oBAAoB,KAAK,IAAI;AAC7D,SAAK,uBAAuB,KAAK,qBAAqB,KAAK,IAAI;AAC/D,SAAK,aAAa,KAAK,WAAW,KAAK,IAAI;AAC3C,SAAK,cAAc,KAAK,YAAY,KAAK,IAAI;AAAA,EACjD;AAAA,EAEA,QAAQ,mBAAmB;AACvB,UAAM,QAAQ,iBAAiB;AAC/B,SAAK,cAAc,IAAI,YAAY,mBAAmB,EAAE,SAAS,MAAM,UAAU,KAAK,CAAC,CAAC;AAAA,EAC5F;AAAA,EAEA,QAAQ;AACJ,SAAK,SAAS;AACd,SAAK,YAAY;AACjB,SAAK,eAAe;AACpB,SAAK,kBAAkB;AACvB,SAAK,mBAAmB;AACxB,SAAK,cAAc;AAAA,EACvB;AAAA,EAEA,aAAa;AACT,QAAI,KAAK,cAAc;AACnB,WAAK,aAAa;AAAA,IACtB;AAAA,EACJ;AAAA,EAEA,MAAM,qBAAqB;AACvB,QAAI,CAAC,OAAO,KAAK,aAAc;AAE/B,QAAI;AACA,YAAM,CAAC,QAAQ,YAAY,IAAI,MAAM,QAAQ,IAAI;AAAA,QAC7C,OAAO,IAAI,aAAa,kBAAkB;AAAA,QAC1C,OAAO,IAAI,aAAa,gBAAgB;AAAA,MAC5C,CAAC;AAED,YAAM,eAAe,CAAC;AACtB,YAAM,eAAe,CAAC;AAEtB,iBAAW,MAAM,QAAQ;AAErB,YAAI,GAAG,SAAS,QAAQ,EAAG;AAC3B,cAAM,eAAe,OAAO,EAAE,EAAE,UAAU,SAAS,KAAK,OAAO;AAC/D,cAAM,eAAe,OAAO,EAAE,EAAE,UAAU,SAAS,KAAK,OAAO;AAE/D,YAAI,cAAc;AACd,uBAAa,KAAK,EAAE,IAAI,MAAM,OAAO,EAAE,EAAE,KAAK,CAAC;AAAA,QACnD;AACA,YAAI,cAAc;AACd,uBAAa,KAAK,EAAE,IAAI,MAAM,OAAO,EAAE,EAAE,KAAK,CAAC;AAAA,QACnD;AAAA,MACJ;AAEA,WAAK,YAAY,EAAE,KAAK,cAAc,KAAK,aAAa;AAGxD,UAAI,aAAa,SAAS,EAAG,MAAK,cAAc,aAAa,CAAC,EAAE;AAChE,UAAI,aAAa,SAAS,EAAG,MAAK,cAAc,aAAa,CAAC,EAAE;AAGhE,UAAI,cAAc,SAAS;AACvB,aAAK,eAAe;AAAA,UAChB,WAAW,aAAa;AAAA,UACxB,SAAS,aAAa;AAAA,QAC1B;AAGA,YAAI,aAAa,SAAS;AACtB,gBAAM,KAAK,qBAAqB;AAAA,QACpC;AAAA,MACJ;AAEA,WAAK,cAAc;AAAA,IACvB,SAAS,OAAO;AACZ,cAAQ,MAAM,kDAAkD,KAAK;AAAA,IACzE;AAAA,EACJ;AAAA,EAEA,MAAM,gBAAgBE,IAAG;AACrB,QAAIA,GAAE,OAAO,YAAY,WAAWA,GAAE,OAAO,YAAY,YAAYA,GAAE,OAAO,YAAY,UAAU;AAChG;AAAA,IACJ;AAEA,IAAAA,GAAE,eAAe;AAEjB,QAAI,CAAC,OAAO,KAAK,aAAc;AAC/B,UAAM,kBAAkB,MAAM,OAAO,IAAI,aAAa,kBAAkB;AAExE,SAAK,YAAY;AAAA,MACb,eAAeA,GAAE;AAAA,MACjB,eAAeA,GAAE;AAAA,MACjB,gBAAgB,gBAAgB;AAAA,MAChC,gBAAgB,gBAAgB;AAAA,MAChC,OAAO;AAAA,IACX;AAEA,WAAO,iBAAiB,aAAa,KAAK,eAAe;AACzD,WAAO,iBAAiB,WAAW,KAAK,eAAe,EAAE,MAAM,KAAK,CAAC;AAAA,EACzE;AAAA,EAEA,gBAAgBA,IAAG;AACf,QAAI,CAAC,KAAK,UAAW;AAErB,UAAM,SAAS,KAAK,IAAIA,GAAE,UAAU,KAAK,UAAU,aAAa;AAChE,UAAM,SAAS,KAAK,IAAIA,GAAE,UAAU,KAAK,UAAU,aAAa;AAEhE,QAAI,SAAS,KAAK,SAAS,GAAG;AAC1B,WAAK,UAAU,QAAQ;AAAA,IAC3B;AAEA,UAAM,aAAa,KAAK,UAAU,kBAAkBA,GAAE,UAAU,KAAK,UAAU;AAC/E,UAAM,aAAa,KAAK,UAAU,kBAAkBA,GAAE,UAAU,KAAK,UAAU;AAE/E,QAAI,OAAO,KAAK,cAAc;AAC1B,aAAO,IAAI,aAAa,aAAa,YAAY,UAAU;AAAA,IAC/D;AAAA,EACJ;AAAA,EAEA,cAAcA,IAAG;AACb,QAAI,CAAC,KAAK,UAAW;AAErB,UAAM,aAAa,KAAK,UAAU;AAElC,WAAO,oBAAoB,aAAa,KAAK,eAAe;AAC5D,SAAK,YAAY;AAEjB,QAAI,YAAY;AACZ,WAAK,iBAAiB;AACtB,iBAAW,MAAM;AACb,aAAK,iBAAiB;AAAA,MAC1B,GAAG,GAAG;AAAA,IACV;AAAA,EACJ;AAAA,EAEA,YAAYA,IAAG;AACX,SAAK,SAASA,GAAE,OAAO;AACvB,SAAK,cAAc;AACnB,YAAQ,IAAI,kBAAkB,KAAK,QAAQ,UAAU,GAAG,OAAO;AAE/D,SAAK,cAAc;AACnB,SAAK,eAAe,KAAK,MAAM;AAC3B,YAAM,aAAa,KAAK,YAAY,cAAc,eAAe;AACjE,UAAI,cAAc,KAAK,gBAAgB;AACnC,mBAAW,MAAM;AAAA,MACrB;AAAA,IACJ,CAAC;AAAA,EACL;AAAA,EAEA,gBAAgB;AACZ,SAAK,eAAe;AACpB,SAAK,iBAAiB;AACtB,SAAK,mBAAmB;AACxB,SAAK,WAAW;AAChB,SAAK,WAAW;AAAA,EACpB;AAAA,EAEA,qBAAqBA,IAAG;AACpB,SAAK,mBAAmBA,GAAE,OAAO;AACjC,SAAK,cAAc;AACnB,YAAQ,IAAI,wBAAwB,KAAK,gBAAgB;AACzD,SAAK,cAAc;AAAA,EACvB;AAAA,EAEA,MAAM,wBAAwBA,IAAG,YAAY;AACzC,UAAM,cAAc,cAAcA,GAAE,OAAO;AAC3C,QAAI,gBAAgB,KAAK,YAAa;AAGtC,SAAK,2BAA2B;AAEhC,SAAK,cAAc;AACnB,SAAK,eAAe;AACpB,SAAK,iBAAiB;AAEtB,QAAI,CAAC,UAAU,QAAQ,EAAE,SAAS,KAAK,WAAW,GAAG;AACjD,WAAK,cAAc,KAAK;AAAA,IAC5B;AAGA,SAAK,aAAa;AAElB,QAAI,KAAK,gBAAgB,UAAU;AAC/B,cAAQ,IAAI,0DAA0D;AACtE,YAAM,KAAK,4BAA4B;AAEvC,WAAK,uBAAuB;AAAA,IAChC,OAAO;AACH,WAAK,uBAAuB,QAAQ,8BAA8B;AAElE,WAAK,sBAAsB;AAAA,IAC/B;AAEA,SAAK,cAAc;AAAA,EACvB;AAAA,EAEA,MAAM,8BAA8B;AAChC,QAAI;AAEA,YAAM,KAAK,yBAAyB;AAAA,IACxC,SAAS,OAAO;AACZ,cAAQ,MAAM,oDAAoD,MAAM,OAAO;AAE/E,UAAI,KAAK,aAAa,KAAK,YAAY;AACnC,cAAM,QAAQ,KAAK,iBAAiB,KAAK,IAAI,GAAG,KAAK,UAAU;AAC/D,gBAAQ,IAAI,gDAAgD,KAAK,eAAe,KAAK,aAAa,CAAC,IAAI,KAAK,UAAU,GAAG;AAEzH,aAAK;AAGL,cAAM,IAAI,QAAQ,aAAW;AACzB,gBAAM,iBAAiB,WAAW,MAAM;AACpC,iBAAK,4BAA4B;AACjC,oBAAQ;AAAA,UACZ,GAAG,KAAK;AAGR,eAAK,kBAAkB,IAAI,SAAS,KAAK,UAAU,IAAI,cAAc;AAAA,QACzE,CAAC;AAAA,MACL,OAAO;AACH,aAAK,uBAAuB,UAAU,2BAA2B,KAAK,UAAU,WAAW;AAAA,MAC/F;AAAA,IACJ;AAAA,EACJ;AAAA,EAEA,MAAM,2BAA2B;AAC7B,UAAM,KAAK,oBAAoB;AAAA,EACnC;AAAA,EAEA,6BAA6B;AACzB,YAAQ,IAAI,6BAA6B,KAAK,iBAAiB,IAAI,0BAA0B,KAAK,eAAe,MAAM,oBAAoB;AAG3I,eAAW,CAAC,eAAe,SAAS,KAAK,KAAK,kBAAkB;AAC5D,WAAK,iBAAiB,aAAa;AAAA,IACvC;AAGA,eAAW,YAAY,KAAK,gBAAgB;AACxC,eAAS,OAAO,IAAI,MAAM,aAAa,SAAS,IAAI,2BAA2B,CAAC;AAAA,IACpF;AACA,SAAK,eAAe,SAAS;AAG7B,eAAW,CAAC,WAAW,OAAO,KAAK,KAAK,mBAAmB;AACvD,mBAAa,OAAO;AAAA,IACxB;AACA,SAAK,kBAAkB,MAAM;AAAA,EACjC;AAAA;AAAA;AAAA;AAAA,EAKA,sBAAsB;AAClB,WAAO;AAAA,MACH,GAAG,KAAK;AAAA,MACR,kBAAkB,KAAK,iBAAiB;AAAA,MACxC,kBAAkB,KAAK,eAAe;AAAA,MACtC,aACI,KAAK,iBAAiB,kBAAkB,IACjC,KAAK,iBAAiB,uBAAuB,KAAK,iBAAiB,kBAAmB,MACvF;AAAA,IACd;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA,EAKA,gCAAgC;AAC5B,UAAM,UAAU,KAAK,oBAAoB;AAGzC,QAAI,QAAQ,cAAc,MAAM,KAAK,0BAA0B,GAAG;AAC9D,WAAK,0BAA0B,KAAK,IAAI,GAAG,KAAK,0BAA0B,CAAC;AAC3E,cAAQ;AAAA,QACJ,uDAAuD,KAAK,uBAAuB,mBAAmB,QAAQ,YAAY,QAAQ,CAAC,CAAC;AAAA,MACxI;AAAA,IACJ;AAGA,QAAI,QAAQ,cAAc,MAAM,QAAQ,sBAAsB,OAAQ,KAAK,0BAA0B,GAAG;AACpG,WAAK;AACL,cAAQ,IAAI,yDAAyD,KAAK,uBAAuB,EAAE;AAAA,IACvG;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA,EAKA,yBAAyB;AACrB,QAAI,KAAK,YAAY,QAAS;AAE9B,SAAK,YAAY,UAAU;AAC3B,SAAK,YAAY,aAAa,YAAY,MAAM;AAC5C,WAAK,oBAAoB;AAAA,IAC7B,GAAG,KAAK,YAAY,UAAU;AAE9B,YAAQ,IAAI,uDAAuD,KAAK,YAAY,UAAU,KAAK;AAAA,EACvG;AAAA,EAEA,wBAAwB;AACpB,QAAI,CAAC,KAAK,YAAY,QAAS;AAE/B,SAAK,YAAY,UAAU;AAC3B,QAAI,KAAK,YAAY,YAAY;AAC7B,oBAAc,KAAK,YAAY,UAAU;AACzC,WAAK,YAAY,aAAa;AAAA,IAClC;AAEA,YAAQ,IAAI,0CAA0C;AAAA,EAC1D;AAAA,EAEA,MAAM,sBAAsB;AAExB,QAAI,KAAK,gBAAgB,YAAY,KAAK,oBAAoB,cAAc;AACxE;AAAA,IACJ;AAEA,UAAM,MAAM,KAAK,IAAI;AACrB,SAAK,YAAY,YAAY;AAE7B,QAAI;AAEA,YAAM,YAAY,MAAM,KAAK;AAAA,QACzB;AAAA,QACA,YAAY;AACR,cAAI,CAAC,OAAO,KAAK,aAAc,QAAO;AACtC,gBAAM,SAAS,MAAM,OAAO,IAAI,aAAa,gBAAgB;AAC7D,iBAAO,QAAQ,WAAW,QAAQ;AAAA,QACtC;AAAA,QACA,EAAE,SAAS,KAAM,UAAU,MAAM;AAAA,MACrC;AAEA,UAAI,WAAW;AACX,aAAK,YAAY,sBAAsB;AAGvC,YAAI,KAAK,oBAAoB,UAAU;AACnC,eAAK,uBAAuB,aAAa,wBAAwB;AAAA,QACrE;AAAA,MACJ,OAAO;AACH,aAAK,0BAA0B;AAAA,MACnC;AAGA,WAAK,8BAA8B;AAAA,IACvC,SAAS,OAAO;AACZ,cAAQ,KAAK,uCAAuC,MAAM,OAAO;AACjE,WAAK,0BAA0B;AAAA,IACnC;AAAA,EACJ;AAAA,EAEA,4BAA4B;AACxB,SAAK,YAAY;AAEjB,QAAI,KAAK,YAAY,uBAAuB,KAAK,YAAY,aAAa;AACtE,cAAQ,KAAK,sCAAsC,KAAK,YAAY,mBAAmB,iCAAiC;AACxH,WAAK,uBAAuB,UAAU,6BAA6B;AAGnE,WAAK,YAAY,aAAa,KAAK,IAAI,KAAO,KAAK,YAAY,aAAa,CAAC;AAC7E,WAAK,yBAAyB;AAAA,IAClC;AAAA,EACJ;AAAA,EAEA,2BAA2B;AACvB,SAAK,sBAAsB;AAC3B,SAAK,uBAAuB;AAAA,EAChC;AAAA;AAAA;AAAA;AAAA,EAKA,kBAAkB;AACd,WAAO;AAAA,MACH,YAAY;AAAA,QACR,OAAO,KAAK;AAAA,QACZ,iBAAiB,KAAK;AAAA,QACtB,qBAAqB,KAAK,IAAI,IAAI,KAAK;AAAA,MAC3C;AAAA,MACA,YAAY,KAAK,oBAAoB;AAAA,MACrC,QAAQ;AAAA,QACJ,SAAS,KAAK,YAAY;AAAA,QAC1B,WAAW,KAAK,YAAY;AAAA,QAC5B,oBAAoB,KAAK,YAAY,YAAY,IAAI,KAAK,IAAI,IAAI,KAAK,YAAY,YAAY;AAAA,QAC/F,qBAAqB,KAAK,YAAY;AAAA,QACtC,YAAY,KAAK,YAAY;AAAA,MACjC;AAAA,MACA,QAAQ;AAAA,QACJ,UAAU,KAAK;AAAA,QACf,QAAQ,KAAK;AAAA,QACb,eAAe,KAAK;AAAA,MACxB;AAAA,IACJ;AAAA,EACJ;AAAA,EAEA,MAAM,wBAAwBA,IAAG,YAAY;AACzC,UAAM,cAAc,cAAcA,GAAE,OAAO;AAC3C,QAAI,gBAAgB,KAAK,YAAa;AAEtC,SAAK,cAAc;AACnB,SAAK,eAAe;AACpB,SAAK,iBAAiB;AAEtB,QAAI,KAAK,gBAAgB,UAAU;AAC/B,cAAQ,KAAK,4FAA4F;AACzG,WAAK,WAAW;AAChB,WAAK,mBAAmB,KAAK,IAAI;AAGjC,YAAM,kBAAkB,KAAK,UAAU,IAAI,KAAK,CAAAC,OAAKA,GAAE,OAAO,SAAS;AACvE,UAAI,iBAAiB;AACjB,aAAK,cAAc;AACnB,gBAAQ,IAAI,8CAA8C;AAAA,MAC9D;AAAA,IACJ;AAEA,SAAK,cAAc;AAAA,EACvB;AAAA;AAAA;AAAA;AAAA,EAKA,MAAM,kBAAkB,eAAe,WAAW,UAAU,CAAC,GAAG;AAC5D,UAAM,cAAc,GAAG,aAAa,IAAI,KAAK,IAAI,CAAC,IAAI,KAAK,OAAO,EAAE,SAAS,EAAE,EAAE,OAAO,GAAG,CAAC,CAAC;AAC7F,UAAM,UAAU,QAAQ,WAAW,KAAK;AACxC,UAAM,WAAW,QAAQ,YAAY;AAGrC,QAAI,KAAK,iBAAiB,QAAQ,KAAK,yBAAyB;AAC5D,UAAI,KAAK,eAAe,UAAU,KAAK,cAAc;AACjD,cAAM,IAAI,MAAM,yBAAyB,KAAK,YAAY,gBAAgB,aAAa,EAAE;AAAA,MAC7F;AAEA,cAAQ,IAAI,oCAAoC,aAAa,KAAK,KAAK,iBAAiB,IAAI,UAAU;AACtG,aAAO,KAAK,gBAAgB,aAAa,eAAe,WAAW,OAAO;AAAA,IAC9E;AAEA,WAAO,KAAK,oBAAoB,aAAa,eAAe,WAAW,OAAO;AAAA,EAClF;AAAA,EAEA,MAAM,gBAAgB,aAAa,eAAe,WAAW,SAAS;AAClE,WAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACpC,YAAM,kBAAkB;AAAA,QACpB,IAAI;AAAA,QACJ,MAAM;AAAA,QACN;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA,UAAU,KAAK,IAAI;AAAA,QACnB,UAAU,QAAQ,YAAY;AAAA,MAClC;AAGA,UAAI,QAAQ,aAAa,QAAQ;AAC7B,aAAK,eAAe,QAAQ,eAAe;AAAA,MAC/C,OAAO;AACH,aAAK,eAAe,KAAK,eAAe;AAAA,MAC5C;AAEA,cAAQ,IAAI,yBAAyB,aAAa,iBAAiB,KAAK,eAAe,MAAM,GAAG;AAAA,IACpG,CAAC;AAAA,EACL;AAAA,EAEA,MAAM,oBAAoB,aAAa,eAAe,WAAW,SAAS;AACtE,UAAM,YAAY,KAAK,IAAI;AAC3B,SAAK,iBAAiB;AAGtB,QAAI,KAAK,iBAAiB,IAAI,aAAa,GAAG;AAC1C,cAAQ,IAAI,4BAA4B,aAAa,2CAA2C;AAChG,WAAK,iBAAiB,aAAa;AAAA,IACvC;AAGA,UAAM,sBAAsB,IAAI,QAAQ,CAACC,IAAG,WAAW;AACnD,YAAM,YAAY,WAAW,MAAM;AAC/B,aAAK,iBAAiB;AACtB,eAAO,IAAI,MAAM,aAAa,aAAa,kBAAkB,OAAO,IAAI,CAAC;AAAA,MAC7E,GAAG,OAAO;AAEV,WAAK,kBAAkB,IAAI,aAAa,SAAS;AAAA,IACrD,CAAC;AAED,UAAM,mBAAmB,QAAQ,KAAK,CAAC,UAAU,GAAG,mBAAmB,CAAC;AAExE,SAAK,iBAAiB,IAAI,eAAe;AAAA,MACrC,IAAI;AAAA,MACJ,SAAS;AAAA,MACT;AAAA,IACJ,CAAC;AAED,QAAI;AACA,YAAM,SAAS,MAAM;AACrB,WAAK,wBAAwB,SAAS;AACtC,aAAO;AAAA,IACX,SAAS,OAAO;AACZ,WAAK,wBAAwB,OAAO,aAAa;AACjD,YAAM;AAAA,IACV,UAAE;AACE,WAAK,kBAAkB,aAAa,aAAa;AACjD,WAAK,cAAc;AAAA,IACvB;AAAA,EACJ;AAAA,EAEA,wBAAwB,WAAW;AAC/B,SAAK,iBAAiB;AACtB,UAAM,eAAe,KAAK,IAAI,IAAI;AAClC,SAAK,2BAA2B,YAAY;AAAA,EAChD;AAAA,EAEA,wBAAwB,OAAO,eAAe;AAC1C,SAAK,iBAAiB;AAEtB,QAAI,MAAM,QAAQ,SAAS,SAAS,GAAG;AACnC,cAAQ,MAAM,4BAA4B,aAAa,YAAY;AACnE,WAAK,uBAAuB,UAAU,YAAY,MAAM,OAAO,EAAE;AAAA,IACrE;AAAA,EACJ;AAAA,EAEA,2BAA2B,cAAc;AACrC,UAAM,WAAW,KAAK,iBAAiB;AACvC,SAAK,iBAAiB,uBAAuB,KAAK,iBAAiB,uBAAuB,WAAW,KAAK,gBAAgB;AAAA,EAC9H;AAAA,EAEA,MAAM,gBAAgB;AAClB,QAAI,KAAK,eAAe,WAAW,KAAK,KAAK,iBAAiB,QAAQ,KAAK,yBAAyB;AAChG;AAAA,IACJ;AAEA,UAAM,WAAW,KAAK,eAAe,MAAM;AAC3C,QAAI,CAAC,SAAU;AAEf,UAAM,YAAY,KAAK,IAAI,IAAI,SAAS;AACxC,YAAQ,IAAI,8CAA8C,SAAS,IAAI,YAAY,SAAS,KAAK;AAEjG,QAAI;AACA,YAAM,SAAS,MAAM,KAAK;AAAA,QACtB,SAAS;AAAA,QACT,SAAS;AAAA,QACT,SAAS;AAAA,QACT,SAAS,QAAQ,WAAW,KAAK;AAAA,MACrC;AACA,eAAS,QAAQ,MAAM;AAAA,IAC3B,SAAS,OAAO;AACZ,eAAS,OAAO,KAAK;AAAA,IACzB;AAAA,EACJ;AAAA,EAEA,iBAAiB,eAAe;AAC5B,UAAM,YAAY,KAAK,iBAAiB,IAAI,aAAa;AACzD,QAAI,WAAW;AACX,WAAK,kBAAkB,UAAU,IAAI,aAAa;AAClD,cAAQ,IAAI,uCAAuC,aAAa,EAAE;AAAA,IACtE;AAAA,EACJ;AAAA,EAEA,kBAAkB,aAAa,eAAe;AAC1C,QAAI,KAAK,kBAAkB,IAAI,WAAW,GAAG;AACzC,mBAAa,KAAK,kBAAkB,IAAI,WAAW,CAAC;AACpD,WAAK,kBAAkB,OAAO,WAAW;AAAA,IAC7C;AACA,SAAK,iBAAiB,OAAO,aAAa;AAAA,EAC9C;AAAA,EAEA,uBAAuB,UAAU,SAAS,IAAI;AAC1C,QAAI,KAAK,oBAAoB,UAAU;AACnC,cAAQ,IAAI,oCAAoC,KAAK,eAAe,OAAO,QAAQ,KAAK,MAAM,GAAG;AACjG,WAAK,kBAAkB;AACvB,WAAK,kBAAkB,KAAK,IAAI;AAGhC,WAAK,mBAAmB,UAAU,MAAM;AAAA,IAC5C;AAAA,EACJ;AAAA,EAEA,mBAAmB,OAAO,QAAQ;AAC9B,YAAQ,OAAO;AAAA,MACX,KAAK;AACD,aAAK,kBAAkB;AACvB,aAAK,kBAAkB;AACvB;AAAA,MACJ,KAAK;AACD,aAAK,eAAe,UAAU;AAC9B,aAAK,kBAAkB;AACvB,aAAK,kBAAkB;AACvB,aAAK,mBAAmB,KAAK,IAAI;AACjC;AAAA,MACJ,KAAK;AACD,aAAK,kBAAkB;AACvB,aAAK,kBAAkB;AACvB;AAAA,MACJ,KAAK;AACD,aAAK,eAAe,EAAE,WAAW,OAAO,SAAS,MAAM;AACvD;AAAA,IACR;AACA,SAAK,cAAc;AAAA,EACvB;AAAA,EAEA,MAAM,sBAAsB;AACxB,QAAI,CAAC,OAAO,KAAK,aAAc;AAE/B,QAAI;AACA,WAAK,uBAAuB,cAAc,wBAAwB;AAElE,YAAM,SAAS,MAAM,KAAK,kBAAkB,iBAAiB,YAAY;AACrE,eAAO,MAAM,OAAO,IAAI,aAAa,gBAAgB;AAAA,MACzD,CAAC;AAED,UAAI,QAAQ,SAAS;AACjB,aAAK,eAAe;AAAA,UAChB,WAAW,OAAO;AAAA,UAClB,SAAS,OAAO;AAAA,QACpB;AAEA,aAAK,uBAAuB,aAAa,6BAA6B;AAGtE,YAAI,OAAO,SAAS;AAChB,gBAAM,KAAK,qBAAqB;AAAA,QACpC;AAAA,MACJ,OAAO;AACH,aAAK,uBAAuB,UAAU,QAAQ,SAAS,qBAAqB;AAAA,MAChF;AAAA,IACJ,SAAS,OAAO;AACZ,cAAQ,MAAM,mDAAmD,MAAM,OAAO;AAC9E,WAAK,uBAAuB,UAAU,MAAM,OAAO;AAAA,IACvD;AAAA,EACJ;AAAA,EAEA,MAAM,uBAAuB;AACzB,QAAI,CAAC,OAAO,KAAK,aAAc;AAE/B,QAAI;AACA,YAAM,SAAS,MAAM,KAAK,kBAAkB,qBAAqB,YAAY;AACzE,eAAO,MAAM,OAAO,IAAI,aAAa,oBAAoB;AAAA,MAC7D,CAAC;AAED,UAAI,QAAQ,SAAS;AACjB,aAAK,mBAAmB,OAAO,eAAe,CAAC;AAG/C,YAAI,CAAC,KAAK,oBAAoB,KAAK,iBAAiB,SAAS,GAAG;AAC5D,gBAAM,iBAAiB,KAAK,iBAAiB,KAAK,CAAAC,OAAKA,GAAE,WAAW,WAAW;AAC/E,cAAI,gBAAgB;AAChB,iBAAK,mBAAmB,eAAe;AAAA,UAC3C;AAAA,QACJ;AACA,aAAK,cAAc;AAAA,MACvB,OAAO;AACH,gBAAQ,KAAK,0DAA0D,QAAQ,KAAK;AAAA,MACxF;AAAA,IACJ,SAAS,OAAO;AACZ,cAAQ,MAAM,oDAAoD,MAAM,OAAO;AAAA,IACnF;AAAA,EACJ;AAAA,EAEA,MAAM,oBAAoB;AACtB,QAAI,CAAC,OAAO,KAAK,aAAc,QAAO;AAEtC,QAAI;AACA,WAAK,uBAAuB,cAAc,0BAA0B;AAEpE,YAAM,SAAS,MAAM,KAAK;AAAA,QACtB;AAAA,QACA,YAAY;AACR,iBAAO,MAAM,OAAO,IAAI,aAAa,kBAAkB;AAAA,QAC3D;AAAA,QACA,EAAE,SAAS,KAAK,iBAAiB;AAAA,MACrC;AAEA,UAAI,QAAQ,SAAS;AACjB,cAAM,KAAK,oBAAoB;AAC/B,aAAK,uBAAuB,aAAa,cAAc;AACvD,eAAO;AAAA,MACX,OAAO;AACH,cAAM,WAAW,2BAA2B,QAAQ,SAAS,eAAe;AAC5E,aAAK,uBAAuB,UAAU,QAAQ;AAC9C,eAAO;AAAA,MACX;AAAA,IACJ,SAAS,OAAO;AACZ,cAAQ,MAAM,iDAAiD,MAAM,OAAO;AAC5E,WAAK,uBAAuB,UAAU,4BAA4B,MAAM,OAAO,EAAE;AACjF,aAAO;AAAA,IACX;AAAA,EACJ;AAAA,EAEA,MAAM,0BAA0B;AAC5B,QAAI,CAAC,OAAO,KAAK,aAAc,QAAO;AAEtC,SAAK,kBAAkB;AACvB,SAAK,kBAAkB;AACvB,SAAK,cAAc;AACnB,SAAK,cAAc;AAEnB,UAAM,kBAAkB,CAAC,OAAO,SAAS;AAErC,UAAI,KAAK,YAAY,SAAU;AAE/B,UAAI,eAAe;AACnB,UAAI,aAAa;AAEjB,cAAQ,KAAK,OAAO;AAAA,QAChB,KAAK;AACD,yBAAe;AACf,uBAAa;AACb;AAAA,QACJ,KAAK;AACD,yBAAe;AACf,uBAAa;AACb;AAAA,QACJ,KAAK;AACD,yBAAe;AACf,uBAAa;AACb;AAAA,QACJ,KAAK;AACD,yBAAe;AACf,uBAAa;AACb;AAAA,QACJ,KAAK;AACD,yBAAe;AACf,uBAAa;AACb;AAAA,QACJ,KAAK;AACD,yBAAe;AACf,uBAAa;AACb;AAAA,MACR;AAEA,YAAM,kBAAkB,eAAgB,KAAK,WAAW,MAAO;AAE/D,WAAK,kBAAkB,KAAK;AAC5B,WAAK,kBAAkB,KAAK,MAAM,eAAe;AACjD,WAAK,cAAc;AAAA,IACvB;AAEA,QAAI,qBAAqB;AACzB,UAAM,oBAAoB,WAAW,YAAY;AAC7C,UAAI,CAAC,oBAAoB;AACrB,gBAAQ,IAAI,+DAA+D;AAC3E,cAAM,KAAK,6BAA6B,IAAI;AAAA,MAChD;AAAA,IACJ,GAAG,IAAK;AAER,UAAM,oBAAoB,OAAO,OAAO,SAAS;AAE7C,UAAI,KAAK,YAAY,SAAU;AAC/B,UAAI,mBAAoB;AACxB,2BAAqB;AACrB,mBAAa,iBAAiB;AAE9B,aAAO,IAAI,aAAa,wBAAwB,eAAe;AAE/D,YAAM,KAAK,6BAA6B,IAAI;AAAA,IAChD;AAGA,WAAO,IAAI,aAAa,kBAAkB,iBAAiB;AAC3D,WAAO,IAAI,aAAa,kBAAkB,eAAe;AAEzD,QAAI;AACA,UAAI;AACJ,UAAI,CAAC,KAAK,aAAa,WAAW;AAC9B,gBAAQ,IAAI,6DAA6D;AACzE,iBAAS,MAAM,OAAO,IAAI,aAAa,cAAc;AAAA,MACzD,OAAO;AACH,gBAAQ,IAAI,oDAAoD;AAChE,iBAAS,MAAM,OAAO,IAAI,aAAa,mBAAmB;AAAA,MAC9D;AAGA,UAAI,QAAQ,WAAW,CAAC,oBAAoB;AACxC,mBAAW,YAAY;AACnB,cAAI,CAAC,oBAAoB;AACrB,iCAAqB;AACrB,yBAAa,iBAAiB;AAC9B,kBAAM,KAAK,6BAA6B,IAAI;AAAA,UAChD;AAAA,QACJ,GAAG,GAAI;AAAA,MACX;AAAA,IACJ,SAAS,OAAO;AACZ,2BAAqB;AACrB,mBAAa,iBAAiB;AAC9B,cAAQ,MAAM,uCAAuC,KAAK;AAC1D,aAAO,IAAI,aAAa,wBAAwB,eAAe;AAC/D,aAAO,IAAI,aAAa,wBAAwB,iBAAiB;AACjE,YAAM,KAAK,6BAA6B,OAAO,MAAM,OAAO;AAAA,IAChE;AAAA,EACJ;AAAA,EAEA,MAAM,6BAA6B,SAAS,eAAe,MAAM;AAC7D,SAAK,kBAAkB;AACvB,SAAK,kBAAkB;AAEvB,QAAI,SAAS;AACT,YAAM,KAAK,oBAAoB;AAC/B,WAAK,iBAAiB;AAAA,IAC1B,OAAO;AACH,WAAK,WAAW,kBAAkB,gBAAgB,eAAe;AAAA,IACrE;AACA,SAAK,mBAAmB,KAAK,IAAI;AACjC,SAAK,cAAc;AAAA,EACvB;AAAA,EAEA,MAAM,iBAAiBH,IAAG;AACtB,UAAM,YAAYA,GAAE,OAAO,MAAM,KAAK;AACtC,SAAK,mBAAmB;AACxB,SAAK,cAAc;AAGnB,QAAI,aAAa,UAAU,SAAS,GAAG;AACnC,WAAK,kBAAkB,SAAS;AAAA,IACpC;AAEA,SAAK,cAAc;AAAA,EACvB;AAAA,EAEA,MAAM,oBAAoBA,IAAG;AACzB,QAAIA,GAAE,QAAQ,WAAW,KAAK,kBAAkB,KAAK,GAAG;AACpD,MAAAA,GAAE,eAAe;AACjB,cAAQ,IAAI,mDAAmD,KAAK,gBAAgB,EAAE;AAGtF,YAAM,cAAc,MAAM,KAAK,kBAAkB;AACjD,UAAI,CAAC,aAAa;AACd,aAAK,WAAW;AAChB,aAAK,mBAAmB,KAAK,IAAI;AACjC,aAAK,cAAc;AACnB;AAAA,MACJ;AAGA,YAAM,KAAK,aAAa,KAAK,gBAAgB;AAAA,IACjD;AAAA,EACJ;AAAA,EAEA,uBAAuB;AACnB,QAAI;AACA,YAAM,QAAQ,aAAa,QAAQ,sBAAsB;AACzD,UAAI,OAAO;AACP,aAAK,mBAAmB,KAAK,MAAM,KAAK;AAAA,MAC5C;AAAA,IACJ,SAAS,OAAO;AACZ,cAAQ,MAAM,gDAAgD,KAAK;AACnE,WAAK,mBAAmB,CAAC;AAAA,IAC7B;AAAA,EACJ;AAAA,EAEA,kBAAkB,WAAW;AACzB,QAAI,CAAC,aAAa,CAAC,UAAU,KAAK,EAAG;AAGrC,SAAK,mBAAmB,KAAK,iBAAiB,OAAO,CAAAG,OAAKA,OAAM,SAAS;AAGzE,SAAK,iBAAiB,QAAQ,SAAS;AAGvC,SAAK,mBAAmB,KAAK,iBAAiB,MAAM,GAAG,EAAE;AAGzD,QAAI;AACA,mBAAa,QAAQ,wBAAwB,KAAK,UAAU,KAAK,gBAAgB,CAAC;AAAA,IACtF,SAAS,OAAO;AACZ,cAAQ,MAAM,gDAAgD,KAAK;AAAA,IACvE;AAAA,EACJ;AAAA,EAEA,8BAA8B;AAC1B,UAAM,WAAW,CAAC;AAGlB,eAAW,SAAS,KAAK,kBAAkB;AACvC,eAAS,KAAK;AAAA,QACV,MAAM,MAAM;AAAA,QACZ,QAAQ;AAAA,QACR,MAAM,MAAM,QAAQ;AAAA,QACpB,QAAQ;AAAA,MACZ,CAAC;AAAA,IACL;AAGA,UAAM,iBAAiB,KAAK,iBAAiB,IAAI,CAAAA,OAAKA,GAAE,IAAI;AAC5D,eAAW,aAAa,KAAK,kBAAkB;AAC3C,UAAI,CAAC,eAAe,SAAS,SAAS,GAAG;AACrC,iBAAS,KAAK;AAAA,UACV,MAAM;AAAA,UACN,QAAQ;AAAA,UACR,MAAM;AAAA,UACN,QAAQ;AAAA,QACZ,CAAC;AAAA,MACL;AAAA,IACJ;AAEA,WAAO;AAAA,EACX;AAAA,EAEA,MAAM,aAAa,WAAW;AAC1B,QAAI,CAAC,WAAW,KAAK,GAAG;AACpB,YAAM,IAAI,MAAM,oBAAoB;AAAA,IACxC;AAEA,SAAK,kBAAkB;AACvB,SAAK,kBAAkB;AACvB,SAAK,cAAc;AACnB,SAAK,cAAc;AAEnB,QAAI,CAAC,OAAO,KAAK,aAAc;AAC/B,QAAI,kBAAkB;AAEtB,QAAI;AACA,cAAQ,IAAI,wDAAwD,SAAS,EAAE;AAG/E,wBAAkB,CAAC,OAAO,SAAS;AAC/B,YAAI,KAAK,YAAY,YAAY,KAAK,UAAU,aAAa,CAAC,KAAK,sBAAsB,SAAS,GAAG;AACjG,gBAAM,WAAW,KAAK,MAAM,KAAK,IAAI,GAAG,KAAK,IAAI,KAAK,KAAK,YAAY,CAAC,CAAC,CAAC;AAE1E,cAAI,aAAa,KAAK,iBAAiB;AACnC,iBAAK,kBAAkB;AACvB,oBAAQ,IAAI,gCAAgC,QAAQ,SAAS,SAAS,KAAK,KAAK,UAAU,aAAa,GAAG;AAC1G,iBAAK,cAAc;AAAA,UACvB;AAAA,QACJ;AAAA,MACJ;AAGA,aAAO,IAAI,aAAa,kBAAkB,eAAe;AAGzD,YAAM,iBAAiB,OAAO,IAAI,aAAa,gBAAgB,SAAS;AACxE,YAAM,iBAAiB,IAAI,QAAQ,CAACD,IAAG,WAAW,WAAW,MAAM,OAAO,IAAI,MAAM,uCAAuC,CAAC,GAAG,GAAM,CAAC;AAEtI,YAAM,SAAS,MAAM,QAAQ,KAAK,CAAC,gBAAgB,cAAc,CAAC;AAElE,UAAI,OAAO,SAAS;AAChB,gBAAQ,IAAI,wBAAwB,SAAS,iCAAiC;AAC9E,aAAK,kBAAkB;AACvB,aAAK,cAAc;AAGnB,cAAM,IAAI,QAAQ,aAAW,WAAW,SAAS,GAAG,CAAC;AAGrD,cAAM,KAAK,oBAAoB;AAC/B,aAAK,iBAAiB,UAAK,SAAS;AACpC,aAAK,mBAAmB,KAAK,IAAI;AAAA,MACrC,OAAO;AACH,cAAM,IAAI,MAAM,OAAO,SAAS,qBAAqB;AAAA,MACzD;AAAA,IACJ,SAAS,OAAO;AACZ,cAAQ,MAAM,6CAA6C,KAAK;AAChE,WAAK,WAAW,YAAY,MAAM,OAAO;AACzC,WAAK,mBAAmB,KAAK,IAAI;AAAA,IACrC,UAAE;AAEE,UAAI,iBAAiB;AACjB,eAAO,IAAI,aAAa,wBAAwB,eAAe;AAAA,MACnE;AAEA,WAAK,kBAAkB;AACvB,WAAK,kBAAkB;AACvB,WAAK,cAAc;AAAA,IACvB;AAAA,EACJ;AAAA,EAEA,sBAAsB,WAAW;AAC7B,WAAO,CAAC,KAAK,mBAAmB,KAAK,oBAAoB;AAAA,EAC7D;AAAA,EAEA,MAAM,qBAAqB,SAAS;AAChC,QAAI,CAAC,SAAS,KAAK,GAAG;AAClB,cAAQ,KAAK,yCAAyC;AACtD;AAAA,IACJ;AAEA,YAAQ,IAAI,mDAAmD,OAAO,EAAE;AAGxE,SAAK,0BAA0B,EAAE,GAAG,KAAK,yBAAyB,CAAC,OAAO,GAAG,EAAE;AAC/E,SAAK,cAAc;AACnB,SAAK,cAAc;AAEnB,QAAI,CAAC,OAAO,KAAK,aAAc;AAC/B,QAAI,kBAAkB;AAEtB,QAAI;AAEA,wBAAkB,CAAC,OAAO,SAAS;AAC/B,YAAI,KAAK,YAAY,aAAa,KAAK,UAAU,SAAS;AACtD,gBAAM,gBAAgB,KAAK,MAAM,KAAK,IAAI,GAAG,KAAK,IAAI,KAAK,KAAK,YAAY,CAAC,CAAC,CAAC;AAC/E,eAAK,0BAA0B,EAAE,GAAG,KAAK,yBAAyB,CAAC,OAAO,GAAG,cAAc;AAC3F,kBAAQ,IAAI,6CAA6C,aAAa,SAAS,OAAO,EAAE;AACxF,eAAK,cAAc;AAAA,QACvB;AAAA,MACJ;AAEA,aAAO,IAAI,aAAa,kBAAkB,eAAe;AAGzD,YAAM,kBAAkB,OAAO,IAAI,aAAa,qBAAqB,OAAO;AAC5E,YAAM,iBAAiB,IAAI,QAAQ,CAACA,IAAG,WAAW,WAAW,MAAM,OAAO,IAAI,MAAM,mCAAmC,CAAC,GAAG,GAAM,CAAC;AAElI,YAAM,SAAS,MAAM,QAAQ,KAAK,CAAC,iBAAiB,cAAc,CAAC;AAEnE,UAAI,QAAQ,SAAS;AACjB,aAAK,iBAAiB,UAAK,OAAO;AAClC,aAAK,mBAAmB,KAAK,IAAI;AACjC,gBAAQ,IAAI,gCAAgC,OAAO,0BAA0B;AAG7E,aAAK,mBAAmB;AAAA,MAC5B,OAAO;AACH,aAAK,WAAW,uBAAuB,OAAO,KAAK,QAAQ,SAAS,eAAe;AACnF,aAAK,mBAAmB,KAAK,IAAI;AACjC,gBAAQ,MAAM,2CAA2C,QAAQ,KAAK;AAAA,MAC1E;AAAA,IACJ,SAAS,OAAO;AACZ,cAAQ,MAAM,kDAAkD,OAAO,KAAK,KAAK;AACjF,WAAK,WAAW,sBAAsB,OAAO,KAAK,MAAM,OAAO;AAC/D,WAAK,mBAAmB,KAAK,IAAI;AAAA,IACrC,UAAE;AAEE,UAAI,iBAAiB;AACjB,eAAO,IAAI,aAAa,wBAAwB,eAAe;AAAA,MACnE;AACA,aAAO,KAAK,wBAAwB,OAAO;AAC3C,WAAK,cAAc;AAAA,IACvB;AAAA,EACJ;AAAA,EAEA,YAAYF,IAAG;AACX,IAAAA,GAAE,eAAe;AACjB,SAAK,cAAc;AACnB,UAAM,iBAAiBA,GAAE,iBAAiB,OAAO,eAAe,QAAQ,MAAM;AAC9E,YAAQ,IAAI,yBAAyB,eAAe,UAAU,GAAG,EAAE,IAAI,KAAK;AAE5E,QAAI,eAAe;AACf,WAAK,SAAS,cAAc,KAAK;AAEjC,YAAM,eAAeA,GAAE;AACvB,mBAAa,QAAQ,KAAK;AAAA,IAC9B;AAEA,SAAK,cAAc;AACnB,SAAK,eAAe,KAAK,MAAM;AAC3B,YAAM,aAAa,KAAK,YAAY,cAAc,eAAe;AACjE,UAAI,YAAY;AACZ,mBAAW,MAAM;AACjB,mBAAW,kBAAkB,WAAW,MAAM,QAAQ,WAAW,MAAM,MAAM;AAAA,MACjF;AAAA,IACJ,CAAC;AAAA,EACL;AAAA,EAEA,eAAeA,IAAG;AACd,QAAIA,GAAE,QAAQ,SAAS;AACnB,MAAAA,GAAE,eAAe;AACjB,WAAK,aAAa;AAAA,IACtB;AAAA,EACJ;AAAA;AAAA,EAGA,MAAM,qBAAqBA,IAAG;AAC1B,UAAM,UAAUA,GAAE,OAAO;AACzB,SAAK,mBAAmB;AAExB,QAAI,WAAW,KAAK,gBAAgB,WAAW;AAE3C,YAAM,eAAe,KAAK,wBAAwB,OAAO,MAAM;AAC/D,UAAI,CAAC,cAAc;AACf,gBAAQ,IAAI,iDAAiD,OAAO,EAAE;AACtE,cAAM,KAAK,qBAAqB,OAAO;AAAA,MAC3C;AAAA,IACJ;AAEA,SAAK,cAAc;AAAA,EACvB;AAAA,EAEA,MAAM,eAAe;AACjB,YAAQ,IAAI,4CAA4C;AAExD,SAAK,YAAY;AACjB,SAAK,cAAc;AACnB,SAAK,cAAc;AAEnB,QAAI,CAAC,OAAO,KAAK,cAAc;AAC3B,WAAK,YAAY;AACjB,WAAK,WAAW;AAChB,WAAK,cAAc;AACnB;AAAA,IACJ;AAEA,QAAI;AAEA,UAAI;AACJ,UAAI,KAAK,gBAAgB,UAAU;AAE/B,YAAI,CAAC,KAAK,kBAAkB,KAAK,GAAG;AAChC,gBAAM,IAAI,MAAM,mCAAmC;AAAA,QACvD;AAEA,cAAM,cAAc,MAAM,KAAK,kBAAkB;AACjD,YAAI,CAAC,aAAa;AACd,gBAAM,IAAI,MAAM,wBAAwB;AAAA,QAC5C;AAGA,cAAM,gBAAgB,KAAK,4BAA4B,EAAE,KAAK,CAAAG,OAAKA,GAAE,SAAS,KAAK,gBAAgB;AACnG,YAAI,CAAC,iBAAiB,cAAc,WAAW,aAAa;AACxD,kBAAQ,IAAI,mCAAmC,KAAK,gBAAgB,KAAK;AACzE,gBAAM,KAAK,aAAa,KAAK,gBAAgB;AAAA,QACjD;AAGA,oBAAY,MAAM,OAAO,IAAI,aAAa,YAAY;AAAA,UAClD,UAAU;AAAA,UACV,KAAK;AAAA,QACT,CAAC;AAED,YAAI,UAAU,SAAS;AAEnB,gBAAM,OAAO,IAAI,aAAa,iBAAiB;AAAA,YAC3C,MAAM;AAAA,YACN,SAAS,KAAK;AAAA,UAClB,CAAC;AAAA,QACL;AAAA,MACJ,OAAO;AAEH,YAAI,CAAC,KAAK,UAAU,KAAK,GAAG;AACxB,gBAAM,IAAI,MAAM,0BAA0B;AAAA,QAC9C;AAEA,oBAAY,MAAM,OAAO,IAAI,aAAa,YAAY;AAAA,UAClD,UAAU,KAAK;AAAA,UACf,KAAK,KAAK,UAAU,KAAK;AAAA,QAC7B,CAAC;AAED,YAAI,UAAU,SAAS;AACnB,gBAAM,SAAS,MAAM,OAAO,IAAI,aAAa,kBAAkB;AAC/D,gBAAM,iBAAiB,OAAO,KAAK,WAAW;AAC9C,cAAI,kBAAkB,eAAe,UAAU,SAAS,GAAG;AACvD,kBAAM,OAAO,IAAI,aAAa,iBAAiB;AAAA,cAC3C,MAAM;AAAA,cACN,SAAS,eAAe,UAAU,CAAC,EAAE;AAAA,YACzC,CAAC;AAAA,UACL;AAAA,QACJ;AAAA,MACJ;AAGA,UAAI;AACJ,UAAI,KAAK,gBAAgB,UAAU;AAE/B,oBAAY,EAAE,SAAS,KAAK;AAAA,MAChC,WAAW,KAAK,gBAAgB,WAAW;AAEvC,oBAAY,MAAM,OAAO,IAAI,aAAa,YAAY;AAAA,UAClD,UAAU;AAAA,UACV,KAAK;AAAA,QACT,CAAC;AAED,YAAI,UAAU,WAAW,KAAK,kBAAkB;AAE5C,gBAAM,OAAO,IAAI,aAAa,iBAAiB;AAAA,YAC3C,MAAM;AAAA,YACN,SAAS,KAAK;AAAA,UAClB,CAAC;AAAA,QACL;AAAA,MACJ,OAAO;AAEH,YAAI,CAAC,KAAK,UAAU,KAAK,GAAG;AACxB,gBAAM,IAAI,MAAM,0BAA0B;AAAA,QAC9C;AAEA,oBAAY,MAAM,OAAO,IAAI,aAAa,YAAY;AAAA,UAClD,UAAU,KAAK;AAAA,UACf,KAAK,KAAK,UAAU,KAAK;AAAA,QAC7B,CAAC;AAED,YAAI,UAAU,SAAS;AACnB,gBAAM,SAAS,MAAM,OAAO,IAAI,aAAa,kBAAkB;AAC/D,gBAAM,iBAAiB,OAAO,KAAK,WAAW;AAC9C,cAAI,kBAAkB,eAAe,UAAU,SAAS,GAAG;AACvD,kBAAM,OAAO,IAAI,aAAa,iBAAiB;AAAA,cAC3C,MAAM;AAAA,cACN,SAAS,eAAe,UAAU,CAAC,EAAE;AAAA,YACzC,CAAC;AAAA,UACL;AAAA,QACJ;AAAA,MACJ;AAEA,UAAI,UAAU,WAAW,UAAU,SAAS;AACxC,gBAAQ,IAAI,qDAAqD;AAGjE,mBAAW,YAAY;AACnB,gBAAM,eAAe,MAAM,OAAO,IAAI,aAAa,uBAAuB;AAC1E,kBAAQ,IAAI,8DAA8D,YAAY;AAEtF,cAAI,cAAc;AACd,iBAAK,uBAAuB;AAAA,UAChC,OAAO;AACH,oBAAQ,MAAM,2EAA2E;AACzF,iBAAK,WAAW;AAChB,iBAAK,YAAY;AACjB,iBAAK,cAAc;AAAA,UACvB;AAAA,QACJ,GAAG,GAAG;AAAA,MACV,OAAO;AACH,aAAK,WAAW,CAAC,UAAU,UAAU,IAAI,UAAU,SAAS,iBAAiB,KAAK;AAClF,aAAK,WAAW,CAAC,UAAU,UAAU,IAAI,UAAU,SAAS,SAAS,KAAK;AAC1E,aAAK,eAAe;AACpB,aAAK,mBAAmB,KAAK,IAAI;AAAA,MACrC;AAAA,IACJ,SAAS,OAAO;AACZ,cAAQ,MAAM,uCAAuC,KAAK;AAC1D,WAAK,WAAW,IAAI,MAAM,OAAO;AACjC,WAAK,mBAAmB,KAAK,IAAI;AAAA,IACrC;AAEA,SAAK,YAAY;AACjB,SAAK,cAAc;AAAA,EACvB;AAAA;AAAA;AAAA,EAKA,yBAAyB;AACrB,YAAQ,IAAI,sEAAsE;AAClF,SAAK,UAAU,IAAI,aAAa;AAGhC,eAAW,MAAM;AACb,UAAI,KAAK,UAAU,SAAS,aAAa,GAAG;AACxC,gBAAQ,IAAI,kEAAkE;AAC9E,aAAK,mBAAmB,EAAE,QAAQ,MAAM,eAAe,WAAW,CAAC;AAAA,MACvE;AAAA,IACJ,GAAG,CAAC;AAAA,EACR;AAAA,EAEA,cAAc;AACV,QAAI,OAAO,KAAK,QAAQ;AACpB,aAAO,IAAI,OAAO,gBAAgB;AAAA,IACtC;AAAA,EACJ;AAAA;AAAA,EAGA,mBAAmBH,IAAG;AAClB,QAAIA,GAAE,WAAW,QAAQ,CAAC,KAAK,UAAU,SAAS,aAAa,EAAG;AAClE,SAAK,UAAU,OAAO,aAAa;AACnC,SAAK,UAAU,IAAI,QAAQ;AAE3B,YAAQ,IAAI,wFAAwF;AAEpG,QAAI,CAAC,OAAO,KAAK,QAAQ;AACrB,cAAQ,MAAM,oEAAoE;AAClF;AAAA,IACJ;AAEA,QAAI,CAAC,KAAK,qBAAqB;AAC3B,cAAQ,MAAM,8GAA8G;AAC5H;AAAA,IACJ;AAEA,WAAO,IAAI,OACN,eAAe,EACf,KAAK,eAAa;AACf,cAAQ,IAAI,4DAA4D,SAAS;AAGjF,aAAO,OAAO,IAAI,aAAa,uBAAuB,EAAE,KAAK,kBAAgB;AACzE,gBAAQ,IAAI,kEAAkE,YAAY;AAE1F,YAAI,CAAC,cAAc;AACf,kBAAQ,KAAK,gGAAgG;AAAA,QACjH;AAGA,aAAK,oBAAoB,SAAS;AAAA,MACtC,CAAC;AAAA,IACL,CAAC,EACA,MAAM,WAAS;AACZ,cAAQ,MAAM,qEAAqE,KAAK;AAGxF,UAAI,KAAK,qBAAqB;AAC1B,gBAAQ,IAAI,4EAA4E;AACxF,aAAK,oBAAoB,EAAE,YAAY,MAAM,CAAC;AAAA,MAClD;AAAA,IACJ,CAAC;AAAA,EACT;AAAA;AAAA,EAGA,oBAAoB;AAChB,UAAM,kBAAkB;AACxB,SAAK,iBAAiB,gBAAgB,KAAK,kBAAkB;AAAA,EACjE;AAAA,EAEA,qBAAqBA,IAAG;AACpB,QAAIA,GAAE,kBAAkB,WAAW;AAE/B,UAAIA,GAAE,OAAO,UAAU,SAAS,eAAe,GAAG;AAC9C,aAAK,eAAe;AAAA,MACxB,WAAWA,GAAE,OAAO,UAAU,SAAS,iBAAiB,GAAG;AACvD,aAAK,iBAAiB;AAAA,MAC1B;AACA,WAAK,mBAAmB;AACxB,WAAK,cAAc;AAAA,IACvB;AAAA,EACJ;AAAA,EAEA,uBAAuB;AACnB,UAAM,qBAAqB;AAC3B,SAAK,oBAAoB,gBAAgB,KAAK,kBAAkB;AAGhE,SAAK,wBAAwB;AAAA,EACjC;AAAA,EAEA,0BAA0B;AACtB,YAAQ,IAAI,4CAA4C;AAGxD,SAAK,sBAAsB;AAG3B,SAAK,2BAA2B;AAGhC,QAAI,KAAK,iBAAiB;AACtB,WAAK,gBAAgB,mBAAmB,KAAK,eAAe;AAAA,IAChE;AAGA,QAAI,OAAO,KAAK,cAAc;AAC1B,aAAO,IAAI,aAAa,mBAAmB;AAAA,IAC/C;AAGA,UAAM,oBAAoB,OAAO,KAAK,KAAK,uBAAuB;AAClE,QAAI,kBAAkB,SAAS,GAAG;AAC9B,cAAQ,IAAI,6BAA6B,kBAAkB,MAAM,4BAA4B;AAC7F,wBAAkB,QAAQ,aAAW;AACjC,eAAO,KAAK,wBAAwB,OAAO;AAAA,MAC/C,CAAC;AAAA,IACL;AAGA,SAAK,kBAAkB;AACvB,SAAK,aAAa;AAElB,YAAQ,IAAI,kCAAkC;AAAA,EAClD;AAAA;AAAA;AAAA;AAAA,EAKA,uBAAuB;AACnB,UAAM,QAAQ,KAAK,kBAAkB;AAErC,YAAQ,MAAM,MAAM;AAAA,MAChB,KAAK;AACD,eAAO,KAAK,uBAAuB,KAAK;AAAA,MAC5C,KAAK;AACD,eAAO,KAAK,4BAA4B;AAAA,MAC5C,KAAK;AACD,eAAO,KAAK,0BAA0B;AAAA,MAC1C,KAAK;AACD,eAAO,KAAK,kBAAkB;AAAA,MAClC,KAAK;AACD,eAAO,KAAK,mBAAmB,KAAK;AAAA,MACxC,KAAK;AACD,eAAO,KAAK,uBAAuB,KAAK;AAAA,MAC5C;AACI,eAAO,KAAK,oBAAoB;AAAA,IACxC;AAAA,EACJ;AAAA,EAEA,oBAAoB;AAEhB,QAAI,KAAK,oBAAoB,cAAc;AACvC,aAAO,EAAE,MAAM,cAAc,SAAS,KAAK,mBAAmB,0BAA0B;AAAA,IAC5F;AAEA,QAAI,KAAK,oBAAoB,UAAU;AACnC,aAAO,EAAE,MAAM,UAAU,SAAS,KAAK,aAAa;AAAA,IACxD;AAEA,QAAI,KAAK,mBAAmB,KAAK,gBAAgB,SAAS,QAAQ,GAAG;AACjE,aAAO,EAAE,MAAM,cAAc,UAAU,KAAK,gBAAgB;AAAA,IAChE;AAEA,QAAI,CAAC,KAAK,aAAa,WAAW;AAC9B,aAAO,EAAE,MAAM,mBAAmB;AAAA,IACtC;AAEA,QAAI,CAAC,KAAK,aAAa,SAAS;AAC5B,aAAO,EAAE,MAAM,iBAAiB;AAAA,IACpC;AAEA,WAAO,EAAE,MAAM,QAAQ;AAAA,EAC3B;AAAA,EAEA,uBAAuB,OAAO;AAC1B,WAAO;AAAA;AAAA;AAAA,sFAGuE,KAAK,eAAe;AAAA;AAAA;AAAA,sBAGpF,KAAK,eAAe;AAAA;AAAA;AAAA;AAAA,EAItC;AAAA,EAEA,8BAA8B;AAC1B,WAAO,yDAA4D,KAAK,uBAAuB;AAAA,EACnG;AAAA,EAEA,4BAA4B;AACxB,WAAO,uDAA0D,KAAK,uBAAuB;AAAA,EACjG;AAAA,EAEA,oBAAoB;AAChB,WAAO;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,yBAMU,KAAK,gBAAgB;AAAA,yBACrB,KAAK,gBAAgB;AAAA,4BAClB,KAAK,mBAAmB;AAAA;AAAA,4BAExB,KAAK,aAAa,KAAK,eAAe;AAAA;AAAA;AAAA;AAAA,kBAIhD,KAAK,4BAA4B,EAAE;AAAA,MACjC,WAAS;AAAA,wCACW,MAAM,IAAI;AAAA,8BACpB,MAAM,IAAI,IAAI,MAAM,WAAW,cAAc,qBAAgB,MAAM,WAAW,YAAY,qBAAc,aAAa;AAAA;AAAA;AAAA,IAGnI,CAAC;AAAA;AAAA;AAAA;AAAA,cAIH,KAAK,kBAAkB,CAAC;AAAA,cACxB,KAAK,mBAAmB,CAAC,KAAK,gBAAgB,SAAS,QAAQ,IAC3D;AAAA;AAAA;AAAA;AAAA,+FAI6E,KAAK,eAAe;AAAA;AAAA;AAAA;AAAA,gCAInF,KAAK,eAAe;AAAA;AAAA;AAAA,sBAIlC,EAAE;AAAA;AAAA,EAEhB;AAAA,EAEA,mBAAmB,OAAO;AACtB,WAAO;AAAA;AAAA;AAAA;AAAA,sBAIO,MAAM,WAAW,eAAe;AAAA;AAAA;AAAA;AAAA;AAAA,6BAKzB,MAAM,KAAK,4BAA4B,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMjE;AAAA,EAEA,uBAAuB,OAAO;AAC1B,WAAO;AAAA;AAAA;AAAA,sFAGuE,MAAM,QAAQ;AAAA;AAAA;AAAA,sBAG9E,MAAM,QAAQ;AAAA;AAAA;AAAA;AAAA,EAIhC;AAAA,EAEA,sBAAsB;AAClB,WAAO;AAAA;AAAA;AAAA;AAAA;AAAA,EAKX;AAAA,EAEA,oBAAoB;AAChB,WAAO;AAAA,EACX;AAAA,EAEA,kBAAkB,MAAM;AACpB,UAAM,aAAa,SAAS,UAAU,KAAK,eAAe,KAAK;AAC/D,WAAO,cAAc,KAAK,mBAAmB,KAAK,KAAK,IAAI,IAAI,KAAK,mBAAmB;AAAA,EAC3F;AAAA,EAEA,oBAAoB;AAChB,YAAQ,IAAI,0CAAmC;AAC/C,QAAI,OAAO,KAAK,QAAQ;AACpB,aAAO,IAAI,OAAO,aAAa,mCAAmC;AAAA,IACtE;AAAA,EACJ;AAAA,EAEA,SAAS;AACL,UAAM,iBAAiB,KAAK,gBAAgB,YAAY,KAAK,gBAAgB;AAC7E,UAAM,iBAAiB,KAAK,gBAAgB,YAAY,KAAK,gBAAgB;AAC7E,UAAM,gBAAgB,KAAK,gBAAgB;AAC3C,UAAM,gBAAgB,KAAK,gBAAgB;AAE3C,UAAM,mBACF,KAAK,aACL,KAAK,mBACL,OAAO,KAAK,KAAK,uBAAuB,EAAE,SAAS,KAClD,kBAAkB,CAAC,KAAK,UAAU,KAAK,KACvC,kBAAkB,CAAC,KAAK,UAAU,KAAK,KACvC,iBAAiB,CAAC,KAAK,kBAAkB,KAAK,KAC9C,iBAAiB,CAAC,KAAK;AAE5B,UAAM,kBAAkB,KAAK,UAAU,IAAI,KAAK,CAAAC,OAAKA,GAAE,OAAO,KAAK,WAAW,GAAG,QAAQ,KAAK;AAE9F,WAAO;AAAA;AAAA,sDAEuC,KAAK,WAAW;AAAA;AAAA,sDAEhB,KAAK,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,8BAYvC,KAAK,UAAU,IAAI;AAAA,MACjB,CAAAA,OAAK;AAAA;AAAA;AAAA,sDAGiB,KAAK,gBAAgBA,GAAE,KAAK,WAAW,SAAS;AAAA,iDACrD,CAAAD,OAAK,KAAK,wBAAwBA,IAAGC,GAAE,EAAE,CAAC;AAAA;AAAA,0CAEjDA,GAAE,IAAI;AAAA;AAAA;AAAA,IAGpB,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,0BAKH,KAAK,gBAAgB,WACjB,KAAK,qBAAqB,IAC1B;AAAA;AAAA;AAAA;AAAA,6DAI+B,KAAK,WAAW,YAAY,EAAE;AAAA,oEACvB,eAAe;AAAA,mDAChC,KAAK,SAAS;AAAA,mDACd,CAAAD,OAAK;AACV,WAAK,YAAYA,GAAE,OAAO;AAC1B,WAAK,WAAW;AAAA,IACpB,CAAC;AAAA,sDACW,KAAK,SAAS;AAAA;AAAA,wCAE5B,KAAK,WAAW,sCAAyC,KAAK,QAAQ,WAAW,EAAE;AAAA;AAAA,+BAE5F;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,8BASD,KAAK,UAAU,IAAI;AAAA,MACjB,CAAAC,OAAK;AAAA;AAAA;AAAA,sDAGiB,KAAK,gBAAgBA,GAAE,KAAK,WAAW,SAAS;AAAA,iDACrD,CAAAD,OAAK,KAAK,wBAAwBA,IAAGC,GAAE,EAAE,CAAC;AAAA;AAAA,0CAEjDA,GAAE,IAAI;AAAA;AAAA;AAAA,IAGpB,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,0BAKH,KAAK,gBAAgB,WACjB;AAAA;AAAA;AAAA;AAAA,kCAKA,KAAK,gBAAgB,YACnB;AAAA;AAAA;AAAA,+DAG+B,KAAK,WAAW,YAAY,EAAE;AAAA,qDACxC,KAAK,oBAAoB,EAAE;AAAA,sDAC1B,CAAAD,OAAK;AACX,WAAK,qBAAqBA,EAAC;AAC3B,WAAK,WAAW;AAAA,IACpB,CAAC;AAAA,wDACW,KAAK,SAAS;AAAA;AAAA;AAAA,8CAGxB;AAAA,MACE,EAAE,IAAI,gBAAgB,MAAM,qBAAqB;AAAA,MACjD,EAAE,IAAI,gBAAgB,MAAM,qBAAqB;AAAA,MACjD,EAAE,IAAI,iBAAiB,MAAM,uBAAuB;AAAA,MACpD,EAAE,IAAI,kBAAkB,MAAM,wBAAwB;AAAA,IAC1D,EAAE,IAAI,WAAS,oBAAuB,MAAM,EAAE,KAAK,MAAM,IAAI,YAAY,CAAC;AAAA;AAAA,0CAE5E,KAAK,WAAW,sCAAyC,KAAK,QAAQ,WAAW,EAAE;AAAA;AAAA,oCAG7F;AAAA;AAAA;AAAA;AAAA,+DAI+B,KAAK,WAAW,YAAY,EAAE;AAAA;AAAA,qDAExC,KAAK,SAAS;AAAA,qDACd,CAAAA,OAAK;AACV,WAAK,YAAYA,GAAE,OAAO;AAC1B,WAAK,WAAW;AAAA,IACpB,CAAC;AAAA,wDACW,KAAK,SAAS;AAAA;AAAA,0CAE5B,KAAK,WAAW,sCAAyC,KAAK,QAAQ,WAAW,EAAE;AAAA;AAAA,iCAE5F;AAAA;AAAA;AAAA;AAAA,4DAI2B,KAAK,YAAY,cAAc,gBAAgB;AAAA,0BACjF,KAAK,YACD,kBACA,KAAK,kBACH,cAAc,KAAK,eAAe,QAClC,OAAO,KAAK,KAAK,uBAAuB,EAAE,SAAS,IACjD,mBACA,SAAS;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,uDAQY,KAAK,iBAAiB;AAAA;AAAA;AAAA,4CAGjC,KAAK,kBAAkB,OAAO,IAAI,qBAAqB,EAAE,mBAAmB,KAAK,oBAAoB;AAAA,sBAC3H,KAAK,YAAY;AAAA;AAAA;AAAA,6CAGM,KAAK,kBAAkB,SAAS,IAAI,qBAAqB,EAAE;AAAA,oCACpE,KAAK,oBAAoB;AAAA;AAAA,sBAEvC,KAAK,cAAc;AAAA;AAAA;AAAA;AAAA,EAIrC;AACJ;AAEA,eAAe,OAAO,iBAAiB,YAAY;;;ACviE5C,IAAM,mBAAN,cAA+B,GAAW;AAAA,EAC7C,OAAO,SAAS;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAoQhB,OAAO,aAAa;AAAA,IAChB,mBAAmB,EAAE,MAAM,OAAO;AAAA,IAClC,eAAe,EAAE,MAAM,OAAO;AAAA,IAC9B,iBAAiB,EAAE,MAAM,OAAO;AAAA,IAChC,YAAY,EAAE,MAAM,OAAO;AAAA,IAC3B,kBAAkB,EAAE,MAAM,SAAS;AAAA,IACnC,UAAU,EAAE,MAAM,OAAO;AAAA;AAAA,EAC7B;AAAA,EAEA,cAAc;AACV,UAAM;AACN,SAAK,oBAAoB;AACzB,SAAK,gBAAgB;AACrB,SAAK,kBAAkB;AACvB,SAAK,aAAa;AAClB,SAAK,mBAAmB;AACxB,SAAK,WAAW;AAAA,EACpB;AAAA,EAEA,QAAQ,mBAAmB;AACvB,UAAM,QAAQ,iBAAiB;AAC/B,QAAI,kBAAkB,IAAI,UAAU,GAAG;AACnC,YAAM,YAAY,KAAK,aAAa,aAAa,MAAM;AACvD,cAAQ,IAAI,2CAA2C,KAAK,QAAQ,0BAA0B,SAAS,IAAI;AAC3G,WAAK,cAAc,IAAI,YAAY,kBAAkB;AAAA,QACjD,QAAQ,EAAE,QAAQ,UAAU;AAAA,QAC5B,SAAS;AAAA,QACT,UAAU;AAAA,MACd,CAAC,CAAC;AAAA,IACN;AAAA,EACJ;AAAA,EAEA,MAAM,oBAAoB;AACtB,UAAM,kBAAkB;AAExB,QAAI,OAAO,KAAK;AACZ,UAAI;AACA,cAAM,YAAY,MAAM,OAAO,IAAI,OAAO,eAAe;AACzD,aAAK,WAAW,UAAU;AAAA,MAC9B,SAASI,IAAG;AACR,gBAAQ,MAAM,+CAA+CA,EAAC;AAC9D,aAAK,WAAW;AAAA,MACpB;AAAA,IACJ;AAEA,UAAM,KAAK,iBAAiB;AAG5B,SAAK,0BAA0B,YAAY,YAAY;AACnD,UAAI,OAAO,KAAK;AACZ,YAAI;AACA,gBAAM,YAAY,MAAM,OAAO,IAAI,OAAO,eAAe;AACzD,eAAK,WAAW,UAAU;AAAA,QAC9B,SAASA,IAAG;AACR,eAAK,WAAW;AAAA,QACpB;AAAA,MACJ;AACA,WAAK,iBAAiB;AAAA,IAC1B,GAAG,GAAI;AAAA,EACX;AAAA,EAEA,uBAAuB;AACnB,UAAM,qBAAqB;AAC3B,QAAI,KAAK,yBAAyB;AAC9B,oBAAc,KAAK,uBAAuB;AAAA,IAC9C;AAAA,EACJ;AAAA,EAEA,MAAM,mBAAmB;AACrB,QAAI,CAAC,OAAO,OAAO,KAAK,WAAY;AAEpC,SAAK,aAAa;AAElB,QAAI;AACA,YAAM,cAAc,MAAM,OAAO,IAAI,iBAAiB,uBAAuB;AAC7E,cAAQ,IAAI,+CAA+C,WAAW;AAEtE,YAAM,UAAU,KAAK;AACrB,YAAM,aAAa,KAAK;AACxB,YAAM,eAAe,KAAK;AAE1B,WAAK,oBAAoB,YAAY;AACrC,WAAK,gBAAgB,YAAY;AACjC,WAAK,kBAAkB,YAAY;AAGnC,UAAI,YAAY,KAAK,qBAAqB,eAAe,KAAK,iBAAiB,iBAAiB,KAAK,iBAAiB;AAClH,gBAAQ,IAAI,2DAA2D;AACvE,aAAK,cAAc;AAAA,MACvB;AAEA,YAAM,qBAAqB,KAAK,aAAa;AAC7C,YAAM,aAAa,CAAC,sBAAsB,KAAK,oBAAoB;AAGnE,UAAI,KAAK,sBAAsB,aAC3B,KAAK,kBAAkB,aACvB,cACA,KAAK,kBAAkB;AACvB,gBAAQ,IAAI,sEAAsE;AAClF,mBAAW,MAAM,KAAK,eAAe,GAAG,GAAG;AAAA,MAC/C;AAAA,IACJ,SAAS,OAAO;AACZ,cAAQ,MAAM,kDAAkD,KAAK;AAAA,IACzE,UAAE;AACE,WAAK,aAAa;AAAA,IACtB;AAAA,EACJ;AAAA,EAEA,MAAM,wBAAwB;AAC1B,QAAI,CAAC,OAAO,OAAO,KAAK,sBAAsB,UAAW;AAEzD,YAAQ,IAAI,wDAAwD;AAEpE,QAAI;AACA,YAAM,SAAS,MAAM,OAAO,IAAI,iBAAiB,uBAAuB;AACxE,cAAQ,IAAI,oDAAoD,MAAM;AAEtE,UAAI,OAAO,eAAe,WAAW;AACjC,aAAK,oBAAoB;AACzB,aAAK,cAAc;AACnB;AAAA,MACF;AAEA,UAAI,OAAO,eAAe,oBAAoB,OAAO,eAAe,YAAY,OAAO,eAAe,aAAa,OAAO,eAAe,cAAc;AACrJ,cAAM,MAAM,MAAM,OAAO,IAAI,iBAAiB,4BAA4B;AAC1E,YAAI,IAAI,WAAW,aAAa,IAAI,YAAY,MAAM;AAClD,eAAK,oBAAoB;AACzB,eAAK,cAAc;AACnB;AAAA,QACJ;AAAA,MACF;AAAA,IAKN,SAAS,OAAO;AACZ,cAAQ,MAAM,8DAA8D,KAAK;AAAA,IACrF;AAAA,EACJ;AAAA,EAEA,MAAM,oBAAoB;AACtB,QAAI,CAAC,OAAO,OAAO,KAAK,kBAAkB,UAAW;AAErD,YAAQ,IAAI,4DAA4D;AAExE,QAAI;AACA,YAAM,cAAc,MAAM,OAAO,IAAI,iBAAiB,uBAAuB;AAC7E,cAAQ,IAAI,sDAAsD,WAAW;AAE7E,UAAI,YAAY,WAAW,WAAW;AAClC,aAAK,gBAAgB;AACrB,aAAK,cAAc;AACnB;AAAA,MACJ;AACA,UAAI,YAAY,WAAW,oBAAoB,YAAY,WAAW,YAAY,YAAY,WAAW,aAAa,YAAY,WAAW,cAAc;AAC3J,gBAAQ,IAAI,4DAA4D;AACxE,cAAM,OAAO,IAAI,iBAAiB,sBAAsB,kBAAkB;AAAA,MAC1E;AAAA,IAKJ,SAAS,OAAO;AACZ,cAAQ,MAAM,kEAAkE,KAAK;AAAA,IACzF;AAAA,EACJ;AAAA,EAEA,MAAM,sBAAsB;AACxB,QAAI,CAAC,OAAO,OAAO,KAAK,oBAAoB,UAAW;AAEvD,YAAQ,IAAI,sDAAsD;AAElE,QAAI;AAGA,YAAM,OAAO,IAAI,iBAAiB,wBAAwB;AAG1D,WAAK,kBAAkB;AACvB,WAAK,cAAc;AAAA,IACvB,SAAS,OAAO;AACZ,cAAQ,MAAM,4DAA4D,KAAK;AAAA,IACnF;AAAA,EACJ;AAAA,EAEA,MAAM,iBAAiB;AACnB,UAAM,qBAAqB,KAAK,aAAa;AAC7C,UAAM,aAAa,CAAC,sBAAsB,KAAK,oBAAoB;AAEnE,QAAI,KAAK,oBACL,KAAK,sBAAsB,aAC3B,KAAK,kBAAkB,aACvB,YAAY;AAEZ,UAAI,OAAO,OAAO,oBAAoB;AAClC,YAAI;AACA,gBAAM,OAAO,IAAI,iBAAiB,sBAAsB;AACxD,kBAAQ,IAAI,iDAAiD;AAAA,QACjE,SAAS,OAAO;AACZ,kBAAQ,MAAM,2DAA2D,KAAK;AAAA,QAClF;AAAA,MACJ;AAEA,WAAK,iBAAiB;AAAA,IAC1B;AAAA,EACJ;AAAA,EAEA,cAAc;AACV,YAAQ,IAAI,sBAAsB;AAClC,QAAI,OAAO,KAAK;AACZ,aAAO,IAAI,OAAO,gBAAgB;AAAA,IACtC;AAAA,EACJ;AAAA,EAEA,SAAS;AACL,UAAM,qBAAqB,KAAK,aAAa;AAC7C,UAAM,kBAAkB,qBAAqB,MAAM;AACnD,UAAM,aAAa,CAAC,sBAAsB,KAAK,oBAAoB;AACnE,UAAM,aAAa,KAAK,sBAAsB,aAAa,KAAK,kBAAkB,aAAa;AAE/F,WAAO;AAAA,oDACqC,eAAe;AAAA,sDACb,KAAK,WAAW;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,2CAO3B,aAAa,gBAAgB,EAAE;AAAA,sBACpD,CAAC,aAAa;AAAA,4FACwD,qBAAqB,kBAAkB,EAAE;AAAA;AAAA;AAAA,0DAG3E,KAAK,sBAAsB,YAAY,YAAY,EAAE;AAAA,kCAC7E,KAAK,sBAAsB,YAAY;AAAA;AAAA;AAAA;AAAA;AAAA,oCAKrC;AAAA;AAAA;AAAA;AAAA;AAAA,iCAKH;AAAA;AAAA;AAAA,0DAGyB,KAAK,kBAAkB,YAAY,YAAY,EAAE;AAAA,kCACzE,KAAK,kBAAkB,YAAY;AAAA;AAAA;AAAA;AAAA;AAAA,oCAKjC;AAAA;AAAA;AAAA;AAAA;AAAA,iCAKH;AAAA;AAAA;AAAA,8BAGH,qBAAqB;AAAA,8DACW,KAAK,oBAAoB,YAAY,YAAY,EAAE;AAAA,sCAC3E,KAAK,oBAAoB,YAAY;AAAA;AAAA;AAAA;AAAA;AAAA,wCAKnC;AAAA;AAAA;AAAA;AAAA;AAAA,qCAKH;AAAA;AAAA,gCAEL,EAAE;AAAA;AAAA;AAAA;AAAA;AAAA,qCAKG,KAAK,qBAAqB;AAAA,wCACvB,KAAK,sBAAsB,SAAS;AAAA;AAAA,8BAE9C,KAAK,sBAAsB,YAAY,8BAA8B,yBAAyB;AAAA;AAAA;AAAA;AAAA;AAAA,qCAKvF,KAAK,iBAAiB;AAAA,wCACnB,KAAK,kBAAkB,SAAS;AAAA;AAAA,8BAE1C,KAAK,kBAAkB,YAAY,6BAA6B,+BAA+B;AAAA;AAAA;AAAA,0BAGnG,qBAAqB;AAAA;AAAA;AAAA,yCAGN,KAAK,mBAAmB;AAAA,4CACrB,KAAK,oBAAoB,SAAS;AAAA;AAAA,kCAE5C,KAAK,oBAAoB,YAAY,uBAAuB,mBAAmB;AAAA;AAAA,uEAE1C,KAAK,oBAAoB,YAAY,WAAW,SAAS;AAAA;AAAA;AAAA,4BAGpG,EAAE;AAAA,wBACN;AAAA;AAAA;AAAA,qCAGa,KAAK,cAAc;AAAA;AAAA;AAAA;AAAA,qBAInC;AAAA;AAAA;AAAA;AAAA,EAIjB;AACJ;AAEA,eAAe,OAAO,oBAAoB,gBAAgB;;;ACvkBnD,IAAM,gBAAN,cAA4B,GAAW;AAAA,EAC1C,OAAO,SAAS;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EA+JhB,OAAO,aAAa;AAAA,IAChB,eAAe,EAAE,MAAM,SAAS;AAAA,IAChC,gBAAgB,EAAE,MAAM,SAAS;AAAA,EACrC;AAAA,EAEA,cAAc;AACV,UAAM;AACN,SAAK,gBAAgB,MAAM;AAAA,IAAC;AAC5B,SAAK,iBAAiB,MAAM;AAAA,IAAC;AAC7B,SAAK,cAAc,KAAK,YAAY,KAAK,IAAI;AAAA,EACjD;AAAA,EAEA,QAAQ,mBAAmB;AACvB,UAAM,QAAQ,iBAAiB;AAC/B,SAAK,cAAc,IAAI,YAAY,mBAAmB,EAAE,SAAS,MAAM,UAAU,KAAK,CAAC,CAAC;AAAA,EAC5F;AAAA,EAEA,cAAc;AACV,QAAI,OAAO,KAAK,QAAQ;AACpB,aAAO,IAAI,OAAO,gBAAgB;AAAA,IACtC;AAAA,EACJ;AAAA,EAEA,SAAS;AACL,WAAO;AAAA;AAAA,sDAEuC,KAAK,WAAW;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,2DAaX,KAAK,aAAa;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,2DAalB,KAAK,cAAc;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,uDAOvB,KAAK,iBAAiB;AAAA;AAAA;AAAA;AAAA,EAIzE;AAAA,EAEA,oBAAoB;AAChB,YAAQ,IAAI,2CAAoC;AAChD,QAAI,OAAO,KAAK,QAAQ;AACpB,aAAO,IAAI,OAAO,aAAa,mCAAmC;AAAA,IACtE;AAAA,EACJ;AACJ;AAEA,eAAe,OAAO,kBAAkB,aAAa;;;ACtOrD,IAAM,0BAAN,MAA8B;AAAA,EAC1B,cAAc;AACV,SAAK,kBAAuB,SAAS,eAAe,kBAAkB;AACtE,SAAK,oBAAuB;AAC5B,SAAK,gBAAuB;AAC5B,SAAK,eAAuB;AAC5B,SAAK,aAAwB;AAC7B,SAAK,mBAAwB;AAM7B,SAAK,eAAe,CAAC,SAAS;AAC1B,cAAQ,IAAI,6DAA6D,IAAI;AAC7E,UAAI,KAAK,sBAAsB,MAAM;AACjC,gBAAQ,IAAI,oDAAoD,MAAM,iBAAiB;AACvF;AAAA,MACJ;AAEA,WAAK,gBAAgB,YAAY;AAEjC,WAAK,gBAAgB;AACrB,WAAK,eAAe;AACpB,WAAK,aAAa;AAClB,WAAK,mBAAmB;AAGxB,UAAI,SAAS,WAAW;AACpB,aAAK,gBAAgB,SAAS,cAAc,gBAAgB;AAC5D,aAAK,cAAc,gBAAgB,MAAM,KAAK,kBAAkB;AAChE,aAAK,cAAc,iBAAiB,MAAM,KAAK,mBAAmB;AAClE,aAAK,gBAAgB,YAAY,KAAK,aAAa;AACnD,gBAAQ,IAAI,oDAAoD,MAAM,UAAU;AAAA,MACpF,WAAW,SAAS,UAAU;AAC1B,aAAK,eAAe,SAAS,cAAc,eAAe;AAC1D,aAAK,aAAa,sBAAsB,CAAC,cAAc,KAAK,kBAAkB,SAAS;AACvF,aAAK,aAAa,eAAe,MAAM,KAAK,0BAA0B;AACtE,aAAK,aAAa,iBAAiB,kBAAkB,CAAAC,OAAK;AACtD,eAAK,iBAAiBA,GAAE,OAAO,MAAM;AAAA,QACzC,CAAC;AACD,aAAK,gBAAgB,YAAY,KAAK,YAAY;AAClD,gBAAQ,IAAI,oDAAoD,MAAM,UAAU;AAAA,MACpF,WAAW,SAAS,cAAc;AAC9B,aAAK,mBAAmB,SAAS,cAAc,kBAAkB;AACjE,aAAK,iBAAiB,iBAAiB,kBAAkB,CAAAA,OAAK;AAC1D,eAAK,2BAA2BA,GAAE,OAAO,MAAM;AAAA,QACnD,CAAC;AACD,aAAK,iBAAiB,mBAAmB,YAAY;AACjD,cAAI,OAAO,OAAO,OAAO,IAAI,kBAAkB;AAC3C,oBAAQ,IAAI,0EAA0E;AACtF,kBAAM,OAAO,IAAI,iBAAiB,uBAAuB;AAAA,UAC7D;AACA,eAAK,uBAAuB;AAAA,QAChC;AACA,aAAK,gBAAgB,YAAY,KAAK,gBAAgB;AAAA,MAC1D,OAAO;AACH,aAAK,aAAa,SAAS,cAAc,aAAa;AACtD,aAAK,gBAAgB,YAAY,KAAK,UAAU;AAChD,aAAK,WAAW,wBAAwB;AAAA,MAC5C;AAEA,WAAK,oBAAoB;AACzB,WAAK,kBAAkB,SAAS,eAAe,WAAW,IAAI;AAAA,IAClE;AAEA,YAAQ,IAAI,wCAAwC;AAGpD,SAAK,oBAAoB,KAAK,kBAAkB,KAAK,IAAI;AACzD,SAAK,qBAAqB,KAAK,mBAAmB,KAAK,IAAI;AAE3D,SAAK,WAAW;AAEhB,QAAI,OAAO,KAAK;AACZ,aAAO,IAAI,iBAAiB,mBAAmB,CAAC,OAAO,cAAc;AACjE,gBAAQ,IAAI,kDAAkD,SAAS;AACvE,aAAK,kBAAkB,SAAS;AAAA,MACpC,CAAC;AAED,aAAO,IAAI,iBAAiB,aAAa,CAAC,OAAO,EAAE,QAAQ,MAAM;AAC7D,gBAAQ,MAAM,+DAA+D,OAAO;AACpF,YAAI,KAAK,cAAc;AACnB,eAAK,aAAa,eAAe;AACjC,eAAK,aAAa,YAAY;AAAA,QAClC;AAAA,MACJ,CAAC;AACD,aAAO,IAAI,iBAAiB,wBAAwB,YAAY;AAC5D,gBAAQ,IAAI,6EAA6E;AACzF,cAAM,eAAe,MAAM,OAAO,IAAI,aAAa,uBAAuB;AAC1E,YAAI,CAAC,cAAc;AACf,gBAAM,KAAK,kBAAkB;AAC7B,eAAK,aAAa,SAAS;AAAA,QAC/B,OAAO;AACH,gBAAM,KAAK,iBAAiB;AAC5B,eAAK,aAAa,QAAQ;AAAA,QAC9B;AAAA,MACJ,CAAC;AAAA,IACL;AAAA,EACJ;AAAA,EAEA,kBAAkB,eAAe;AAC7B,UAAM,QAAQ,iBAAiB,KAAK,qBAAqB;AACzD,QAAI,OAAO,KAAK;AACZ,aAAO,IAAI,iBAAiB,uBAAuB,KAAK;AAAA,IAC5D;AAAA,EACJ;AAAA,EAEA,MAAM,aAAa;AAGf,QAAI,OAAO,KAAK;AACZ,YAAM,YAAY,MAAM,OAAO,IAAI,OAAO,eAAe;AACzD,cAAQ,IAAI,6DAA6D,SAAS;AAClF,WAAK,kBAAkB,SAAS;AAAA,IACpC,OAAO;AAEH,WAAK,aAAa,SAAS;AAAA,IAC/B;AAAA,EACJ;AAAA;AAAA,EAIA,MAAM,kBAAkB,WAAW;AAC/B,UAAM,eAAe,MAAM,OAAO,IAAI,aAAa,uBAAuB;AAE1E,QAAI,cAAc;AAEd,YAAM,mBAAmB,MAAM,KAAK,iBAAiB;AACrD,UAAI,iBAAiB,SAAS;AAC1B,aAAK,uBAAuB;AAAA,MAChC,OAAO;AACH,aAAK,6BAA6B;AAAA,MACtC;AAAA,IACJ,OAAO;AAEH,YAAM,KAAK,kBAAkB;AAC7B,WAAK,aAAa,SAAS;AAAA,IAC/B;AAAA,EACJ;AAAA;AAAA,EAGA,MAAM,oBAAoB;AACtB,YAAQ,IAAI,0CAA0C;AACtD,QAAI,OAAO,KAAK;AACZ,YAAM,OAAO,IAAI,OAAO,kBAAkB;AAAA,IAC9C;AAAA,EACJ;AAAA,EAEA,MAAM,qBAAqB;AACvB,YAAQ,IAAI,4CAA4C;AACxD,UAAM,KAAK,iBAAiB,GAAG;AAC/B,SAAK,aAAa,QAAQ;AAE1B,QAAI,KAAK,cAAc;AACnB,WAAK,aAAa,eAAe,MAAM,KAAK,0BAA0B;AAAA,IAC1E;AAAA,EACJ;AAAA,EAEA,MAAM,4BAA4B;AAC9B,QAAI,KAAK,sBAAsB,WAAW;AACtC,aAAO,KAAK,kBAAkB;AAAA,IAClC;AAEA,UAAM,KAAK,kBAAkB;AAC7B,SAAK,aAAa,SAAS;AAAA,EAC/B;AAAA;AAAA,EAGA,MAAM,+BAA+B;AAEjC,QAAI,KAAK,sBAAsB,cAAc;AACzC,cAAQ,IAAI,0EAA0E;AACtF;AAAA,IACJ;AAGA,QAAI,OAAO,KAAK;AACZ,UAAI;AACA,cAAM,uBAAuB,MAAM,OAAO,IAAI,iBAAiB,0BAA0B;AACzF,YAAI,sBAAsB;AACtB,kBAAQ,IAAI,sFAAsF;AAGlG,gBAAM,mBAAmB,MAAM,KAAK,iBAAiB;AACrD,cAAI,iBAAiB,SAAS;AAE1B,iBAAK,uBAAuB;AAC5B;AAAA,UACJ;AAEA,kBAAQ,IAAI,kEAAkE;AAAA,QAClF;AAAA,MACJ,SAAS,OAAO;AACZ,gBAAQ,MAAM,mEAAmE,KAAK;AAAA,MAC1F;AAAA,IACJ;AAEA,QAAI,gBAAgB;AACpB,QAAI,OAAO,KAAK;AACZ,UAAI;AACA,cAAM,YAAY,MAAM,OAAO,IAAI,OAAO,eAAe;AACzD,YAAI,UAAU,SAAS,YAAY;AAC/B,0BAAgB;AAAA,QACpB;AAAA,MACJ,SAASA,IAAG;AACR,gBAAQ,MAAM,uCAAuCA,EAAC;AAAA,MAC1D;AAAA,IACJ;AAEA,UAAM,KAAK,2BAA2B,aAAa;AACnD,SAAK,aAAa,YAAY;AAAA,EAClC;AAAA,EAEA,MAAM,uBAAuB,UAAU,MAAM;AACzC,QAAI,KAAK,sBAAsB,QAAQ;AACnC,aAAO,KAAK,eAAe;AAAA,IAC/B;AAEA,UAAM,KAAK,eAAe;AAC1B,SAAK,aAAa,MAAM;AAAA,EAC5B;AAAA,EAEA,MAAM,iBAAiB;AACnB,QAAI,CAAC,OAAO,IAAK;AACjB,YAAQ,IAAI,8DAA8D;AAC1E,WAAO,OAAO,IAAI,iBAAiB,mBAAmB,EAAE,OAAO,KAAK,QAAQ,GAAG,CAAC,EAAE,MAAM,MAAM;AAAA,IAAC,CAAC;AAAA,EACpG;AAAA,EAEA,MAAM,iBAAiB,SAAS,KAAK;AACjC,QAAI,CAAC,OAAO,IAAK;AACjB,YAAQ,IAAI,+DAA+D,MAAM,EAAE;AACnF,WAAO,OAAO,IAAI,iBAAiB,mBAAmB,EAAE,OAAO,KAAK,OAAe,CAAC,EAAE,MAAM,MAAM;AAAA,IAAC,CAAC;AAAA,EACxG;AAAA,EAEA,MAAM,2BAA2B,QAAQ;AACrC,QAAI,CAAC,OAAO,IAAK;AACjB,UAAM,cAAc,UAAU;AAC9B,WAAO,OAAO,IAAI,iBAAiB,mBAAmB,EAAE,OAAO,KAAK,QAAQ,YAAY,CAAC,EACpF,MAAM,MAAM;AAAA,IAAC,CAAC;AAAA,EACvB;AAAA,EAEA,MAAM,oBAAoB;AACtB,QAAI,CAAC,OAAO,IAAK;AACjB,YAAQ,IAAI,kEAAkE;AAC9E,WAAO,OAAO,IAAI,iBAAiB,mBAAmB,EAAE,OAAO,KAAK,QAAQ,IAAI,CAAC,EAC5E,MAAM,MAAM;AAAA,IAAC,CAAC;AAAA,EACvB;AAAA,EAEA,MAAM,mBAAmB;AACrB,QAAI,CAAC,OAAO,KAAK;AACb,aAAO,EAAE,SAAS,KAAK;AAAA,IAC3B;AAEA,QAAI;AACA,YAAM,cAAc,MAAM,OAAO,IAAI,iBAAiB,uBAAuB;AAC7E,cAAQ,IAAI,2CAA2C,WAAW;AAElE,UAAI,CAAC,YAAY,YAAY;AACzB,eAAO,EAAE,SAAS,KAAK;AAAA,MAC3B;AAEA,UAAI,eAAe;AACnB,UAAI,CAAC,YAAY,cAAc,CAAC,YAAY,QAAQ;AAChD,uBAAe;AAAA,MACnB;AAEA,aAAO;AAAA,QACH,SAAS;AAAA,QACT,OAAO;AAAA,MACX;AAAA,IACJ,SAAS,OAAO;AACZ,cAAQ,MAAM,kDAAkD,KAAK;AACrE,aAAO;AAAA,QACH,SAAS;AAAA,QACT,OAAO;AAAA,MACX;AAAA,IACJ;AAAA,EACJ;AACJ;AAEA,OAAO,iBAAiB,oBAAoB,MAAM;AAC9C,MAAI,wBAAwB;AAChC,CAAC;", "names": ["t", "e", "i", "s", "n", "o", "r", "l", "h", "a", "d", "c", "t", "e", "s", "i", "n", "o", "r", "t", "e", "s", "e", "k", "e", "p", "_", "m", "e", "e"]}